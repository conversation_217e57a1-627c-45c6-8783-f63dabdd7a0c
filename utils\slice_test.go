package utils

import "testing"

func TestSliceContains(t *testing.T) {
	type args struct {
		a []string
		x string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "包含",
			args: args{
				a: []string{"1", "2", "3", "4"},
				x: "4",
			},
			want: true,
		},
		{
			name: "不包含",
			args: args{
				a: []string{"1", "2", "3", "4"},
				x: "5",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SliceContains(tt.args.a, tt.args.x); got != tt.want {
				t.<PERSON>rf("SliceContains() = %v, want %v", got, tt.want)
			}
		})
	}
}
