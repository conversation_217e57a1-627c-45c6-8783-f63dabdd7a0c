package controller

import (
	"_/proto/et"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @Summary 查询子龙商品信息
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param type query string true "类型：0-条形码 1-商品名称 2-总部编码"
// @Param content query string true "搜索内容"
// @Param page_index query string false "分页"
// @Param page_size query string false "分页大小"
// @Success 200 {object} et.ZiLongProductListRes
// @Failure 400 {object} et.ZiLongProductListRes
// @Router /boss/product/zlproduct/list [get]
func QueryZlProducts(c echo.Context) error {
	out := &et.ZiLongProductListRes{Code: 400}
	searchType := cast.ToInt32(c.QueryParam("type"))
	content := c.QueryParam("content")
	page_index := cast.ToInt32(c.QueryParam("page_index"))
	page_size := cast.ToInt32(c.Query<PERSON>aram("page_size"))
	if page_size <= 0 {
		page_size = 10
	}
	if page_index <= 0 {
		page_index = 1
	}
	req := &et.ZiLongProductListReq{
		Size:   page_size,
		Number: page_index,
	}

	switch searchType {
	case 1:
		req.ProductName = content
	case 2:
		req.ProductCode = []string{content}
	default:
		req.BarCode = content
	}

	etClient := et.GetExternalClient()
	out, err := etClient.ZiLong.ProductList(etClient.Ctx, req)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else if out.Code != 200 {
		return c.JSON(400, out)
	}
	out.Code = 200
	return c.JSON(200, out)
}
