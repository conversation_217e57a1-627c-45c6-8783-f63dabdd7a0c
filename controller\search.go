package controller

import (
	"_/proto/ctc"
	"encoding/json"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"net/http"
	"regexp"
	"strings"
	"unicode/utf8"
)

// @summary 热词列表
// @tags 搜索词
// @Accept json
// @Produce json
// @Param model body ctc.HotWordRequest false " "
// @success 200 {object} ctc.WordResponse
// @failure 400 {object} ctc.WordResponse
// @router /boss/search/hot-word [GET]
func HotWord(c echo.Context) error {
	out := new(ctc.WordResponse)
	in := new(ctc.WordRequest)

	// 参数绑定
	in.Name = c.QueryParam("name")
	in.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	in.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	if in.PageSize < 1 || in.PageIndex < 1 {
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "分页参数错误"})
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	in.Type = 1
	in.OrgId = cast.ToInt32(c.Request().Header.Get("org_id"))
	gRes, err := client.RPC.WordList(client.Ctx, in)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}
	if gRes.Data == nil {
		gRes.Data = []*ctc.WordData{}
	}

	return c.JSON(http.StatusOK, gRes)
}

// @summary 热词新增、更新
// @tags 搜索词
// @Accept json
// @Produce json
// @Param model body []ctc.HotWordOperateRequest true " "
// @success 200 {object} ctc.ContentResponse
// @failure 400 {object} ctc.ContentResponse
// @router /boss/search/hot-word-operate [POST]
func HotWordOperate(c echo.Context) error {
	out := new(ctc.ContentResponse)
	var in []ctc.WordOperateRequest
	// 参数绑定
	if err := c.Bind(&in); err != nil {
		glog.Info("热词新增修改HotWordOperate绑定参数失败：" + err.Error())
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "参数获取失败"})
	}
	// 参数校验
	if len(in) > 10 {
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "一次最多10条"})
	}
	for k, word := range in {
		word.Name = strings.Trim(word.Name, " ")
		in[k].Name = word.Name
		if word.Name == "" {
			return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "热词必填"})
		}
		if strings.Contains(word.Name, " ") {
			return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "热词中间不能有空格"})
		}
		wLen := utf8.RuneCountInString(word.Name)
		if wLen > 12 || wLen < 1 {
			return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "热词必须是1-12个汉字"})
		}
		if match, err := regexp.MatchString("^[\u4E00-\u9FA5A-Za-z0-9]+$", word.Name); err != nil || !match {
			return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "存在特殊字符"})
		}
		if word.IsRecom != 1 && word.IsRecom != 2 {
			return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "is_recom值错误"})
		}
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	OrgId := cast.ToInt32(c.Request().Header.Get("org_id"))

	jsonBytes, _ := json.Marshal(in)
	gRes, err := client.RPC.WordOperate(client.Ctx, &ctc.JsonStrRequest{
		JsonByte: jsonBytes,
		Type:     1,
		OrgId:    OrgId,
	})
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}

	return c.JSON(http.StatusOK, gRes)
}

// @summary 热词删除
// @tags 搜索词
// @Accept json
// @Produce json
// @Param model body ctc.WordDeleteRequest true " "
// @success 200 {object} ctc.ContentResponse
// @failure 400 {object} ctc.ContentResponse
// @router /boss/search/hot-word-delete [POST]
func HotWordDelete(c echo.Context) error {
	out := new(ctc.ContentResponse)
	in := new(ctc.WordDeleteRequest)

	// 参数绑定
	if err := c.Bind(&in); err != nil {
		glog.Info("热词隐藏HotWordDelete绑定参数失败：" + err.Error())
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "参数获取失败"})
	}
	if in.Id < 1 {
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "id参数值错误"})
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()
	OrgId := cast.ToInt32(c.Request().Header.Get("org_id"))
	in.OrgId = OrgId
	in.Type = 1
	gRes, err := client.RPC.WordDelete(client.Ctx, in)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}

	return c.JSON(http.StatusOK, gRes)
}

// @summary 默认词列表
// @tags 搜索词
// @Accept json
// @Produce json
// @Param model body ctc.DefaultWordRequest false " "
// @success 200 {object} ctc.WordResponse
// @failure 400 {object} ctc.WordResponse
// @router /boss/search/default-word [GET]
func DefaultWord(c echo.Context) error {
	out := new(ctc.WordResponse)
	in := new(ctc.WordRequest)

	// 参数绑定
	in.Name = c.QueryParam("name")
	in.RealName = c.QueryParam("real_name")
	in.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	in.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	in.Channel = cast.ToInt32(c.QueryParam("channel"))
	if in.PageSize < 1 || in.PageIndex < 1 {
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "分页参数错误"})
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	in.Type = 2
	OrgId := cast.ToInt32(c.Request().Header.Get("org_id"))
	in.OrgId = OrgId
	gRes, err := client.RPC.WordList(client.Ctx, in)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}
	if gRes.Data == nil {
		gRes.Data = []*ctc.WordData{}
	}

	return c.JSON(http.StatusOK, gRes)
}

// @summary 默认词新增、更新
// @tags 搜索词
// @Accept json
// @Produce json
// @Param model body []ctc.DefaultWordOperateRequest true " "
// @success 200 {object} ctc.ContentResponse
// @failure 400 {object} ctc.ContentResponse
// @router /boss/search/default-word-operate [POST]
func DefaultWordOperate(c echo.Context) error {
	out := new(ctc.ContentResponse)
	var in []ctc.WordOperateRequest

	// 参数绑定
	if err := c.Bind(&in); err != nil {
		glog.Info("默认词新增修改DefaultWordOperate绑定参数失败：" + err.Error())
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "参数获取失败"})
	}

	// 参数校验
	if len(in) > 10 {
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "一次最多10条"})
	}
	for k, word := range in {
		word.Name = strings.Trim(word.Name, " ")
		in[k].Name = word.Name
		wLen := utf8.RuneCountInString(word.Name)
		if wLen > 12 || wLen < 1 {
			return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "默认词必须是1-12个汉字"})
		}
		word.RealName = strings.Trim(word.RealName, " ")
		in[k].RealName = word.RealName
		rwLen := utf8.RuneCountInString(word.RealName)
		if rwLen > 12 {
			return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "实际词必须是1-12个汉字"})
		}
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	jsonBytes, _ := json.Marshal(in)
	gRes, err := client.RPC.WordOperate(client.Ctx, &ctc.JsonStrRequest{
		JsonByte: jsonBytes,
		Type:     2,
	})
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}

	return c.JSON(http.StatusOK, gRes)
}

// @summary 默认词删除
// @tags 搜索词
// @Accept json
// @Produce json
// @Param model body ctc.WordDeleteRequest true " "
// @success 200 {object} ctc.ContentResponse
// @failure 400 {object} ctc.ContentResponse
// @router /boss/search/default-word-delete [post]
func DefaultWordDelete(c echo.Context) error {
	out := new(ctc.ContentResponse)
	in := new(ctc.WordDeleteRequest)

	// 参数绑定
	if err := c.Bind(&in); err != nil {
		glog.Info("默认词隐藏DefaultWordDelete绑定参数失败：" + err.Error())
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "参数获取失败"})
	}
	if in.Id < 1 {
		return c.JSON(200, ctc.ContentResponse{Code: 400, Message: "id参数值错误"})
	}

	// rpc请求
	client := ctc.GetContentCenterClient()
	defer client.Close()

	in.Type = 2
	gRes, err := client.RPC.WordDelete(client.Ctx, in)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if gRes.Code == 400 {
		return c.JSON(http.StatusBadRequest, gRes)
	}

	return c.JSON(http.StatusOK, gRes)
}

// SearchKeyword
// @summary 搜索词库列表
// @tags 搜索词
// @Accept json
// @Produce json
// @Param model query ctc.KeywordsRequest false " "
// @success 200 {object} ctc.KeywordsResponse
// @failure 400 {object} ctc.KeywordsResponse
// @router /boss/search/keyword [GET]
func SearchKeyword(c echo.Context) error {
	req := &ctc.KeywordsRequest{
		Name:      c.QueryParam("name"),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
	}
	client := ctc.GetContentCenterClient()
	if out, err := client.RPC.Keywords(client.Ctx, req); err == nil {
		return c.JSON(200, out)
	} else {
		return c.JSON(400, &ctc.KeywordsResponse{Code: 400, Message: out.Message})
	}
}

// SearchKeywordStore
// @Summary 添加搜索词库
// @Tags 搜索词
// @Accept json
// @Produce json
// @Param model body ctc.KeywordStoreRequest true " "
// @success 200 {object} ctc.ContentResponse
// @Failure 400 {object} ctc.ContentResponse
// @Router /boss/search/keyword [post]
func SearchKeywordStore(c echo.Context) error {
	req := &ctc.KeywordStoreRequest{}
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &ctc.ContentResponse{Code: 400, Message: err.Error()})
	}
	client := ctc.GetContentCenterClient()
	if out, err := client.RPC.KeywordStore(client.Ctx, req); err == nil {
		return c.JSON(200, out)
	} else {
		return c.JSON(400, &ctc.ContentResponse{Code: 400, Message: out.Message})
	}
}

// SearchKeywordDelete
// @Summary 搜索词库删除
// @Tags 搜索词
// @Accept json
// @Produce json
// @Param model query ctc.KeywordDeleteRequest true " "
// @Success 200 {object} ctc.ContentResponse
// @Failure 400 {object} ctc.ContentResponse
// @Router /boss/search/keyword [DELETE]
func SearchKeywordDelete(c echo.Context) error {
	req := &ctc.KeywordDeleteRequest{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	if req.Id < 1 {
		return c.JSON(400, &ctc.ContentResponse{Code: 400, Message: "参数错误"})
	}
	client := ctc.GetContentCenterClient()
	if out, err := client.RPC.KeywordDelete(client.Ctx, req); err == nil {
		return c.JSON(200, out)
	} else {
		return c.JSON(400, &ctc.ContentResponse{Code: 400, Message: out.Message})
	}
}
