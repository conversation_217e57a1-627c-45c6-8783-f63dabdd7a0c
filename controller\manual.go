package controller

import (
	"_/models"
	"_/params"
	"_/proto/ic"
	"_/utils"
	"context"
	"fmt"
	"strings"

	"google.golang.org/grpc/status"

	"github.com/tricobbler/echo-tool/validate"

	kit "github.com/tricobbler/rp-kit"

	"github.com/maybgit/glog"

	"github.com/labstack/echo/v4"
)

// ManualStock
// @summary 手动拉取  前置仓（虚拟仓）单仓多SKU同步库存
// @tags 单仓拉库存优化
// @accept json
// @produce json
// @param warehouse_code body string true "仓库编号"
// @param goods_code body string  true "a8货号,多个货号之间用英文逗号隔开"
// @success 200 {object} params.BaseResponseNew
// @failure 400 {object} params.BaseResponseNew
// @router /boss/product/operate/manual-stock [POST]
func ManualStock(c echo.Context) error {
	var (
		p          = new(params.ManualStockRequest)
		err        error
		response   params.BaseResponseNew
		rpcRequest = new(ic.RunStockByManualRequest)
	)
	//数据绑定
	if err = c.Bind(p); err != nil {
		glog.Error("boss-ManualStock-eva-错误=", err.Error())
		response.Msg = err.Error()
		return c.JSON(400, response)
	}
	logStr := "boss-ManualStock-eva-入参=" + kit.JsonEncode(p)
	glog.Info(logStr)
	//数据验证
	if err = c.Validate(p); err != nil {
		errList := validate.Translate(err)
		glog.Error(logStr, ", 错误=", errList.All())
		response.Msg = errList.One()
		return c.JSON(400, response)

	}
	//  如果goodsCode里包含 中文 逗号， 则报错
	if strings.Contains(p.GoodsCode, "，") {
		response.Msg = "货号包含中文逗号，请注意检查"
		return c.JSON(400, response)
	}

	goodCode := strings.Split(p.GoodsCode, ",")

	max := 100
	if len(goodCode) > max {
		response.Msg = fmt.Sprintf("最多可输入%d个货号", max)
		return c.JSON(400, response)
	}

	//组装数据
	rpcRequest.WarehouseCode = p.WarehouseCode
	rpcRequest.GoodsCode = p.GoodsCode
	client := ic.GetInventoryServiceClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	if _, err := client.RPC.RunStockByManual(ctx, rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, ",错误=", s.Message())
			response.Msg = s.Message()
			return c.JSON(400, response)
		}
	}
	return c.JSON(200, response)

}

// ManualStockRecordList
// @summary 手动拉取  前置仓（虚拟仓）单仓多SKU同步库存 记录列表
// @tags 单仓拉库存优化
// @accept json
// @produce json
// @param page_index body integer true "页码"
// @param page_size body integer  true "条数"
// @success 200 {object} params.ManualStockRecordListResponse
// @failure 400 {object} params.ManualStockRecordListResponse
// @router /boss/product/operate/manual-stock/record-list [GET]
func ManualStockRecordList(c echo.Context) error {
	var (
		p           params.ManualStockRecordListRequest
		err         error
		response    params.ManualStockRecordListResponse
		rpcRequest  = new(ic.ManualStockRecordListRequest)
		rpcResponse *ic.ManualStockRecordListResponse
	)
	response.Data = make([]*models.ManualStockRecord, 0)
	if err = c.Bind(&p); err != nil {
		response.Msg = err.Error()
		glog.Error("boss-ManualStockRecordList-eva-错误-bind=", err.Error())
		return c.JSON(400, response)
	}
	logStr := "boss-ManualStockRecordList-eva-入参=" + kit.JsonEncode(p)
	glog.Info(logStr)
	if err := c.Validate(&p); err != nil {
		errList := validate.Translate(err)
		glog.Error(logStr, ",错误-Validate=", errList.All())
		response.Msg = errList.One()
		return c.JSON(400, response)

	}

	//组装数据
	if err := utils.MapTo(&p, rpcRequest); err != nil {
		response.Msg = err.Error()
		glog.Error(logStr, ",錯誤-MapTo=", err.Error())
		return c.JSON(400, response)
	}

	client := ic.GetInventoryServiceClient()
	defer client.Close()

	if rpcResponse, err = client.RPC.ManualStockRecordList(client.Ctx, rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			response.Msg = s.Message()
			glog.Error(logStr, ",rpc,请求错误=", s.Message())
			return c.JSON(400, response)
		}
		glog.Error(logStr, ",rpc,请求错误2=", err.Error())
		return c.JSON(400, response)
	}
	if rpcResponse == nil {
		return c.JSON(200, response)
	}
	glog.Info(logStr, ",数据记录=", kit.JsonEncode(rpcResponse))
	response.Total = rpcResponse.Total
	if len(rpcResponse.Data) > 0 {
		if err = utils.MapTo(rpcResponse, &response); err != nil {
			response.Msg = err.Error()
			glog.Error(logStr, ",错误-MapTo=", err.Error())
			return c.JSON(400, response)
		}
	}

	return c.JSON(200, response)

}
