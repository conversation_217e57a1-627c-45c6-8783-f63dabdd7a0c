package controller

//拼团模块控制器

import (
	"_/dto"
	"_/models"
	"_/params"
	"_/proto/ac"
	"_/proto/ic"
	"_/utils"
	"bytes"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/tricobbler/echo-tool/validate"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
)

var x int

//---------------------------------拼团活动信息部分----------------------------

// GetGroupBuyList
// @Summary 获取拼团信息列表
// @Tags 拼团
// @Accept plain
// @Accept json
// @Produce json
// @Param channelId query int true "业务渠道 1阿文到家 5电商"
// @Param type query int true "拼团类型 1普通拼团"
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Param title query string false "活动名称"
// @Param status query int false "活动状态 0所有 1未开始 3 进行中 4 已结束 5 已终止 不传则默认查询所有"
// @Param beginTimeDuring query string false "开始时间区间 格式：2012-07-05 00：00：00 - 2012-07-05 00：00：00"
// @Param endTimeDuring query string false "结束时间区间 格式：2012-07-05 00：00：00 - 2012-07-05 00：00：00"
// @Param GetGroupBuyListRequest body params.GetGroupBuyListRequest true " "
// @Success 200 {object} params.GetGroupBuyListResponse
// @Failure 400 {object} params.GetGroupBuyListResponse
// @Router /boss/market/group-buy/list [GET]
func GetGroupBuyList(c echo.Context) error {
	var (
		out    params.GetGroupBuyListResponse //返回参数
		rpcRes *ac.GroupBuyListResponse       //rpc请求结果
		err    error
	)
	out.Code = http.StatusBadRequest
	request := new(params.GetGroupBuyListRequest)

	rpcRequestParam := new(ac.GroupBuyListRequest)

	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = utils.MapTo(request, rpcRequestParam); err != nil {
		glog.Error("GetGroupBuyList MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	//分页与时间区间查询参数转换
	rpcRequestParam.Pagination = &ac.Pagination{
		PageIndex: request.PageIndex,
		PageSize:  request.PageSize,
	}
	//此处必须使用new开辟存储空间与初始值，否则可能导致接受参数的服务读取字段报错
	rpcRequestParam.BeginTimeDuring = new(ac.DuringTime)
	if request.BeginTimeDuring != "" {
		beginTimeSlice := strings.Split(request.BeginTimeDuring, " - ")
		rpcRequestParam.BeginTimeDuring = &ac.DuringTime{
			BeginTime: beginTimeSlice[0],
			EndTime:   beginTimeSlice[1],
		}
	}

	rpcRequestParam.EndTimeDuring = new(ac.DuringTime)
	if request.EndTimeDuring != "" {
		endTimeSlice := strings.Split(request.EndTimeDuring, " - ")
		rpcRequestParam.EndTimeDuring = &ac.DuringTime{
			BeginTime: endTimeSlice[0],
			EndTime:   endTimeSlice[1],
		}
	}

	glog.Info("GetGroupBuyList rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.GB.GetGroupBuyList(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetGroupBuyList rpc error:", err)
		out.Message = "获取失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if rpcRes.Code != http.StatusOK {
		glog.Error("GetGroupBuyList rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Total = rpcRes.Total
	out.Code = http.StatusOK
	out.Message = "获取成功"

	if rpcRes.Total > 0 {
		for _, v := range rpcRes.Data {
			item := new(params.GroupBuyDataItem)
			err = utils.MapTo(v, item)
			if err != nil {
				glog.Error("GetGroupBuyList MapTo fail:", err.Error())
				out.Message = err.Error()
				return c.JSON(http.StatusBadRequest, out)
			}
			if v.AdditionPromotionType != "" {
				item.AdditionPromotionType = strings.Split(v.AdditionPromotionType, ",")
			}
			if v.UpdateTime == "0001-01-01 00:00:00" {
				item.UpdateTime = v.CreateTime
			}
			//是否可以被更新 状态为未开始
			if v.Status == 1 || v.Status == 2 {
				item.CanBeUpdated = 1
			}
			item.BeginEndDesc = v.BeginDate + " - " + v.EndDate
			item.Status, item.StatusDesc = groupBuyStatusAdaptor(v.Status, v.Stop)
			out.Data = append(out.Data, item)
		}
	}

	return c.JSON(http.StatusOK, out)
}

// GetGroupBuyDetail
// @Summary 拼团活动详细信息
// @Tags 拼团
// @Accept plain
// @Accept json
// @Produce json
// @Param id query int true "记录id"
// @Param IdRequest body params.IdRequest true " "
// @Success 200 {object} params.GetGroupBuyDetailResponse
// @Failure 400 {object} params.GetGroupBuyDetailResponse
// @Router /boss/market/group-buy/detail [GET]
func GetGroupBuyDetail(c echo.Context) error {
	var (
		out             params.GetGroupBuyDetailResponse //返回参数
		rpcRequestParam ac.GroupBuyIdRequest             //rpc请求详情的参数
		rpcRes          *ac.GroupBuyDetailResponse       //rpc获取详情的结果
		err             error
	)
	out.Code = http.StatusBadRequest
	request := new(params.IdRequest)
	outData := new(params.GroupBuyDataItem)

	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		errV := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = errV.One()
		return c.JSON(http.StatusBadRequest, out)
	}
	rpcRequestParam.Id = request.Id

	glog.Info("GetGroupBuyDetail rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.GB.GetGroupBuyDetail(client.Ctx, &rpcRequestParam); err != nil {
		glog.Error("GetGroupBuyDetail rpc error:", err)
		out.Message = "获取失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("GetGroupBuyDetail rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = utils.MapTo(rpcRes.GroupBuyData, outData); err != nil {
		glog.Error("GetGroupBuyDetail MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	//是否可以被更新 下面没有产品 且状态为未开始
	if rpcRes.GroupBuyData.ProductCount == 0 && rpcRes.GroupBuyData.Status == 1 && rpcRes.GroupBuyData.Stop == 0 {
		outData.CanBeUpdated = 1
	}

	if rpcRes.GroupBuyData.AdditionPromotionType != "" {
		outData.AdditionPromotionType = strings.Split(rpcRes.GroupBuyData.AdditionPromotionType, ",")
	}

	outData.ExpirationDay = int32(math.Floor(float64(rpcRes.GroupBuyData.ExpirationMinute) / 1440))
	outData.ExpirationHour = int32(math.Floor(float64(rpcRes.GroupBuyData.ExpirationMinute%1440) / 60))
	outData.ExpirationMinute = int32(math.Floor(float64(rpcRes.GroupBuyData.ExpirationMinute % 60)))

	outData.Status, outData.StatusDesc = groupBuyStatusAdaptor(rpcRes.GroupBuyData.Status, rpcRes.GroupBuyData.Stop)

	out.Code = http.StatusOK
	out.Message = "获取成功"
	out.Data = outData
	return c.JSON(http.StatusOK, out)
}

// CreateGroupBuy
// @Summary 创建拼团活动
// @Tags 拼团
// @Accept json
// @Produce json
// @Param SaveGroupBuyRequest body params.SaveGroupBuyRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/group-buy [POST]
func CreateGroupBuy(c echo.Context) error {
	var (
		out params.BaseResponse
	)
	out.Code = http.StatusBadRequest
	request := new(params.SaveGroupBuyRequest)
	//rpc请求参数
	rpcRequestParam := new(ac.GroupBuyCreateRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	///参数校验
	pass, validateMsg := checkSaveData(request, false)
	if pass == false {
		out.Message = validateMsg
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := utils.MapTo(request, rpcRequestParam); err != nil {
		glog.Error("CreateGroupBuy MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam.AdditionPromotionType = strings.Join(request.AdditionPromotionType, ",")
	rpcRequestParam.ExpirationMinute = (request.ExpirationDay * 24 * 60) + (request.ExpirationHour * 60) + request.ExpirationMinute

	glog.Info("CreateGroupBuy rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err := client.GB.CreateGroupBuy(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("CreateGroupBuy rpc error:", err)
		out.Message = rpcRes.Message
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("CreateGroupBuy rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	out.Code = http.StatusOK
	out.Message = "创建成功"
	return c.JSON(http.StatusOK, out)
}

// UpdateGroupBuy
// @Summary 修改拼团活动
// @Tags 拼团
// @Accept json
// @Produce json
// @Param SaveGroupBuyRequest body params.SaveGroupBuyRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/group-buy [PUT]
func UpdateGroupBuy(c echo.Context) error {
	var (
		out params.BaseResponse
	)

	out.Code = http.StatusBadRequest
	request := new(params.SaveGroupBuyRequest)
	rpcRequestParam := new(ac.GroupBuyUpdateRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}
	//参数校验
	pass, validateMsg := checkSaveData(request, true)
	if pass == false {
		out.Message = validateMsg
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := utils.MapTo(request, rpcRequestParam); err != nil {
		glog.Error("UpdateGroupBuy MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	//拼团叠加的活动类型 数组转字符
	rpcRequestParam.AdditionPromotionType = strings.Join(request.AdditionPromotionType, ",")
	rpcRequestParam.ExpirationMinute = (request.ExpirationDay * 24 * 60) + (request.ExpirationHour * 60) + request.ExpirationMinute

	glog.Info("UpdateGroupBuy rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err := client.GB.UpdateGroupBuy(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("UpdateGroupBuy rpc error:", err)
		out.Message = "更新失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("UpdateGroupBuy rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	out.Code = http.StatusOK
	out.Message = "修改成功"
	return c.JSON(http.StatusOK, out)
}

// StopGroupBuy
// @Summary 终止拼团活动
// @Tags 拼团
// @Accept json
// @Produce json
// @Param IdRequest body params.IdRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/group-buy/stop [PUT]
func StopGroupBuy(c echo.Context) error {
	var (
		out             params.BaseResponse
		rpcRequestParam ac.GroupBuyIdRequest
	)

	out.Code = http.StatusBadRequest
	request := new(params.IdRequest)
	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam.Id = request.Id

	glog.Info("StopGroupBuy rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err := client.GB.StopGroupBuy(client.Ctx, &rpcRequestParam); err != nil {
		glog.Error("StopGroupBuy rpc error:", err)
		out.Message = "终止失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("StopGroupBuy rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	out.Code = http.StatusOK
	out.Message = "终止成功"

	return c.JSON(http.StatusOK, out)
}

//---------------------------------拼团商品管理部分----------------------------

// GetGroupBuyProductList
// @Summary 获取拼团商品列表
// @Tags 拼团商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Param gid query string false "拼团活动id 多个活动用逗号隔开"
// @Param productName query string false "商品名称"
// @Param skuId query int false "商品sku id"
// @Param productId query int false "商品的产品id"
// @Param GetGroupBuyProductListRequest body params.GetGroupBuyProductListRequest true " "
// @Success 200 {object} params.GetGroupBuyProductListResponse
// @Failure 400 {object} params.GetGroupBuyProductListResponse
// @Router /boss/market/group-buy/product/list [GET]
func GetGroupBuyProductList(c echo.Context) error {
	var (
		out    params.GetGroupBuyProductListResponse
		err    error
		rpcRes *ac.GetGroupBuyProductListResponse
	)
	out.Code = http.StatusBadRequest
	//请求参数
	request := new(params.GetGroupBuyProductListRequest)

	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	//rpc请求参数
	rpcRequestParam := new(ac.GetGroupBuyProductListRequest)
	//分页与时间区间查询参数转换
	rpcRequestParam.Pagination = &ac.Pagination{
		PageIndex: request.PageIndex,
		PageSize:  request.PageSize,
	}
	var GIds []int32
	if request.Gid != "" {
		gidSlice := strings.Split(request.Gid, ",")
		for _, v := range gidSlice {
			GIds = append(GIds, cast.ToInt32(v))
		}
	}
	rpcRequestParam.Gid = GIds
	rpcRequestParam.ProductName = request.ProductName
	rpcRequestParam.SkuId = request.SkuId
	rpcRequestParam.ProductId = request.ProductId
	rpcRequestParam.ChannelId = ChannelMallId
	rpcRequestParam.Export = request.Export
	rpcRequestParam.Type = cast.ToInt32(c.QueryParam("type"))

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("GetGroupBuyProductList rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.GB.GetGroupBuyProductList(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetGroupBuyProductList rpc error:", err)
		out.Message = "获取失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if rpcRes.Code != http.StatusOK {
		glog.Error("GetGroupBuyProductList rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}
	groupBuy := new(models.GroupBuy)
	if _, err := GetDcActivityDBConn().Table("group_buy").In("id", GIds).Get(groupBuy); err != nil {
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		out.Status = groupBuy.Status
	}
	out.NormalNum = rpcRes.NormalNum
	out.NonormalNum = rpcRes.NonormalNum
	out.Total = rpcRes.Total
	out.Code = http.StatusOK
	out.Message = "获取成功"

	if request.Export == 1 {
		f := excelize.NewFile()
		writer, _ := f.NewStreamWriter("Sheet1")
		_ = writer.SetRow("A1", []interface{}{
			"sku", "售价", "拼团价", "采购价", "成团人数", "活动库存", "每单限购", "每个用户可开团最多次数", "每个用户可参团最多次数", "是否负毛利", "是否异常折扣",
		})
		for i := 0; i < len(rpcRes.Data); i++ {
			v := rpcRes.Data[i]
			//判断毛利
			var (
				flag     = "否"
				isNormal = "否"
			)
			R1PurchasePrice := int32(kit.YuanToFen(v.R1PurchasePrice))
			if v.Price < R1PurchasePrice {
				flag = "是"
			}
			if v.IsNormal == 1 {
				isNormal = "是"
			}
			writer.SetRow("A"+strconv.Itoa(i+2), []interface{}{
				v.SkuId,
				kit.FenToYuan(v.MarketPrice),
				kit.FenToYuan(v.Price),
				kit.FenToYuan(v.R1PurchasePrice),
				v.SuccessNum,
				v.Stock,
				v.BuyLimitNum,
				v.OpenNum,
				v.PartNum,
				flag,
				isNormal,
			})
		}
		var buff bytes.Buffer
		if err = f.Write(&buff); err != nil {
			return c.JSON(200, &dto.BaseRes{Code: 400, Message: "导出文件失败"})
		}
		fileName := "拼团活动商品列表导出 -" + time.Now().Format("20060102150405") + ".xlsx"
		c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
		return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
	}

	if rpcRes.Total > 0 {
		var queryStocks []*queryStock
		for _, v := range rpcRes.Data {
			if v.IsVirtual == 0 { //实物商品
				queryStocks = append(queryStocks, &queryStock{
					skuId:       v.SkuId,
					goodsType:   v.GoodsType,
					childSkuIds: v.ChildSkuIds,
				})
			}

			item := new(params.GroupBuyProductDataItem)
			//商品所属的活动还未开始时 商品可被编辑 否则不可被编辑

			err = utils.MapTo(v, item)

			if err != nil {
				glog.Error("GetGroupBuyProductList MapTo fail:", err.Error())
				out.Message = err.Error()
				return c.JSON(http.StatusBadRequest, out)
			}

			//活动未开始可以被编辑
			if v.Status == 1 || v.Status == 2 {
				item.CanBeEdited = 1
			}

			//当商品下没有订单的时候可以被删除
			if v.TotalOrderCount == 0 && (v.Stop != 1 || v.Status != 4) {
				item.CanBeDeleted = 1
			}
			item.IsNormal = v.IsNormal
			item.IsMark = v.IsMark
			out.Data = append(out.Data, item)
			//没有实物商品 直接返回
		}

		if len(queryStocks) == 0 {
			return c.JSON(http.StatusOK, out)
		}

		skuIdStockMap, msg, errGetStock := getPhysicalSkuStock(queryStocks)
		if errGetStock != nil {
			glog.Error("拼团GetGroupBuyProductList：getPhysicalSkuStock rpc error:", err)
			out.Error = err.Error()
			out.Message = msg
			return c.JSON(http.StatusBadRequest, out)
		}
		//没有查询出结果，直接返回
		if len(skuIdStockMap) == 0 {
			return c.JSON(http.StatusOK, out)
		}

		//如果查询出了库存 则遍历更新列表中商品的库存
		for _, val := range out.Data {
			if val.IsVirtual == 0 { //实物商品
				if stock, ok := skuIdStockMap[val.SkuId]; ok {
					val.Stock = stock
				}
			}
		}
	}

	return c.JSON(http.StatusOK, out)
}

// GetGroupBuyProductDetail
// @Summary 拼团活动商品详细信息
// @Tags 拼团商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param id query int true "记录id"
// @Param IdRequest body params.IdRequest true " "
// @Success 200 {object} params.GetGroupBuyProductDetailResponse
// @Failure 400 {object} params.GetGroupBuyProductDetailResponse
// @Router /boss/market/group-buy/product/detail [GET]
func GetGroupBuyProductDetail(c echo.Context) error {
	var (
		out    params.GetGroupBuyProductDetailResponse //返回参数
		err    error
		rpcRes *ac.GetGroupBuyProductDetailResponse //rpc返回结果
	)

	out.Code = http.StatusBadRequest
	request := new(params.IdRequest)

	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam := new(ac.GroupBuyProductDetailRequest)
	rpcRequestParam.Id = request.Id

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("GetGroupBuyProductDetail rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.GB.GetGroupBuyProductDetail(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetGroupBuyProductDetail rpc error:", err)
		out.Message = "更新失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("GetGroupBuyProductDetail rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	outData := new(params.GroupBuyProductDataItem)
	if err = utils.MapTo(rpcRes.Data, outData); err != nil {
		glog.Error("GetGroupBuyProductDetail MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	//查库存
	out.Code = http.StatusOK
	out.Data = outData

	if outData.IsVirtual == 0 { //实物商品
		skuIdStockMap, msg, errGetStock := getPhysicalSkuStock([]*queryStock{{
			skuId:       outData.SkuId,
			goodsType:   outData.GoodsType,
			childSkuIds: rpcRes.Data.ChildSkuIds,
		},
		})
		if errGetStock != nil {
			glog.Error("拼团GetGroupBuyProductDetail：getPhysicalSkuStock rpc error:", err)
			out.Error = err.Error()
			out.Message = msg
			out.Code = http.StatusBadRequest
			return c.JSON(http.StatusBadRequest, out)
		}
		//没有查询出结果，直接返回
		if len(skuIdStockMap) == 0 {
			return c.JSON(http.StatusOK, out)
		}

		//如果查询出了库存 则赋值
		if stock, ok := skuIdStockMap[outData.SkuId]; ok {
			outData.Stock = stock
		}
	}
	return c.JSON(http.StatusOK, out)
}

// CreateGroupBuyProduct
// @Summary 给拼团活动添加商品
// @Tags 拼团商品管理
// @Accept json
// @Produce json
// @Param SaveGroupBuyProductRequest body params.SaveGroupBuyProductRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/group-buy/product [POST]
func CreateGroupBuyProduct(c echo.Context) error {
	var (
		out params.BaseResponse
	)

	out.Code = http.StatusBadRequest
	request := new(params.SaveGroupBuyProductRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数校验
	pass, validateMsg := checkProductSaveData(request, false)
	if pass == false {
		out.Message = validateMsg
		return c.JSON(http.StatusBadRequest, out)
	}
	saveData := new(ac.SaveGroupBuyProductData)
	if err := utils.MapTo(request, saveData); err != nil {
		glog.Error("CreateGroupBuy MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数转换
	rpcRequestParam := new(ac.CreateGroupBuyProductRequest)
	rpcRequestParam.SkuId = request.SkuId
	rpcRequestParam.ProductId = request.ProductId
	rpcRequestParam.SaveData = saveData

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("CreateGroupBuyProduct rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err := client.GB.CreateGroupBuyProduct(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("CreateGroupBuyProduct rpc error:", err)
		out.Message = "创建失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("CreateGroupBuyProduct rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	//服务端需要验证 price sku coupon
	out.Code = http.StatusOK
	out.Message = "创建成功"
	return c.JSON(http.StatusOK, out)
}

// UpdateGroupBuyProduct
// @Summary 修改购活动商品信息
// @Tags 拼团商品管理
// @Accept json
// @Produce json
// @Param SaveGroupBuyProductRequest body params.SaveGroupBuyProductRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/group-buy/product [PUT]
func UpdateGroupBuyProduct(c echo.Context) error {
	var (
		out params.BaseResponse
	)

	out.Code = http.StatusBadRequest
	request := new(params.SaveGroupBuyProductRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数校验
	pass, validateMsg := checkProductSaveData(request, true)
	if pass == false {
		out.Message = validateMsg
		return c.JSON(http.StatusBadRequest, out)
	}

	saveData := new(ac.SaveGroupBuyProductData)
	if err := utils.MapTo(request, saveData); err != nil {
		glog.Error("UpdateGroupBuyProduct MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	//参数转换
	rpcRequestParam := new(ac.UpdateGroupBuyProductRequest)
	rpcRequestParam.Id = request.Id
	rpcRequestParam.SkuId = request.SkuId
	rpcRequestParam.ProductId = request.ProductId
	rpcRequestParam.SaveData = saveData

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("UpdateGroupBuyProduct rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err := client.GB.UpdateGroupBuyProduct(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("UpdateGroupBuyProduct rpc error:", err)
		out.Message = "更新失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("UpdateGroupBuyProduct rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	out.Code = http.StatusOK
	out.Message = "修改成功"

	return c.JSON(http.StatusOK, out)
}

// DeleteGroupBuyProduct
// @Summary 删除拼团活动商品信息
// @Tags 拼团商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param id query int true "记录id"
// @Param IdRequest body params.IdRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/group-buy/product [DELETE]
func DeleteGroupBuyProduct(c echo.Context) error {
	var (
		out params.BaseResponse
	)

	out.Code = http.StatusBadRequest
	request := new(params.IdRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam := new(ac.GroupBuyProductIdRequest)
	rpcRequestParam.Id = request.Id
	//rpc请求
	client := ac.GetActivityCenterClient()
	glog.Info("DeleteGroupBuyProduct rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	if rpcRes, err := client.GB.DeleteGroupBuyProduct(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("DeleteGroupBuyProduct rpc error:", err, "rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
		out.Message = "删除失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("DeleteGroupBuyProduct rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	out.Code = http.StatusOK
	out.Message = "删除成功"
	return c.JSON(http.StatusOK, out)
}

// GetGroupBuyUPetProductSelectList
// @Summary 获取可以参加拼团活动的阿闻商城的商品
// @Tags 拼团商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Param gid query int true "拼团活动id"
// @Param productName query string false "商品名称"
// @Param skuId query int false "商品sku id"
// @Param productId query int false "商品的产品id"
// @Param GetGroupBuyUPetProductSelectListRequest body params.GetGroupBuyUPetProductSelectListRequest true " "
// @Success 200 {object} params.GetGroupBuyUPetProductSelectListResponse
// @Failure 400 {object} params.GetGroupBuyUPetProductSelectListResponse
// @Router /boss/market/group-buy/product/select-list [GET]
func GetGroupBuyUPetProductSelectList(c echo.Context) error {
	var (
		out    params.GetGroupBuyUPetProductSelectListResponse //返回参数
		err    error
		rpcRes *ac.GetGroupBuyUPetProductSelectListResponse //RPC请求返回结果
	)

	out.Code = http.StatusBadRequest
	request := new(params.GetGroupBuyUPetProductSelectListRequest)
	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	rpcRequestParam := new(ac.GetGroupBuyUPetProductSelectListRequest)

	//分页与时间区间查询参数转换
	rpcRequestParam.Pagination = &ac.Pagination{
		PageIndex: request.PageIndex,
		PageSize:  request.PageSize,
	}
	rpcRequestParam.Gid = request.Gid
	rpcRequestParam.ProductName = request.ProductName
	rpcRequestParam.SkuId = request.SkuId
	rpcRequestParam.ProductId = request.ProductId
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("GetGroupBuyUPetProductSelectList rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.GB.GetGroupBuyUPetProductSelectList(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetGroupBuyUPetProductSelectList rpc error:", err)
		out.Message = "获取失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if rpcRes.Code != http.StatusOK {
		glog.Error("GetGroupBuyUPetProductSelectList rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Total = rpcRes.Total
	out.Code = http.StatusOK
	out.Message = "获取成功"

	if rpcRes.Total > 0 {
		var queryStocks []*queryStock //需要查询库存的实物skuId
		for _, v := range rpcRes.Data {
			if v.IsVirtual == 0 { //实物商品
				queryStocks = append(queryStocks, &queryStock{
					skuId:       v.SkuId,
					goodsType:   v.GoodsType,
					childSkuIds: v.ChildSkuIds,
				})
			}
			item := new(params.GroupBuySelectUPetProductData)
			err = utils.MapTo(v, item)
			if err != nil {
				glog.Error("GetGroupBuyUPetProductSelectList MapTo fail:", err.Error())
				out.Message = err.Error()
				return c.JSON(http.StatusBadRequest, out)
			}
			out.Data = append(out.Data, item)
		}
		//没有实物商品 直接返回
		if len(queryStocks) == 0 {
			return c.JSON(http.StatusOK, out)
		}
		skuIdStockMap, msg, errGetStock := getPhysicalSkuStock(queryStocks)
		if errGetStock != nil {
			glog.Error("GetGroupBuyUPetProductSelectList：getPhysicalSkuStock error:", err)
			out.Error = err.Error()
			out.Message = msg
			return c.JSON(http.StatusBadRequest, out)
		}
		//没有查询出结果，直接返回
		if len(skuIdStockMap) == 0 {
			return c.JSON(http.StatusOK, out)
		}

		//如果查询出了库存 则遍历更新列表中商品的库存
		for _, v := range out.Data {
			if v.IsVirtual == 0 { //实物商品
				if stock, ok := skuIdStockMap[v.SkuId]; ok {
					v.Stock = stock
				}
			}
		}
	}
	return c.JSON(http.StatusOK, out)
}

// 创建与新增拼团活动参数检测与数据处理
func checkSaveData(p *params.SaveGroupBuyRequest, isUpdate bool) (res bool, msg string) {
	if isUpdate == true {
		if p.Id == 0 {
			msg = "修改活动时，活动id为必传"
			return
		}
	}

	//活动名称最长12个字符
	//考虑中文以及特殊字符 需要转换成rune再计长度
	runeTitle := []rune(p.Title)
	if len(runeTitle) > 12 {
		msg = "活动名称不能超过12个字符"
		return
	}

	//结束时间 必须 大于开始时间
	intBeginDate := utils.StrTimeToUnixTime(p.BeginDate, kit.DATETIME_LAYOUT)
	intEndDate := utils.StrTimeToUnixTime(p.EndDate, kit.DATETIME_LAYOUT)
	if intBeginDate >= intEndDate {
		msg = "结束时间必须大于等于开始时间"
		return
	}

	//拼团有效期必须在15分钟到30天之间
	ExpirationSeconds := (p.ExpirationDay * 86400) + (p.ExpirationHour * 3600) + (p.ExpirationMinute * 60)
	if ExpirationSeconds < 15*60 {
		msg = "拼团有效期大于等于15分钟"
		return
	}
	if ExpirationSeconds > 30*86400 {
		msg = "拼团有效期小于等于30天"
		return
	}

	//开启了预告却没有设置预告时间
	if p.PreviewSwitch == 1 && p.PreviewHour == 0 {
		msg = "开启预告的情况下，预告提前时间必须大于等于1小时"
		return
	}
	//开启了模拟成团却没有设置模拟成团适用对象
	if p.MockSuccess == 1 && p.MockSuccessTarget == 0 {
		msg = "开启模拟成团的情况下，必须选择模拟成团适用对象"
		return
	}
	//开启了模拟成团却没有设置模拟成团适用对象
	if p.MockSuccess == 1 && p.MockSuccessMember == 0 {
		msg = "模拟成团最低参团人数必须>=1"
		return
	}
	if p.AdditionPromotionAble == 1 && len(p.AdditionPromotionType) > 0 {
		additionPromotionTypeMap := map[string]string{
			"1": "优惠券",
			"2": "现时折扣",
			"3": "店铺满减",
			"4": "满减运费",
		}
		for _, v := range p.AdditionPromotionType {
			if _, ok := additionPromotionTypeMap[v]; !ok {
				msg = "叠加的活动类型值错误 类型只能在 1，2，3，4中"
				return
			}
		}
	}
	return true, ""
}

// 创建与新增拼团商品参数检测与数据处理
func checkProductSaveData(p *params.SaveGroupBuyProductRequest, isUpdate bool) (res bool, msg string) {
	if isUpdate == true {
		if p.Id == 0 {
			msg = "修改时，id为必传"
			return
		}
	}
	//限购类型 默认为1
	if p.BuyLimitType == 0 {
		p.BuyLimitType = 1
	}
	//如果传过来的是0 则一定是因为前端没有填写，此时需要默认值，因为前端填写0时会传过来-1
	//0 没填写默认为1  -1时为不限制 数据库里存0 所以 这两种情况最终的值是原始值+1
	if p.SuccessCouponLimitNum == 0 || p.SuccessCouponLimitNum == -1 {
		p.SuccessCouponLimitNum += 1
	}
	if p.FailCouponLimitNum == 0 || p.FailCouponLimitNum == -1 {
		p.FailCouponLimitNum += 1
	}
	return true, ""
}

type queryStock struct {
	skuId       int32          //商品的skuId
	goodsType   int32          //商品类型
	childSkuIds []*ac.ChildRen //活动中心返回的子商品数据
}

// 获取实物商品的库存
func getPhysicalSkuStock(skuIds []*queryStock) (map[int32]int32, string, error) {
	//now := time.Now()
	var (
		rpcRes *ic.GetStockInfoResponse
		err    error
	)
	skuIdStockMap := make(map[int32]int32)

	clientIc := ic.GetInventoryServiceClient()
	defer clientIc.Close()
	rpcRequestParam := new(ic.GetStockInfoRequest)
	rpcRequestParam.Source = 1 //拼团商品目前暂时为电商的商品

	var ProductLIst = make([]*ic.ProductsInfo, len(skuIds))

	for i, v := range skuIds {
		if v.goodsType == 0 { //普通非组合商品实物
			ProductLIst[i] = &ic.ProductsInfo{
				SkuId: v.skuId,
				Type:  2,
			}
		} else if v.goodsType == 1 || v.goodsType == 3 { //实实组合、虚实组合商品 如果增加阿闻本地的商品，需要验证数据源对组合商品的标识
			ProductLIst[i] = &ic.ProductsInfo{
				SkuId: v.skuId,
				Type:  1,
			}
			if len(v.childSkuIds) == 0 {
				continue
			}
			var childRen []*ic.ChildRen
			for _, skuInfo := range v.childSkuIds {
				childRen = append(childRen, &ic.ChildRen{
					SkuId:     skuInfo.SkuId,
					RuleNum:   skuInfo.RuleNum,
					IsVirtual: skuInfo.IsVirtual,
				})
			}
			//活动中心子商品数据转换为查库存时需要的子商品数据
			ProductLIst[i].ChildRen = childRen
		}
	}
	rpcRequestParam.ProductsInfo = ProductLIst
	glog.Info("GroupBuy GetStockInfo rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	if rpcRes, err = clientIc.RPC.GetStockInfo(clientIc.Ctx, rpcRequestParam); err != nil {
		glog.Error("GroupBuy GetStockInfo rpc err:", err.Error())
		return skuIdStockMap, "获取实物库存出错", err
	}
	if rpcRes.Code != http.StatusOK {
		glog.Info("拼团:getPhysicalSkuStock rpc fail:", rpcRes.Message)
		return skuIdStockMap, rpcRes.Message, nil
	}
	//返回map数据

	for _, v := range rpcRes.GoodsInfo.ProductsInfo {
		skuIdStockMap[v.SkuId] = v.Stock
	}
	/*end := time.Since(now)
	fmt.Println(end)*/
	return skuIdStockMap, "", nil

}

// 拼团活动前后端状态适配
// 前端的状态主要用于搜索与显示，后端的状态主要用于数据逻辑，需要做一层转换
// 输入后端状态 返回前端状态 目前boss后台没有用到预告中于显示中的前端状态，不做转换
// +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//
//	状态	|	未开始	 |	预告中	|	显示中				| 进行中  	| 已结束	   | 已终止
//
// +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//
//	前端状态	|	status=1 | status=2 |  status=3			    | status=4  | status=5 | status=6
//
// +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
//
//	后端状态	|	status=1 | status=2 | status=2 OR status=3  | status=3  | status=4 | status=4 AND stop=1
//
// +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
func groupBuyStatusAdaptor(status int32, stop int32) (int32, string) {
	if status == -3 {
		return -3, "待提交"
	}
	if status == -2 {
		return -2, "待审核"
	}
	if status == 1 || status == 2 {
		return 1, "未开始"
	}
	if status == 3 {
		return 4, "进行中"
	}
	if status == 4 {
		if stop == 0 {
			return 5, "已结束"
		} else if stop == 1 {
			return 6, "已终止"
		}
	}
	return 0, "未知"
}

// BookmarkGroupBuyProduct
// @Summary 标识异常商品
// @Tags 拼团
// @Accept json
// @Produce json
// @Param BaseRequest body params.BaseRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/group-buy/bookmark [POST]
func BookmarkGroupBuyProduct(c echo.Context) error {
	out := &params.BaseResponse{Code: 400}
	in := new(params.BaseRequest)
	if err := c.Bind(in); err != nil {
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if err := c.Validate(in); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	if in.Id > 0 {
		in.Ids = append(in.Ids, in.Id)
	}
	if len(in.Ids) == 0 {
		out.Message = "Id不能为空"
		return c.JSON(http.StatusBadRequest, out)
	}

	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		out.Error = err.Error()
		out.Message = "获取操作人信息失败!"
		return c.JSON(http.StatusBadRequest, out)
	}

	var groupBuyProducts []*models.GroupBuyProduct
	if err = GetDcActivityDBConn().Table("group_buy_product").In("id", in.Ids).Find(&groupBuyProducts); err != nil {
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else if len(groupBuyProducts) == 0 {
		out.Message = "活动商品不存在"
		return c.JSON(http.StatusBadRequest, out)
	}

	for _, groupBuyProduct := range groupBuyProducts {
		//判断异常类型
		bookmarkData := params.BookmarkData{}
		if in.Type == 1 {
			//判断异常折扣
			redisKey := fmt.Sprintf(DiscountSettingKey, GroupType)
			Redis := GetRedisConn()
			discount := Redis.Get(redisKey).Val()
			bookmarkData.MarkDiscount = cast.ToFloat64(discount)
			//判断异常采购价
			sku := &params.SkuData{}
			has, err := GetDcProductDBConn().Table("sku").Where("id = ?", groupBuyProduct.SkuId).Get(sku)
			if err != nil {
				out.Error = err.Error()
				return c.JSON(http.StatusBadRequest, out)
			}
			if has {
				bookmarkData.MarkPurchasePrice = sku.R1PurchasePrice
			}
		} else {
			bookmarkData.MarkDiscount = 0
			bookmarkData.MarkPurchasePrice = 0
		}
		_, err = GetDcActivityDBConn().Table("group_buy_product").Cols("mark_discount,mark_purchase_price").Where("id=?", groupBuyProduct.Id).Update(&bookmarkData)
		if err != nil {
			out.Message = err.Error()
			return c.JSON(400, out)
		}
		//添加操作人信息
		go func() {
			oldData, _ := json.Marshal(groupBuyProduct)
			newDate, _ := json.Marshal(bookmarkData)
			AddActivityOptLog(in.Id, models.GroupType, models.BookmarkLogType, string(oldData), string(newDate), "", userInfo.UserNo, userInfo.UserName)
		}()
	}

	out.Code = http.StatusOK
	out.Message = "操作成功"
	return c.JSON(http.StatusOK, out)
}
