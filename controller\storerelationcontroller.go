package controller

import (
	"_/proto/dac"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// @Summary 获取门店分页数据 v6.13
// @Tags 门店对应关系
// @Accept json
// @Produce json
// @Param model body dac.StoreListRequest true " "
// @Success 200 {object} dac.StoreListResponse
// @Failure 400 {object} dac.StoreListResponse
// @Router /boss/storerelationcontroller/StoreList [POST]
func StoreList(c echo.Context) error {

	glog.Info("获取门店分页数据")
	model := new(dac.StoreListRequest)
	var res dac.StoreListResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	laims, err := utils.ParseAndGetPayload(model.Token)
	model.UserCode = fmt.Sprintf("%s", laims["userno"])
	client := GetDataCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	grpcRes, err := client.RPC.StoreList(client.Ctx, model)
	if err != nil || grpcRes.Code != 200 {

		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("获取门店分页数据:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 修改或者新增对应关系
// @Tags 门店对应关系
// @Accept json
// @Produce json
// @Param model body dac.StoreToShopRequest true " "
// @Success 200 {object} dac.StoreToShopResponse
// @Failure 400 {object} dac.StoreToShopResponse
// @Router /boss/storerelationcontroller/BindStoreRelation [POST]
func BindStoreRelation(c echo.Context) error {

	glog.Info("保存或者新增对应关系")
	model := new(dac.StoreToShopRequest)
	var res dac.StoreToShopResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	grpcRes, err := client.RPC.ShopTozilong(context.Background(), model)
	if err != nil || grpcRes.Code != 200 {

		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("保存或者新增对应关系:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 创建配送门店
// @Tags 门店信息
// @Accept json
// @Produce json
// @Param model body dac.StoreToShopRequest true " "
// @Success 200 {object} dac.StoreToShopResponse
// @Failure 400 {object} dac.StoreToShopResponse
// @Router /boss/storerelationcontroller/CreateStore [POST]
func CreateStore(c echo.Context) error {

	glog.Info("创建配送门店")
	model := new(dac.StoreToShopRequest)
	var res dac.StoreToShopResponse
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	storeMasterId, err := GetAppChannelByFinanceCode(model.FinanceCode)
	if err != nil {
		glog.Error("CreateStore,", "GetAppChannelByFinanceCode,", model.FinanceCode, err)
		return c.JSON(http.StatusBadRequest, dac.StoreToShopResponse{
			Message: "获取店铺主体失败！",
		})
	}
	model.StoreMasterId = storeMasterId

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()
	grpcRes, err := client.RPC.CreateShop(context.Background(), model)
	if err != nil || grpcRes.Code != 200 {

		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("创建配送门店:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}
