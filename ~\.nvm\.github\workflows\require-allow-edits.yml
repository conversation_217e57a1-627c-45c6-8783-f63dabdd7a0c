name: Require “Allow Edits”

on: [pull_request_target]

permissions:
  contents: read

jobs:
  _:
    permissions:
      pull-requests: read
    name: "Require “Allow Edits”"

    runs-on: ubuntu-latest

    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@v2
      with:
        allowed-endpoints:
          api.github.com:443
    - uses: ljharb/require-allow-edits@main
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
