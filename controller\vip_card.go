package controller

import (
	"_/dto"
	"_/models"
	"_/proto/ac"
	"_/proto/cc"
	"_/proto/dac"
	"_/proto/mk"
	"_/proto/oc"
	"_/proto/pc"
	"_/utils"
	"bytes"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/gommon/log"

	"github.com/maybgit/glog"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/xuri/excelize/v2"
	"google.golang.org/grpc/status"
)

// @summary 付费会员卡模板
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.VipCardTemplateListRequest true " "
// @Success 200 {object} cc.VipCardTemplateListResponse
// @Failure 400 {object} cc.VipCardTemplateListResponse
// @Router /boss/vip/template/list [GET]
func GetVipCardTemplateList(c echo.Context) error {
	req := &cc.VipCardTemplateListRequest{
		PageIndex: cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:  cast.ToInt32(c.QueryParam("pageSize")),
		Type:      cast.ToInt32(c.QueryParam("type")),
		OrId:      cast.ToInt32(c.QueryParam("or_id")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.GetVipCardTemplateList(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.VipCardTemplateListResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 付费会员卡模板详情
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.BaseIdRequest true " "
// @Success 200 {object} cc.VipCardTemplateDetailResponse
// @Failure 400 {object} cc.VipCardTemplateDetailResponse
// @Router /boss/vip/template/detail [GET]
func GetVipCardTemplateDetail(c echo.Context) error {
	req := &cc.BaseIdRequest{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.GetVipCardTemplateDetail(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.VipCardTemplateDetailResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 付费会员卡模板添加
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model body cc.VipCardTemplateAddRequest true " "
// @Success 200 {object} cc.VcBaseResponse
// @Failure 400 {object} cc.VcBaseResponse
// @Router /boss/vip/template [POST]
func AddVipCardTemplate(c echo.Context) error {
	req := new(cc.VipCardTemplateAddRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.AddVipCardTemplate(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 付费会员卡模板编辑
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model body cc.VipCardTemplateUpdateRequest true " "
// @Success 200 {object} cc.VcBaseResponse
// @Failure 400 {object} cc.VcBaseResponse
// @Router /boss/vip/template/store [POST]
func UpdateVipCardTemplate(c echo.Context) error {
	req := new(cc.VipCardTemplateUpdateRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.UpdateVipCardTemplate(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 权益类型下拉列表
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query dto.HttpJson true " "
// @Success 200 {object} dto.CommonHttpResponse
// @Failure 400 {object} dto.CommonHttpResponse
// @Router /boss/vip/equity-type/list [GET]
func EquityTypeList(c echo.Context) error {
	out := &dto.CommonHttpResponse{Code: 200}

	type Equity struct {
		Label string `json:"label"`
		Value int32  `json:"value"`
	}

	out.Data = []Equity{
		{
			Label: "商城优惠券",
			Value: 1,
		},
		{
			Label: "子龙门店券",
			Value: 2,
		},
		//{
		//	Label: "积分翻倍",
		//	Value: 3,
		//},
		{
			Label: "会员价商品",
			Value: 4,
		},
		{
			Label: "大牌礼包",
			Value: 5,
		},
		{
			Label: "家庭服务包",
			Value: 6,
		},
		{
			Label: "宠物医保链接",
			Value: 7,
		},
		{
			Label: "子龙打折卡",
			Value: 8,
		},
		{
			Label: "会员特价商品",
			Value: 9,
		},
		{
			Label: "医保价商品",
			Value: 10,
		},
	}

	return c.JSON(200, out)
}

// @summary 卡周期下拉列表
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query dto.BaseReq true " "
// @Success 200 {object} dto.CommonHttpResponse
// @Failure 400 {object} dto.CommonHttpResponse
// @Router /boss/vip/card-type/list [GET]
func CardCycleTypeList(c echo.Context) error {
	out := &dto.CommonHttpResponse{Code: 200}
	req := &dto.BaseReq{
		OrId:    cast.ToInt32(c.QueryParam("or_id")),
		CardTid: cast.ToInt32(c.QueryParam("card_tid")),
	}
	type Equity struct {
		CycleName string `json:"cycle_name"`
		Id        int32  `json:"id"` //卡id
	}
	type vipCardMap struct {
		Id        int32 `json:"id"` //卡id
		CardCycle int32 `json:"card_cycle"`
	}
	cycleTypeMap := map[int32]string{
		1: "年卡",
		2: "季卡",
		3: "月卡",
		4: "周卡",
		5: "日卡",
	}
	db := GetDatacenterDBConn()
	if req.CardTid > 0 { //权益配置编辑返回周期
		var list vipCardMap
		if _, err := db.SQL("select id,card_cycle from vip_card_template where id = ?", req.CardTid).Get(&list); err != nil {
			out.Message = err.Error()
			return c.JSON(400, out)
		}
		if list.Id > 0 {
			var resp []Equity
			var res = Equity{
				Id:        list.Id,
				CycleName: cycleTypeMap[list.CardCycle],
			}
			resp = append(resp, res)
			out.Data = resp
		}
	} else { //权益配置添加时，返回可选周期
		var list []*vipCardMap
		var resp []Equity
		if err := db.SQL("select t.id,t.card_cycle from vip_card_template as t left join vip_card_equity_config "+
			"as e on t.id = e.card_tid and t.or_id = e.or_id where t.or_id = ? and t.type = 1 and e.card_tid is null; ", req.OrId).Find(&list); err != nil {
			out.Message = err.Error()
			return c.JSON(400, out)
		} else {
			for i := range list {
				date := list[i]
				var res = Equity{
					Id:        date.Id,
					CycleName: cycleTypeMap[date.CardCycle],
				}
				resp = append(resp, res)
			}
			out.Data = resp
		}
	}
	return c.JSON(200, out)
}

// @summary 付费会员卡权益列表
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.ListPageRequest true " "
// @Success 200 {object} cc.ListVipEquityResponse
// @Failure 400 {object} cc.ListVipEquityResponse
// @Router /boss/vip/equity/list [GET]
func VipEquityList(c echo.Context) error {
	req := &cc.ListPageRequest{
		PageIndex: cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:  cast.ToInt32(c.QueryParam("pageSize")),
		OrId:      cast.ToInt32(c.QueryParam("or_id")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.ListVipEquity(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.ListVipEquityResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 付费会员卡权益详情
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.BaseIdRequest true " "
// @Success 200 {object} cc.GetVipEquityResponse
// @Failure 400 {object} cc.GetVipEquityResponse
// @Router /boss/vip/equity/detail [GET]
func GetVipEquity(c echo.Context) error {
	req := &cc.BaseIdRequest{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.GetVipEquity(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.GetVipEquityResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 付费会员卡权益新增/更新
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model body cc.VipEquity true " "
// @Success 200 {object} cc.VcBaseResponse
// @Failure 400 {object} cc.VcBaseResponse
// @Router /boss/vip/equity/store [POST]
func CreateOrUpdateVipEquity(c echo.Context) error {
	req := new(cc.VipEquity)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.CreateOrUpdateVipEquity(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 权益配置列表
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.ListPageRequest true " "
// @Success 200 {object} cc.ListEquityConfigsResponse
// @Failure 400 {object} cc.ListEquityConfigsResponse
// @Router /boss/vip/equity-config/list [GET]
func EquityConfigsList(c echo.Context) error {
	req := &cc.ListPageRequest{
		PageIndex: cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:  cast.ToInt32(c.QueryParam("pageSize")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.ListEquityConfigs(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.ListEquityConfigsResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 不可退规则列表
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.ListPageRequest true " "
// @Success 200 {object} cc.ListNoRefundableEquityConfigsResponse
// @Failure 400 {object} cc.ListNoRefundableEquityConfigsResponse
// @Router /boss/vip/equity-config/list-non-refundable [GET]
func EquityConfigsListNoRefundable(c echo.Context) error {
	req := &cc.ListPageRequest{
		PageIndex: cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:  cast.ToInt32(c.QueryParam("pageSize")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.ListNoRefundableEquityConfig(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.ListNoRefundableEquityConfigsResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 权益配置详情
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.GetEquityConfigRequest true " "
// @Success 200 {object} cc.GetEquityConfigResponse
// @Failure 400 {object} cc.GetEquityConfigResponse
// @Router /boss/vip/equity-config/detail [GET]
func EquityConfigDetail(c echo.Context) error {
	req := &cc.GetEquityConfigRequest{
		CardTid: cast.ToInt32(c.QueryParam("card_tid")),
		OrId:    cast.ToInt64(c.QueryParam("or_id")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.GetEquityConfig(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.GetEquityConfigResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 权益配置新增/更新
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model body cc.CreateEquityConfigRequest true " "
// @Success 200 {object} cc.VcBaseResponse
// @Failure 400 {object} cc.VcBaseResponse
// @Router /boss/vip/equity-config/store [POST]
func SaveEquityConfig(c echo.Context) error {
	req := new(cc.CreateEquityConfigRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	req.UserId = userInfo.UserNo
	req.UserName = userInfo.UserName

	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.SaveEquityConfig(client.Ctx, req); err != nil {
		if status, ok := status.FromError(err); ok {
			return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: status.Message()})
		}
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	} else {
		GetDatacenterDBConn().Insert()
		return c.JSON(int(out.Code), out)
	}
}

// @summary 开卡礼包列表
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.GiftListRequest true " "
// @Success 200 {object} cc.GetGiftListResponse
// @Failure 400 {object} cc.GetGiftListResponse
// @Router /boss/vip/gift/list [GET]
func GetGiftList(c echo.Context) error {
	req := &cc.GiftListRequest{
		PageIndex: cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:  cast.ToInt32(c.QueryParam("pageSize")),
		Id:        cast.ToInt32(c.QueryParam("id")),
		State:     cast.ToInt32(c.QueryParam("state")),
		IsMain:    cast.ToInt32(c.QueryParam("is_main")),
		PackName:  c.QueryParam("pack_name"),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.GetGiftList(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.GetGiftListResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 开卡礼包详情
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.BaseIdRequest true " "
// @Success 200 {object} cc.GetGiftResponse
// @Failure 400 {object} cc.GetGiftResponse
// @Router /boss/vip/gift/detail [GET]
func GetGift(c echo.Context) error {
	req := &cc.BaseIdRequest{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.GetGift(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.GetGiftResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 开卡礼包新增/更新
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model body cc.GiftData true " "
// @Success 200 {object} cc.VcBaseResponse
// @Failure 400 {object} cc.VcBaseResponse
// @Router /boss/vip/gift/store [POST]
func SaveGift(c echo.Context) error {
	req := new(cc.GiftData)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	req.UserNo = userInfo.UserNo
	req.UserName = userInfo.UserName
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	if out, err := client.VipCard.SaveGift(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 开卡礼包详情
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.BaseIdRequest true " "
// @Success 200 {object} cc.GetGiftResponse
// @Failure 400 {object} cc.GetGiftResponse
// @Router /boss/vip/gift/delete [DELETE]
func DeleteGift(c echo.Context) error {
	req := &cc.BaseIdRequest{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.DeleteGift(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.GetGiftResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 开卡礼包上下架
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model body cc.BaseIdRequest true " "
// @Success 200 {object} cc.VcBaseResponse
// @Failure 400 {object} cc.VcBaseResponse
// @Router /boss/vip/gift/up_down [POST]
func UpDownGift(c echo.Context) error {
	req := new(cc.BaseIdRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	if out, err := client.VipCard.UpDownGift(client.Ctx, req); err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 用户手机号解密
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query oc.GetOrderListRequest true " "
// @Success 200 {object} dto.UserMobileEecrypt
// @Failure 400 {object} dto.UserMobileEecrypt
// @Router /boss/vip/order/mobile-ecrypt [GET]
func VipMobileEcrypt(c echo.Context) error {
	out := &dto.UserMobileEecrypt{Code: 400}
	UserId := c.QueryParam("user_id")
	user_mobile := ""
	var (
		userNo, userName = "", ""
	)

	//获取用户信息
	if claims, err := utils.GetPayloadDirectly(c); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		userNo = utils.InterfaceToString(claims["userno"])
		userName = utils.InterfaceToString(claims["name"])
	}

	_, err := GetDatacenterDBConn().Table("scrm_organization_db.t_scrm_user_info").Select("user_mobile").Where("user_id=?", UserId).Get(&user_mobile)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(int(out.Code), out)
	}
	//记录操作日志
	//记录日志
	GetDatacenterDBConn().Insert(models.StoreOperateLog{
		Type:       5,
		FromId:     0,
		Desc:       "解密手机号",
		UserName:   userName,
		UserNo:     userNo,
		BeforeJson: UserId,
		AfterJson:  "",
	})
	out.UserMobile = user_mobile
	out.Code = 200
	return c.JSON(int(out.Code), out)
}

// @summary 会员卡订单明细
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query oc.GetOrderListRequest true " "
// @Success 200 {object} oc.GetOrderListResponse
// @Failure 400 {object} oc.GetOrderListResponse
// @Router /boss/vip/order/list [GET]
func GetVipCardOrderList(c echo.Context) error {
	virtualCardsStr := c.QueryParam("card_id")
	virtualCard := strings.Trim(virtualCardsStr, "FY")
	virtualCardId, _ := strconv.Atoi(virtualCard)

	req := &oc.GetOrderListRequest{
		PageIndex:     cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:      cast.ToInt32(c.QueryParam("pageSize")),
		Type:          cast.ToInt32(c.QueryParam("type")),
		Export:        cast.ToInt32(c.QueryParam("export")),
		OrderSn:       c.QueryParam("order_sn"),
		UserId:        c.QueryParam("user_id"),
		Ids:           c.QueryParam("ids"),
		UserMobile:    c.QueryParam("user_mobile"),
		CardName:      c.QueryParam("card_name"),
		Source:        cast.ToInt32(c.QueryParam("source")),
		PayTimeStart:  c.QueryParam("pay_time_start"),
		PayTimeEnd:    c.QueryParam("pay_time_end"),
		State:         cast.ToInt32(c.QueryParam("state")),
		Order:         "DESC",
		EntityOrderSn: c.QueryParam("entity_order_sn"),
		VirtualCardId: int64(virtualCardId),
	}
	client := oc.GetOrderServiceClient()

	if out, err := client.VC.GetOrderList(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.GetOrderListResponse{Code: 400, Message: err.Error()})
	} else {
		if req.Export == 1 {
			f := excelize.NewFile()
			writer, _ := f.NewStreamWriter("Sheet1")
			_ = writer.SetRow("A1", []interface{}{
				"用户id", "用户等级", "卡名称", "卡类型", "卡号", "周期", "订单号", "实体卡订单号", "手机", "购买时间", "销售方式", "支付方式", "订单状态", "子龙门店id", "门店名称", "卡密（虚拟卡券）", "大区", "省份", "城市", "分销人id", "分销人姓名", "分销佣金",
			})
			//订单状态：0-已取消，10-未付款(默认)，20-已付款，40-已完成
			//var stateMap = map[int32]string{10: "已支付", 20: "已退款"}
			var stateMap = map[int32]string{0: "已取消", 10: "未付款", 20: "已付款", 40: "已完成"}
			for i := 0; i < len(out.Data); i++ {
				v := out.Data[i]
				//销售方式
				xsfs := "主动购买"
				if v.Source == 1 {
					xsfs = "分销购买"
				} else if v.Source == 2 {
					xsfs = "虚拟卡券兑换"
				} else if v.Source == 3 {
					xsfs = "充值赠送"
				}

				storeId := ""
				if v.StoreId > 0 {
					storeId = cast.ToString(v.StoreId)
				}

				writer.SetRow("A"+strconv.Itoa(i+2), []interface{}{
					v.UserId,
					v.UserLevelId,
					v.CardName,
					v.CardTypeName,
					v.CardId,
					v.CycleName,
					v.OrderSn,
					v.EntityOrderSn,
					v.UserMobile,
					cast.ToString(v.PayTime),
					xsfs,
					v.PaymentCode,
					stateMap[v.OrderState],
					storeId,
					v.StoreName,
					v.CardPass,
					v.Region,
					v.Province,
					v.City,
					v.DisMemberId,
					v.DisMemberName,
					kit.FenToYuan(v.DisCommission),
				})
			}
			writer.Flush()
			var buff bytes.Buffer
			if err = f.Write(&buff); err != nil {
				return c.JSON(200, &dto.BaseRes{Code: 400, Message: "导出文件失败"})
			}
			fileName := "购卡明细列表导出 -" + time.Now().Format("20060102150405") + ".xlsx"
			c.Response().Header().Set(echo.HeaderContentType, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
			return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
		} else if req.Export == 2 {
			f := excelize.NewFile()
			writer, _ := f.NewStreamWriter("Sheet1")
			_ = writer.SetRow("A1", []interface{}{
				"用户id", "用户等级", "卡名称", "卡类型", "周期", "订单号", "手机", "销售方式", "退款时间", "退款金额",
			})

			for i := 0; i < len(out.Data); i++ {
				v := out.Data[i]
				//销售方式
				xsfs := "主动购买"
				if v.Source == 1 {
					xsfs = "分销购买"
				} else if v.Source == 2 {
					xsfs = "虚拟卡券兑换"
				} else if v.Source == 3 {
					xsfs = "充值赠送"
				}

				writer.SetRow("A"+strconv.Itoa(i+2), []interface{}{
					v.UserId,
					v.UserLevelId,
					v.CardName,
					v.CardTypeName,
					v.CycleName,
					v.OrderSn,
					v.UserMobile,
					xsfs,
					cast.ToString(v.RefundTime),
					kit.FenToYuan(v.RefundAmount),
				})
			}
			writer.Flush()
			var buff bytes.Buffer
			if err = f.Write(&buff); err != nil {
				return c.JSON(200, &dto.BaseRes{Code: 400, Message: "导出文件失败"})
			}
			fileName := "退卡明细列表导出 -" + time.Now().Format("20060102150405") + ".xlsx"
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
			return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
		}
		return c.JSON(int(out.Code), out)
	}
}

// @summary 会员卡订单权益详情 feature-vip1.4
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query oc.GetVipCardOrderEquityRequest true " "
// @Success 200 {object} oc.GetVipCardOrderEquityResponse
// @Failure 400 {object} oc.GetVipCardOrderEquityResponse
// @Router /boss/vip/order/equity [GET]
func GetVipCardOrderEquity(c echo.Context) error {
	out := &oc.GetVipCardOrderEquityResponse{
		Code: http.StatusBadRequest,
	}
	req := &oc.GetVipCardOrderEquityRequest{OrderSn: c.QueryParam("order_sn")}
	if len(req.OrderSn) == 0 {
		out.Message = "订单编号不能为空"
		return c.JSON(http.StatusOK, out)
	}
	client := oc.GetOrderServiceClient()
	if out, err := client.VC.GetVipCardOrderEquity(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.GetVipCardOrderEquityResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(200, out)
	}

}

// @summary 删除会员卡订单
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model body oc.DelVipOrderCardRequest true " "
// @Success 200 {object} oc.VcBaseResponse
// @Failure 400 {object} oc.VcBaseResponse
// @Router /boss/vip/order/delete [POST]
func DelVipOrderCard(c echo.Context) error {
	req := new(oc.DelVipOrderCardRequest)

	// 获取登录的用户信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, &oc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	req.UserNo = userInfo.UserNo
	req.UserName = userInfo.UserName

	if err := c.Bind(req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}
	client := oc.GetOrderServiceClient()
	defer client.Close()

	if out, err := client.VC.DelVipOrderCard(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.VcBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @summary 会员卡订单删除日志查询
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query oc.GetOrderOperateLogListRequest true " "
// @Success 200 {object} oc.GetOrderOperateLogListResponse
// @Failure 400 {object} oc.GetOrderOperateLogListResponse
// @Router /boss/vip/order-operate-log/list [GET]
func GetOrderOperateLogList(c echo.Context) error {
	req := &oc.GetOrderOperateLogListRequest{
		StartTime: c.QueryParam("start_time"),
		EndTime:   c.QueryParam("end_time"),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
	}
	client := oc.GetOrderServiceClient()

	out, err := client.VC.GetOrderOperateLogList(client.Ctx, req)
	if err != nil {
		return c.JSON(400, &oc.GetOrderOperateLogListResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(int(out.Code), out)
}

// @summary 优惠券名称校验
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query cc.VoucherRequest true " "
// @Success 200 {object} ac.BaseResponse
// @Failure 400 {object} ac.BaseResponse
// @Router /boss/vip/voucher/check [GET]
func CheckVoucher(c echo.Context) error {
	out := &ac.BaseResponse{Code: 400}
	req := &cc.VoucherRequest{
		Vid:  c.QueryParam("vid"),
		Type: cast.ToInt32(c.QueryParam("type")),
	}
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	rpcResponse, err := client.VipCard.CheckVouchers(client.Ctx, req)
	if err != nil {
		if status, ok := status.FromError(err); ok {
			out.Message = status.Message()
			out.Error = status.Message()
		}
		return c.JSON(400, &out)
	} else {
		return c.JSON(http.StatusOK, rpcResponse)
	}
}

// @summary 所属组织
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query dto.HttpJson true " "
// @Success 200 {object} dto.CommonHttpResponse
// @Failure 400 {object} dto.CommonHttpResponse
// @Router /boss/vip/organization/list [GET]
func OrganizationList(c echo.Context) error {
	out := &dto.CommonHttpResponse{Code: 200}

	type Equity struct {
		Label string `json:"label"`
		Value int32  `json:"value"`
	}

	out.Data = []Equity{
		{
			Label: "新瑞鹏集团",
			Value: -1,
		},
		{
			Label: "大湾区",
			Value: 1,
		},
		{
			Label: "华南区",
			Value: 2,
		},
		{
			Label: "华北区",
			Value: 3,
		},
		{
			Label: "东北区",
			Value: 4,
		},
		{
			Label: "华西区",
			Value: 5,
		},
		{
			Label: "华中区",
			Value: 6,
		},
		{
			Label: "西北区",
			Value: 8,
		},
		{
			Label: "上海区",
			Value: 9,
		},
		{
			Label: "南京区",
			Value: 10,
		},
		{
			Label: "苏皖区",
			Value: 11,
		},
		{
			Label: "浙闵一区",
			Value: 12,
		},
		{
			Label: "浙闵二区",
			Value: 13,
		},
	}

	return c.JSON(200, out)
}

// @summary 会员商品明细
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query pc.ShopVipCardGoodsRequest true " "
// @Success 200 {object} pc.ShopVipCardGoodsResponse
// @Failure 400 {object} pc.ShopVipCardGoodsResponse
// @Router /boss/vip/shop_good/list [GET]
func ShopVipCardGoodsList(c echo.Context) error {
	out := &pc.ShopVipCardGoodsResponse{Code: 400}
	req := &pc.ShopVipCardGoodsRequest{
		IsFree:    cast.ToInt32(c.QueryParam("is_free")),
		Export:    cast.ToInt32(c.QueryParam("export")),
		SkuId:     cast.ToInt32(c.QueryParam("sku_id")),
		SpuId:     cast.ToInt32(c.QueryParam("spu_id")),
		GoodsName: c.QueryParam("goods_name"),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
	}
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	client := pc.GetDcProductClient()
	defer client.Close()

	out, err := client.RPC.ShopVipCardGoodsList(client.Ctx, req)
	if err != nil {
		return c.JSON(400, &out)
	} else {
		if req.Export == 1 {
			f := excelize.NewFile()
			writer, _ := f.NewStreamWriter("Sheet1")
			_ = writer.SetRow("A1", []interface{}{
				"商品名称", "商品skuid", "商品spuid", "销售价", "商城活动价", "活动名称", "参与付费会员折扣的价格", "付费会员折扣",
				"付费会员价", "是否有免费会员价", "免费会员价",
			})
			for i := 0; i < len(out.Data); i++ {
				v := out.Data[i]
				writer.SetRow("A"+strconv.Itoa(i+2), []interface{}{
					v.GoodsName,
					v.SkuId,
					v.SpuId,
					v.GoodsPrice,
					v.GoodsPromotionPrice,
					v.GoodsPromotionName,
					v.VipDiscountPrice,
					v.VipDiscount,
					v.VipMemberPrice,
					v.IsMemberText,
					v.MemberPrice,
				})
			}
			writer.Flush()
			var buff bytes.Buffer
			if err = f.Write(&buff); err != nil {
				return c.JSON(200, &dto.BaseRes{Code: 400, Message: "导出文件失败"})
			}
			fileName := "会员商品明细列表导出 -" + time.Now().Format("20060102150405") + ".xlsx"
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
			return c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
		}
		return c.JSON(http.StatusOK, out)
	}
}

// @summary 会员商品删除
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query pc.DelShopVipCardGoodsReq true " "
// @Success 200 {object} pc.ProductBaseResponse
// @Failure 400 {object} pc.ProductBaseResponse
// @Router /boss/vip/shop_good/delete [DELETE]
func DelShopVipCardGoods(c echo.Context) error {
	out := &pc.ProductBaseResponse{Code: 400}
	req := &pc.DelShopVipCardGoodsReq{
		SkuId: cast.ToInt32(c.QueryParam("sku_id")),
	}
	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	req.UserId = userInfo.UserNo
	req.UserName = userInfo.UserName

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	client := pc.GetDcProductClient()
	defer client.Close()

	out, err = client.RPC.DelShopVipCardGoods(client.Ctx, req)
	if err != nil {
		return c.JSON(400, &out)
	} else {
		return c.JSON(http.StatusOK, out)
	}
}

// @summary 权益配置调用组织列表
// @Tags 付费会员卡
// @Accept json
// @Produce json
// @Param model query dto.HttpJson true " "
// @Success 200 {object} dto.CommonHttpResponse
// @Failure 400 {object} dto.CommonHttpResponse
// @Router /boss/vip/equity-org/list [GET]
func EquityOrganizationList(c echo.Context) error {
	out := &dto.CommonHttpResponse{Code: 200}
	type EquityOrg struct {
		OrName string `json:"or_name"`
		OrId   int32  `json:"or_id"`
	}
	var OrId []int32
	var resp []EquityOrg
	if err := GetDatacenterDBConn().SQL("select or_id from vip_card_template where type=1 group by or_id;").Find(&OrId); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else {
		var OrIdMap = map[int32]string{
			-1: "新瑞鹏集团", 1: "大湾区", 2: "华南区", 3: "华北区", 4: "东北区", 5: "华西区", 6: "华中区", 8: "西北区",
			9: "上海区", 10: "南京区", 11: "苏皖区", 12: "浙闵一区", 13: "浙闵二区"}
		for _, v := range OrId {
			var res = EquityOrg{
				OrId:   v,
				OrName: OrIdMap[v],
			}
			resp = append(resp, res)
		}
		out.Data = resp
	}
	return c.JSON(200, out)
}

// @summary 申请退费分页查询、导出 feature-vip1.4
// @Tags 申请退费
// @Accept json
// @Produce json
// @Param model query oc.GetVrRefundListRequest true " "
// @Success 200 {object} oc.GetVrRefundListResponse
// @Failure 400 {object} oc.GetVrRefundListResponse
// @Router /boss/vip/vr-refund/list [GET]
func VipVrRefundList(c echo.Context) error {
	req := &oc.GetVrRefundListRequest{
		ErpOrderSn: cast.ToString(c.QueryParam("erp_order_sn")),
		Mobile:     cast.ToString(c.QueryParam("mobile")),
		StartTime:  cast.ToString(c.QueryParam("start_time")),
		EndTime:    cast.ToString(c.QueryParam("end_time")),
		AdminState: cast.ToInt32(c.QueryParam("admin_state")),
		Export:     cast.ToInt32(c.QueryParam("export")),
		PageIndex:  cast.ToInt32(c.QueryParam("page_index")),
		PageSize:   cast.ToInt32(c.QueryParam("page_size")),
		CardName:   c.QueryParam("card_name"),
	}
	client := oc.GetOrderServiceClient()

	if out, err := client.VC.GetVipVrRefundList(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.GetVrRefundListResponse{Code: 400, Message: err.Error()})
	} else {
		if req.Export == 1 {
			f := excelize.NewFile()
			writer, _ := f.NewStreamWriter("Sheet1")
			_ = writer.SetRow("A1", []interface{}{
				"申请批次", "用户手机号", "订单号", "卡名称", "申请类型", "申请人", "申请原因", "申请时间", "状态", "审核人", "审核时间", "审核原因",
			})
			for i := 0; i < len(out.Data); i++ {
				v := out.Data[i]
				// 手机号码加密
				v.Mobile = v.Mobile[0:3] + "****" + v.Mobile[7:]

				// 申请类型转文字
				applyType := ""
				if v.ApplyType == 1 {
					applyType = "仅注销身份和权益"
				} else if v.ApplyType == 2 {
					applyType = "退款+注销身份和权益"
				}

				// 状态转文字
				state := ""
				if v.AdminState == 1 {
					state = "待审核"
				} else if v.AdminState == 2 {
					state = "审批拒绝"
				} else if v.AdminState == 3 {
					state = "退款成功"
				} else if v.AdminState == 4 {
					state = "退款失败"
				} else if v.AdminState == 5 {
					state = "注销成功"
				}

				writer.SetRow("A"+strconv.Itoa(i+2), []interface{}{
					v.RefundId,
					v.Mobile,
					v.OrderSn,
					v.CardName,
					applyType,
					v.ApplyUser,
					v.BuyerMessage,
					v.AddTime,
					state,
					v.AdminUser,
					v.AdminTime,
					v.AdminMessage,
				})
			}
			writer.Flush()
			var buff bytes.Buffer
			if err = f.Write(&buff); err != nil {
				return c.JSON(200, &dto.BaseRes{Code: 400, Message: "导出文件失败"})
			}
			fileName := "申请退费列表导出 -" + time.Now().Format("20060102150405") + ".xlsx"
			c.Response().Header().Set(echo.HeaderContentType, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
			return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
		}
		return c.JSON(int(out.Code), out)
	}
}

// @summary 申请退费详情（审核页展示用） feature-vip1.4
// @Tags 申请退费
// @Accept json
// @Produce json
// @Param model query oc.VipVrRefundDetailReq true " "
// @Success 200 {object} oc.VipVrRefundDetailResp
// @Failure 400 {object} oc.VipVrRefundDetailResp
// @Router /boss/vip/vr-refund/detail [GET]
func VipVrRefundDetail(c echo.Context) error {
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, &oc.VipVrRefundDetailResp{Code: 400, Message: err.Error()})
	}
	req := &oc.VipVrRefundDetailReq{
		RefundId: cast.ToInt32(c.QueryParam("refund_id")),
		UserId:   userInfo.UserNo,
	}
	client := oc.GetOrderServiceClient()
	out, err := client.VC.VipVrRefundDetail(client.Ctx, req)
	if err != nil {
		return c.JSON(400, &oc.VipVrRefundDetailResp{Code: 400, Message: err.Error()})
	}
	return c.JSON(int(out.Code), out)
}

// @summary 创建会员卡退费申请
// @Tags 申请退费
// @Accept json
// @Produce json
// @Param model query oc.CreateVrRefundRequest true " "
// @Success 200 {object} oc.CreateVrRefundResponse
// @Failure 400 {object} oc.CreateVrRefundResponse
// @Router /boss/vip/vr-refund/create [POST]
func CreateVipVrRefund(c echo.Context) error {
	req := &oc.CreateVrRefundRequest{}
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}
	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, &cc.VcBaseResponse{Code: 400, Message: err.Error()})
	}
	req.ApplyUser = userInfo.UserName

	client := oc.GetOrderServiceClient()

	out, err := client.VC.CreateVipVrRefund(client.Ctx, req)
	if err != nil {
		log.Error("会员卡退费异常")
		return err
	}
	return c.JSON(int(out.Code), out)
}

// @summary 健康服务金 按月下发 历史数据修补 vip-2.0.1
// @Tags 健康服务金
// @Accept json
// @Produce json
// @Success 200 {object} dto.CommonHttpResponse
// @Failure 400 {object} dto.CommonHttpResponse
// @Router /vip/insurance-month [GET]
func InsuranceMonth(c echo.Context) error {

	begin := c.QueryParam("begin")
	end := c.QueryParam("end")
	logFix := fmt.Sprintf("InsuranceMonth入参：%s ,%s", begin, end)
	glog.Info(logFix)

	out := &dto.CommonHttpResponse{Code: 200}

	client := dac.GetDcDataCenterClient()

	if out, err := client.RPC.InsuranceMonth(client.Ctx, &dac.InsuranceMonthReq{BeginDay: begin, EndDay: end}); err != nil {
		glog.Error(err)
		return c.JSON(400, &out)
	}
	return c.JSON(200, out)

}
