module _

go 1.13

require (
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/aliyun/aliyun-oss-go-sdk v2.1.10+incompatible
	github.com/baiyubin/aliyun-sts-go-sdk v0.0.0-20180326062324-cfa1a18b161f // indirect
	github.com/denisenkom/go-mssqldb v0.10.0 // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/go-openapi/spec v0.20.7 // indirect
	github.com/go-openapi/swag v0.22.3 // indirect
	github.com/go-playground/locales v0.13.0
	github.com/go-playground/universal-translator v0.17.0
	github.com/go-playground/validator/v10 v10.8.0
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.6.0
	github.com/go-xorm/xorm v0.7.9
	github.com/gojek/heimdall/v7 v7.0.2
	github.com/golang/protobuf v1.5.2
	github.com/google/uuid v1.2.0
	github.com/json-iterator/go v1.1.11
	github.com/labstack/echo/v4 v4.1.17
	github.com/labstack/gommon v0.3.0
	github.com/legofun/elasticsearch v0.0.0-20200918041933-1372b67196ce
	github.com/lib/pq v1.10.2 // indirect
	github.com/limitedlee/microservice v0.1.0
	github.com/mattn/go-sqlite3 v2.0.3+incompatible // indirect
	github.com/maybgit/glog v0.0.0-20210928064228-9506732eb074
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.19
	github.com/onsi/ginkgo v1.15.0 // indirect
	github.com/onsi/gomega v1.10.5 // indirect
	github.com/pkg/errors v0.9.1
	github.com/ppkg/kit v0.0.0-20210928070906-2e2b70f489af
	github.com/satori/go.uuid v1.2.0 // indirect
	github.com/shopspring/decimal v1.3.1
	github.com/sony/sonyflake v1.0.0
	github.com/spf13/cast v1.5.0
	github.com/streadway/amqp v1.0.0
	github.com/stretchr/testify v1.8.0
	github.com/swaggo/echo-swagger v0.0.0-20191205130555-62f81ea88919
	github.com/swaggo/gin-swagger v1.3.0 // indirect
	github.com/swaggo/swag v1.8.7
	github.com/tealeg/xlsx v1.0.5
	github.com/tricobbler/echo-tool v0.0.0-20210415033113-e8a087670120
	github.com/tricobbler/mqgo v0.3.1716
	github.com/tricobbler/rp-kit v0.0.0-20210326101043-100a30604458
	github.com/xuri/excelize/v2 v2.6.1
	golang.org/x/net v0.1.0
	golang.org/x/text v0.4.0
	golang.org/x/tools v0.2.0 // indirect
	google.golang.org/genproto v0.0.0-20210317182105-75c7a8546eb9
	google.golang.org/grpc v1.48.0
	google.golang.org/protobuf v1.28.1
	xorm.io/builder v0.3.9 // indirect
	xorm.io/core v0.7.3 // indirect
)

replace (
	_ => ./
	//github.com/maybgit/glog => github.com/maybgit/glog v0.0.0-20210928064228-9506732eb074
	github.com/maybgit/glog => github.com/maybgit/glog v0.0.0-20220118085313-5fbe11e2f392
)
