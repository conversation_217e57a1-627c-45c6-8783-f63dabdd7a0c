package controller

import (
	"_/dto"
	"_/models"
	mk "_/proto/mk"
	"_/proto/oc"
	"_/utils"
	"errors"
	"net/http"
	"reflect"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// grpc 函数调用
type GrpcFuncCall func(grpc *mk.Client) (response interface{}, err error)

// 请求处理
func PromotionGrpcProcess(c echo.Context, request interface{}, grpcFun GrpcFuncCall) error {
	// 400 错误代码返回
	var badResponse = new(mk.BaseResponse)
	badResponse.Code = mk.Code_parameterError

	//获取请求参数到实体对象
	err := c.Bind(request)
	if err != nil {
		glog.Error(err)
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 获取grpc 链接
	var conn = mk.GetMarketingCenterClient()
	// 关闭链接
	defer conn.Close()
	if conn == nil {
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = "内部Grpc通讯错误"
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	//调用Grpc方法
	response, err := grpcFun(conn)
	if err != nil {
		glog.Error(err)
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 反射查询Grpc响应
	var responseType = reflect.ValueOf(response)
	if responseType.Kind() == reflect.Ptr {
		responseType = responseType.Elem()
	}
	// 查询code 属性
	codePrototy := responseType.FieldByName("Code")
	if codePrototy.Kind() != reflect.Invalid {
		grpcCode := codePrototy.Int()
		// 解析错误代码
		if grpcCode > 400 {
			badResponse.Code = 400
			if grpcCode == int64(mk.Code_queryDbException) {
				badResponse.Error = "查询数据库异常"
			}
			if grpcCode == int64(mk.Code_saveDbException) {
				badResponse.Error = "保存数据库异常"
			}
			if grpcCode == int64(mk.Code_businessError) {
				// 是否有Message 信息
				var messageFiled = codePrototy.FieldByName("Message")
				if messageFiled.Kind() != reflect.Invalid {
					badResponse.Error = messageFiled.String()
				}
			}
			return c.JSON(http.StatusBadRequest, badResponse)
		}
	}

	return c.JSON(http.StatusOK, response)
}

// QueryPromotionSummary
// @Summary 查询店铺活动汇总(多店铺)
// @Tags 营销活动
// @Accept json
// @Produce json
// @param model body mk.PromotionSummaryReq true " "
// @Success 200 {object} mk.PromotionSummaryRes
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryPromotionSummary [post]
func QueryPromotionSummary(c echo.Context) error {
	req := new(mk.PromotionSummaryReq)
	return PromotionGrpcProcess(c, req, func(grpc *mk.Client) (interface{}, error) {
		if req.ShopId == "" {
			return nil, errors.New("财务编码不能为空")
		}
		return grpc.RPC.Summary(grpc.Ctx, req)
	})
}

// QueryPromotionDetailById
// @Summary 根据活动Id查询活动详细信息
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request query mk.QueryByIdRequest true "查询参数组合"
// @Success 200 {object} models.PromotionDetail
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryPromotionDetailById [get]
func QueryPromotionDetailById(c echo.Context) error {
	var request = new(mk.QueryByIdRequest)
	// 调用封装的函数
	return PromotionGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		var response models.PromotionDetail
		response.Code = 200
		// 查询促销活动
		promotionResponse, err := grpc.RPC.QueryPromotionDetailById(grpc.Ctx, request)
		if err == nil && promotionResponse.Code == 200 {
			response.PromotionResponse = promotionResponse
			// 查询订单统计
			var grpcClient = GetUpetDjClient()
			defer grpcClient.Close()

			var upetOrderRequest = new(oc.PromotonOrderReportRequest)
			upetOrderRequest.PromotionId = request.Id
			upetOrderRequest.StartDate = promotionResponse.Promotion.BeginDate
			upetOrderRequest.EndDate = promotionResponse.Promotion.EndDate
			upetOrderResponse, upetOrderErr := grpcClient.UpetDjClient.QueryPromotonOrderReport(grpc.Ctx, upetOrderRequest)
			if upetOrderErr == nil {
				response.PromotionOrderResponse = upetOrderResponse
			}
		} else if promotionResponse.Code > 200 && promotionResponse.Code < 400 {
			response.Code = int(promotionResponse.Code)
			response.Message = promotionResponse.Message
		}

		return response, err
	})
}

// QueryPromotionSummaryByUserId
// @Summary 查询营销活动日志 v6.6.7.1
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request query dto.GetPromotionOperateLogListRequest true "查询参数组合"
// @Success 200 {object} dto.GetPromotionOperateLogListResponse
// @Failure 400 {object} dto.GetPromotionOperateLogListResponse
// @Router /boss/Promotion/operate-list [Get]
func QueryPromotionOperateList(c echo.Context) error {
	var request = new(mk.GetPromotionOperateLogListRequest)
	var out = new(dto.GetPromotionOperateLogListResponse)
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)

	model := new(dto.GetPromotionOperateLogListRequest)
	if err := c.Bind(model); err != nil {
		out.Message = "参数错误"
		return c.JSON(400, out)
	}

	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	if userInfo.FinancialCode != "" {
		request.FinanceCode = userInfo.FinancialCode
	} else {
		if model.FinanceCode != "" {
			request.FinanceCode = model.FinanceCode
		}
	}
	if request.FinanceCode == "" {
		out.Message = "请选择门店后再查询"
		return c.JSON(400, out)
	}
	request.SkuId = model.SkuId
	request.PromotionName = model.PromotionName
	request.OperateType = model.OperateType
	request.StartTime = model.StartTime
	request.EndTime = model.EndTime
	request.PageIndex = model.PageIndex
	request.PageSize = model.PageSize
	request.PromotionId = model.PromotionId
	request.PromotionType = model.PromotionType

	// 获取grpc 链接
	var conn = mk.GetMarketingCenterClient()
	defer conn.Close()

	res, err := conn.RPC.GetPromotionOperateLogList(conn.Ctx, request)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	if res.Code != 200 {
		out.Message = res.Message
		return c.JSON(400, out)
	}
	out.Total = res.Total
	out.Data = res.Data
	out.Message = "获取成功"
	return c.JSON(200, out)
}
