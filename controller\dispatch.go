package controller

import (
	"_/dto"
	"_/proto/dc"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// GetA8WareHouseList @Summary 同步仓库信息列表
// @Tags 仓库
// @Accept plain
// @Produce json
// @Param beginTime query string false "开始时间"
// @Param endTime query string false "结束时间"
// @Param pageIndex query int false "页码"
// @Param pageSize query int false "页大小"
// @Param condition query string false "1"
// @Param isAll query int false "标记是否是查询全部的，如果是则不选择。默认值0，暂时为S2B2C使用，值为1"
// @Success 200 {object} dto.GetA8WareHouseDto
// @Failure 400 {object} dto.GetA8WareHouseDto
// @Router /boss/dispatch/wareHouse/A8List [get]
func GetA8WareHouseList(c echo.Context) error {
	out := new(dto.GetA8WareHouseDto)

	// rpc请求
	client := dc.GetDcDispatchClient()
	gRes, err := client.RPC.GetA8WareHouseList(client.Ctx, &dc.GetA8WareHouseListRequest{
		BeginTime: c.QueryParam("beginTime"),
		EndTime:   c.QueryParam("endTime"),
		PageIndex: cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:  cast.ToInt32(c.QueryParam("pageSize")),
		Condition: c.QueryParam("condition"),
		IsAll:     cast.ToInt32(c.QueryParam("isAll")),
	})
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	out.TotalCount = gRes.TotalCount
	out.Data = gRes.Data
	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// GetWarehouseRelationShopList @Summary 门店仓库管理列表
// @Tags 仓库
// @Accept plain
// @Produce json
// @Param model body dc.ShopBindWarehouseReq true " "
// @Success 200 {object} dc.ShopBindWarehouseRes
// @Failure 400 {object} dc.ShopBindWarehouseRes
// @Router /boss/dispatch/wareHouse/List [get]
func GetWarehouseRelationShopList(c echo.Context) error {
	out := new(dc.ShopBindWarehouseRes)

	client := dc.GetDcDispatchClient()
	gRes, err := client.RPC.WarehouseRelationShopList(client.Ctx, &dc.ShopBindWarehouseReq{
		ChannelId:       cast.ToInt32(c.QueryParam("channel_id")),
		BindType:        cast.ToInt32(c.QueryParam("bind_type")),
		PageIndex:       cast.ToInt32(c.QueryParam("page_index")),
		PageSize:        cast.ToInt32(c.QueryParam("page_size")),
		ShopSearch:      c.QueryParam("shop_search"),
		WarehouseSearch: c.QueryParam("warehouse_search"),
	})
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	out.TotalCount = gRes.TotalCount
	out.Info = gRes.Info
	out.Code = 200
	return c.JSON(http.StatusOK, out)
}
