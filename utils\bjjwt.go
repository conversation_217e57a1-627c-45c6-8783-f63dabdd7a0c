package utils

import (
	"crypto/rsa"
	"github.com/dgrijalva/jwt-go"
	"github.com/limitedlee/microservice/common/config"
	"io/ioutil"
	"log"
)

var (
	BjPublicKey *rsa.PublicKey
)

func init() {
	//北京jwt公钥
	BjPublicKeyString, err := config.Get("BJAuth.BjPublicKey")

	if err != nil {
		log.Println(err.Error())
	}

	bjpublicKeyByte, _ := ioutil.ReadFile(BjPublicKeyString)
	log.Println("BjPublicKey1: ", string(bjpublicKeyByte))
	BjPublicKey, _ = jwt.ParseRSAPublicKeyFromPEM(bjpublicKeyByte)
	log.Println("BjPublicKey: ", BjPublicKey)
}
