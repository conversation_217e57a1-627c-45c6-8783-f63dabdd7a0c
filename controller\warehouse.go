package controller

import (
	"_/models"
	"_/proto/ac"
	"_/proto/dac"
	proto "_/proto/dc"
	"_/proto/mk"
	"_/proto/pc"
	"_/utils"
	"context"
	"runtime"
	"strconv"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

// @Summary 新增仓库
// @Tags 调度中心
// @Param AddWarehouseRequest body dc.AddWarehouseRequest true " "
// @Success 200 {object} dc.BaseResponse
// @Router /boss/warehouse/add [post]
func AddWarehouse(c echo.Context) error {

	var res proto.BaseResponse
	model := new(proto.AddWarehouseRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	// v6.27.1  去除仓库的售药资质
	//model.SellDrugs = cast.ToInt32(c.FormValue("sell_drugs"))

	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	grpcRes, err := client.AddWarehouse(ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 编辑仓库信息v6.14
// @Tags v6.14
// @Accept json
// @Produce json
// @Param EditWarehouseRequest body dc.EditWarehouseRequest true " "
// @Success 200 {object} dc.BaseResponse
// @Router /boss/warehouse/edit [post]
func EditWarehouse(c echo.Context) error {

	var res proto.BaseResponse
	model := new(proto.EditWarehouseRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	//model.SellDrugs = cast.ToInt32(c.FormValue("sell_drugs"))

	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	grpcRes, err := client.EditWarehouse(ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 添加仓库配送区域
// @Tags 调度中心
// @Param AddWarehouseAreaRequest body dc.AddWarehouseAreaRequest true " "
// @Success 200 {object} dc.BaseResponse
// @Router /boss/warehouse/addwarehousearea [post]
func AddWarehouseArea(c echo.Context) error {

	var res proto.BaseResponse
	model := new(proto.AddWarehouseAreaRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	grpcRes, err := client.AddWarehouseArea(context.Background(), model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 获取仓库类型
// @Tags 调度中心
// @Success 200 {object} dc.GetWarehouseTypeResponse
// @Router /boss/warehouse/getwarehousetype [get]
func GetWarehouseType(c echo.Context) error {
	var res proto.BaseResponse
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	var empty proto.Empty
	grpcRes, err := client.GetWarehouseType(context.Background(), &empty)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 获取仓库等级
// @Tags 调度中心
// @Success 200 {object} dc.GetWarehouseLevelResponse
// @Router /boss/warehouse/getwarehouselevel [get]
func GetWarehouseLevel(c echo.Context) error {
	var res proto.BaseResponse
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	var empty proto.Empty
	grpcRes, err := client.GetWarehouseLevel(context.Background(), &empty)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 仓库列表
// @Tags 调度中心
// @Param category query string false "仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)"
// @Param pageindex query int true "分页索引"
// @Param pagesize query int true "分页大小"
// @Param keyword query string false "搜索关键词"
// @Param city query string false "城市"
// @Param prepose_category query int false "是否查询前置仓和前置虚拟仓, 默认 0 否，1 是"
// @Param status query int false "根据仓库状态查询, 默认 0 全部，1 启用，2 禁用"
// @Success 200 {object} dc.WarehouseListResponse
// @Router /boss/warehouse/warehouselist [post]
func GetWarehouseList(c echo.Context) error {
	category := c.FormValue("category")
	pageIndex, _ := strconv.Atoi(c.FormValue("pageindex"))
	pageSize, _ := strconv.Atoi(c.FormValue("pagesize"))
	keyword := c.FormValue("keyword")
	city := c.FormValue("city")
	status := cast.ToInt32(c.FormValue("status"))
	prepose := cast.ToInt32(c.FormValue("prepose_category"))
	//主体ID
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	grpcRes, err := client.WarehouseList(context.Background(), &proto.WarehouseListRequest{
		Category:        category,
		Pageindex:       int32(pageIndex),
		Pagesize:        int32(pageSize),
		KeyWord:         keyword,
		City:            city,
		PreposeCategory: prepose,
		Status:          status,
		OrgId:           cast.ToInt32(orgId),
	})
	if err != nil {
		return c.JSON(400, &proto.BaseResponse{Code: 400, Error: err.Error()})
	}
	if grpcRes.Code == 400 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 根据仓库ID获取仓库详情
// @Tags v6.14
// @Param id query int true "仓库ID"
// @Success 200 {object} dc.GetWarehouseByIdResponse
// @Router /boss/warehouse/warehousebyid [get]
func GetWarehouseById(c echo.Context) error {
	id, _ := strconv.Atoi(c.QueryParam("id"))
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	grpcRes, err := client.GetWarehouseById(context.Background(), &proto.GetWarehouseByIdRequest{Id: int32(id)})
	if err != nil {
		grpcRes.Code = 400
		grpcRes.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 变更仓库状态
// @Tags 调度中心
// @Param id query int true "仓库ID"
// @Param status query int true "仓库状态（0-禁用，1-启用）"
// @Success 200 {object} dc.BaseResponse
// @Router /boss/warehouse/updatewarehousestatus [post]
func UpdateWarehouseStatus(c echo.Context) error {
	id, _ := strconv.Atoi(c.FormValue("id"))
	status := c.FormValue("status")
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	grpcRes, err := client.UpdateWarehouseStatus(ctx, &proto.UpdateWarehouseStatusRequest{Id: int32(id), Status: status})
	if err != nil {
		grpcRes.Code = 400
		grpcRes.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 选择区域
// @Tags 调度中心
// @Param areadeep query int true " 地区深度,传1获取大区和省数据，传2获取大区省市数据，传3获取大区省市县数据"
// @Success 200 {object} dc.BaseAreaResponse
// @Router /boss/warehouse/getbasearea [get]
func GetBaseArea(c echo.Context) error {
	areadeep, _ := strconv.Atoi(c.QueryParam("areadeep"))
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewBaseAreaServiceClient(conn)
	grpcRes, err := client.BaseArea(context.Background(), &proto.BaseAreaRequest{Areadeep: int32(areadeep)})
	if err != nil {
		grpcRes.Code = 400
		grpcRes.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 根据仓库ID获取仓库配送区域
// @Tags 调度中心
// @Param warehouseid query int true "仓库ID"
// @Success 200 {object} dc.GetAreaByWarehouseIdResponse
// @Router /boss/warehouse/getAreabywarehouseid [get]
func GetAreaByWarehouseId(c echo.Context) error {
	id, _ := strconv.Atoi(c.QueryParam("warehouseid"))
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewBaseAreaServiceClient(conn)
	grpcRes, err := client.GetAreaByWarehouseId(context.Background(), &proto.GetAreaByWarehouseIdRequest{Warehouseid: int32(id)})
	if err != nil {
		grpcRes.Code = 400
		grpcRes.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 根据仓库配送区域查仓库列表
// @Tags 调度中心
// @Param WarehouseByAreaRequest body dc.WarehouseByAreaRequest true " "
// @Success 200 {object} dc.WarehouseByAreaResponse
// @Router /boss/warehouse/getwarehousebyarea [post]
func GetWarehouseByArea(c echo.Context) error {
	var res proto.WarehouseByAreaResponse

	model := new(proto.WarehouseByAreaRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)

	grpcRes, err := client.GetWarehouseByArea(context.Background(), model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

func GetGrpcConn() *grpc.ClientConn {
	grpcAddress := config.GetString("grpc.dispatch-center")
	if grpcAddress == "" || runtime.GOOS == "windows" {
		grpcAddress = "127.0.0.1:11006"
	}
	conn, err := grpc.Dial(grpcAddress, grpc.WithInsecure())
	if err != nil {
		//glog.Fatalf("did not connect: %v", err)
	}
	return conn

}

// @Summary 关联店铺
// @Tags 调度中心
// @Param WarehouseRelationRequest body dc.WarehouseRelationRequest true " "
// @Success 200 {object} dc.BaseResponse
// @Router /boss/warehouse/warehouserelation [post]
func WarehouseRelation(c echo.Context) error {
	var res proto.BaseResponse
	model := new(proto.WarehouseRelationRequest)
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	ctx = metadata.AppendToOutgoingContext(ctx, "ipAddr", c.RealIP())
	ctx = metadata.AppendToOutgoingContext(ctx, "ipLocation", GetIpAddress(c.RealIP()))
	grpcRes, err := client.WarehouseRelation(ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 根据仓库id获取已绑定的门店信息列表
// @Tags 调度中心
// @Param warehouseid query int true "仓库ID"
// @Success 200 {object} dc.WarehouseRelationListResponse
// @Router /boss/warehouse/getwarehouserelationlist [get]
func GetWarehouseRelationList(c echo.Context) error {
	id, _ := strconv.Atoi(c.QueryParam("warehouseid"))
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	grpcRes, err := client.GetWarehouseRelationList(context.Background(), &proto.WarehouseRelationListRequest{WarehouseId: int32(id)})
	if err != nil {
		grpcRes.Code = 400
		grpcRes.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 仓库操作日志
// @Tags 调度中心
// @Param WarehouseLogRequest body dc.WarehouseLogRequest true " "
// @Success 200 {object} dc.WarehouseLogResponse
// @Router /boss/warehouse/warehouselog [get]
func GetWarehouseLog(c echo.Context) error {
	starttime := c.FormValue("starttime")
	endtime := c.FormValue("endtime")
	pageIndex, _ := strconv.Atoi(c.FormValue("pageindex"))
	pageSize, _ := strconv.Atoi(c.FormValue("pagesize"))
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	grpcRes, err := client.WarehouseLog(context.Background(), &proto.WarehouseLogRequest{Starttime: starttime, Endtime: endtime, Pageindex: int32(pageIndex), Pagesize: int32(pageSize)})
	if err != nil {
		grpcRes.Code = 400
		grpcRes.Error = err.Error()
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 关联前置仓
// @Tags 调度中心
// @Param PreposeWarehouseRelationRequest body dc.PreposeWarehouseRelationRequest true " "
// @Success 200 {object} dc.BaseResponse
// @Router /boss/warehouse/region/relation [post]
func PreposeWarehouseRelation(c echo.Context) error {
	var res = new(proto.BaseResponse)
	model := new(proto.PreposeWarehouseRelationRequest)
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	res, err := client.PreposeWarehouseRelation(ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	} else if res.Code == 400 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 根据仓库id获取已绑定的前置仓信息列表
// @Tags 调度中心
// @Param warehouse_id query int true "仓库ID"
// @Success 200 {object} dc.RegionRelationListRespon
// @Router /boss/warehouse/region/relation/list [get]
func GetPreposeWarehouseRelationList(c echo.Context) error {
	id := cast.ToInt32(c.QueryParam("warehouse_id"))
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	grpcRes, err := client.GetPreposeWarehouseRelationList(context.Background(), &proto.WarehouseRelationListRequest{WarehouseId: id})
	if err != nil {
		grpcRes.Code = 400
		grpcRes.Msg = err.Error()
		return c.JSON(400, grpcRes)
	}
	if grpcRes.Code == 400 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 查询前置仓关联的所有城市列表
// @Tags 调度中心
// @Success 200 {object} dc.PreposeCiytListResponse
// @Failure 400 {object} dc.PreposeCiytListResponse
// @Router /boss/warehouse/prepose/city/list [get]
func PreposeWarehouseCityList(c echo.Context) error {
	conn := GetGrpcConn()
	defer conn.Close()
	client := proto.NewWarehouseServiceClient(conn)
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	res, err := client.PreposeWarehouseCityList(ctx, &proto.Empty{})
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if res.Code == 400 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 美团店铺关联虚拟仓批量导入
// @Tags 调度中心
// @Accept plain
// @Produce json
// @Param channel_id query string true "操作渠道 : 1阿闻外卖 2美团 3饿了么 4 京东 9互联网医院 10阿闻自提"
// @Param qiniu_url query string true "上传后的文件链接"
// @Param bind_type query string true "操作渠道 : 1电商仓 3门店仓 4前置仓  5前置虚拟仓"
// @Success 200 {object} pc.BindShopWarehouseResponse
// @Failure 400 {object} pc.BindShopWarehouseResponse
// @Router /boss/warehouse/shop_bind_warehouse [get]
func ShopBindWarehouse(c echo.Context) error {
	channel_id := cast.ToInt32(c.FormValue("channel_id"))
	qiniuUrl := c.FormValue("qiniu_url")
	bindType := cast.ToInt32(c.FormValue("bind_type"))

	client := GetDcProductClient(c)
	defer client.Close()
	res, err := client.RPC.ShopBindWarehouse(client.Ctx, &pc.BindShopWarehouse{
		ChannelId:  channel_id,
		QiniuUrl:   qiniuUrl,
		Ip:         c.Request().RemoteAddr,
		IpLocation: GetIpAddress(c.Request().RemoteAddr),
		BindType:   bindType,
	})
	if err != nil {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 获取商品绑定仓库列表
// @Tags 调度中心
// @Accept plain
// @Produce json
// @Param channel_id query string false "渠道id : 0 所有"
// @Param bind_type query string false "绑定仓库类型 1电商仓 3门店仓 4前置仓，5前置虚拟仓"
// @Param search query string false "店铺名称/财务编码"
// @Param page_index query int false "当前页码"
// @Param page_size query int false "每页行数"
// @Success 200 {object} dc.ShopBindInfoRespond
// @Failure 400 {object} dc.ShopBindInfoRespond
// @Router /boss/warehouse/shop_warehouse_info [get]
func ShopBindWarehouseList(c echo.Context) error {
	params := &proto.ShopBindInfoRequest{
		ChannelId: cast.ToInt32(c.QueryParam("channel_id")),
		BindType:  cast.ToInt32(c.QueryParam("bind_type")),
		Search:    c.QueryParam("search"),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
	}

	//默认分页大小20
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageIndex <= 0 {
		params.PageIndex = 1
	}

	client := GetDispatchCenter()
	defer client.Close()

	res, err := client.RPC.ShopBindInfoList(client.Ctx, params)
	if err != nil {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 获取绑定了仓库的店铺数量
// @Tags 调度中心
// @Accept plain
// @Produce json
// @Param channel_id query int false "渠道id : 0所有渠道"
// @Success 200 {object} dc.BindShopsRespond
// @Failure 400 {object} dc.BindShopsRespond
// @Router /boss/warehouse/bind_shop [get]
func BindShops(c echo.Context) error {
	params := &proto.BindShopsRequest{
		ChannelId: cast.ToInt32(c.QueryParam("channel_id")),
	}

	glog.Info("BindShops", params)
	client := GetDispatchCenter()
	defer client.Close()

	res, err := client.RPC.BindShops(client.Ctx, params)
	if err != nil {
		glog.Info("BindShops err", err)
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 取消导入任务
// @Tags 调度中心
// @Accept plain
// @Produce json
// @Param id query string false "任务id"
// @Success 200 {object} pc.CancelTaskResponse
// @Failure 400 {object} pc.CancelTaskResponse
// @Router /boss/warehouse/cancel_task [get]
func CancelTask(c echo.Context) error {
	params := &pc.CancelTaskRequest{
		Id: cast.ToInt64(c.QueryParam("id")),
	}

	client := GetDcProductClient()
	res, err := client.RPC.CancelTask(client.Ctx, params)
	if err != nil {
		return c.JSON(400, res)
	}

	return c.JSON(int(res.Code), res)
}

func InitWarehouseBindShopDB(c echo.Context) error {
	client := GetDispatchCenter()
	defer client.Close()

	params := &proto.InitShopDataRequest{}
	res, err := client.RPC.InitBindShopData(client.Ctx, params)
	if err != nil {
		glog.Info("InitBindShopData err", err)
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 仓库白名单列表
// @Tags 参数配置
// @Param model body dac.WarehouseWhiteListReq true " "
// @Success 200 {object} dac.WarehouseWhiteListRes
// @Failure 400 {object} dac.WarehouseWhiteListRes
// @Router /boss/warehouse/white-list [get]
func WarehouseWhiteList(c echo.Context) error {
	client := GetDataCenterClient()

	var model dac.WarehouseWhiteListReq
	model.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	model.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	model.Search = c.QueryParam("search")

	if model.PageSize <= 0 {
		model.PageSize = 10
	}
	if model.PageIndex <= 0 {
		model.PageIndex = 1
	}

	if out, err := client.RPC.WarehouseWhiteList(client.Ctx, &model); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 仓库白名单移除
// @Tags 参数配置
// @Param WarehouseWhiteDel body dac.WarehouseWhiteDelReq true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/warehouse/white-del [post]
func WarehouseWhiteDel(c echo.Context) error {
	var res dac.BaseResponse
	model := new(dac.WarehouseWhiteDelReq)
	if err := c.Bind(model); err != nil {
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	if model.Id <= 0 {
		res.Error = "参数错误"
		return c.JSON(400, res)
	}

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return c.JSON(400, &pc.CreateBatchTaskResponse{Code: 400, Message: err.Error(), Error: err.Error()})
	}

	model.CreateName = userInfo.UserName
	model.CreateId = userInfo.UserNo
	model.IpAddr = c.RealIP()
	model.IpLocation = GetIpAddress(c.RealIP())

	_, err = client.RPC.WarehouseWhiteDel(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 批量导入仓库白名单
// @Tags 参数配置
// @Param WarehouseWhiteImportReq body dac.WarehouseWhiteImportReq true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/warehouse/white-import [post]
func WarehouseWhiteImport(c echo.Context) error {
	var model dac.WarehouseWhiteImportReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	}

	if len(model.FileUrl) <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.WarehouseWhiteImport(client.Ctx, &model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 移除门店渠道绑定关系
// @Tags 调度中心
// @Accept plain
// @Produce json
// @Param channel_id query string false "渠道id"
// @Param shop_id query string false "门店财务编码"
// @Success 200 {object} dc.RemoveShopRespond
// @Failure 400 {object} dc.RemoveShopRespond
// @Router /boss/warehouse/remove_shop_channel_relation [get]
func RemoveShop(c echo.Context) error {
	params := &proto.RemoveShopRequest{
		ChannelId: cast.ToInt32(c.QueryParam("channel_id")),
		ShopId:    c.QueryParam("shop_id"),
	}

	client := GetDispatchCenter()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(client.Ctx, c)
	ctx = metadata.AppendToOutgoingContext(ctx, "ipAddr", c.RealIP())
	ctx = metadata.AppendToOutgoingContext(ctx, "ipLocation", GetIpAddress(c.RealIP()))
	res, err := client.RPC.RemoveShop(ctx, params)
	if err != nil {
		return c.JSON(400, res)
	}

	return c.JSON(int(res.Code), res)
}

// @Summary 操作记录
// @Tags 参数配置
// @Param model body pc.OperateRecordReq true " "
// @Success 200 {object} pc.OperateRecordRes
// @Failure 400 {object} pc.OperateRecordRes
// @Router /boss/warehouse/operate-record [get]
func OperateRecord(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var userInfo = models.LoginUserInfo{}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	}

	var model pc.OperateRecordReq
	model.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	model.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	model.TaskContent = cast.ToInt32(c.QueryParam("task_content"))
	model.Type = cast.ToInt32(c.QueryParam("type"))
	model.CreateId = userInfo.UserNo

	if model.PageSize <= 0 {
		model.PageSize = 10
	}
	if model.PageIndex <= 0 {
		model.PageIndex = 1
	}

	if out, err := client.RPC.OperateRecord(client.Ctx, &model); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 门店修改仓库
// @Tags 调度中心
// @Accept plain
// @Produce json
// @Param model body pc.ShopBindingWarehouseReq true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/warehouse/shop_bind_warehouse/edit [POST]
func EditShopBindWarehouse(c echo.Context) error {

	req := new(pc.ShopBindingWarehouseReq)
	req.Ip = c.Request().RemoteAddr
	req.IpLocation = GetIpAddress(c.Request().RemoteAddr)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}
	client := GetDcProductClient(c)
	defer client.Close()
	glog.Info(client.Ctx, "门店仓修改仓库,入参：", kit.JsonEncode(req))
	res, err := client.RPC.EditShopBindWarehouse(client.Ctx, req)
	if err != nil {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}
