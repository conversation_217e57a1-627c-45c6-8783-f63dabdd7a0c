name: 'Tests: linting'

on: [pull_request, push]

permissions:
  contents: read

jobs:
  eclint:
    runs-on: ubuntu-latest
    steps:
      - uses: step-security/harden-runner@v2
        with:
          allowed-endpoints:
            github.com:443
            raw.githubusercontent.com:443
            nodejs.org:443
            registry.npmjs.org:443
      - uses: actions/checkout@v4
      - uses: ljharb/actions/node/install@main
        name: 'nvm install ${{ matrix.node-version }} && npm install'
        with:
          node-version: 'lts/*'
      - run: npm run eclint

  dockerfile_lint:
    runs-on: ubuntu-latest
    steps:
      - uses: step-security/harden-runner@v2
        with:
          allowed-endpoints:
            ghcr.io:443
            github.com:443
            raw.githubusercontent.com:443
            pkg-containers.githubusercontent.com:443
            nodejs.org:443
            registry.npmjs.org:443
      - uses: actions/checkout@v4
      - uses: ljharb/actions/node/install@main
        name: 'nvm install ${{ matrix.node-version }} && npm install'
        with:
          node-version: 'lts/*'
      - run: npm run dockerfile_lint

  doctoc:
    runs-on: ubuntu-latest
    steps:
      - uses: step-security/harden-runner@v2
        with:
          allowed-endpoints:
            github.com:443
            raw.githubusercontent.com:443
            nodejs.org:443
            registry.npmjs.org:443
      - uses: actions/checkout@v4
      - uses: ljharb/actions/node/install@main
        name: 'nvm install ${{ matrix.node-version }} && npm install'
        with:
          node-version: 'lts/*'
      - run: npm run doctoc:check

  test_naming:
    runs-on: ubuntu-latest
    steps:
      - uses: step-security/harden-runner@v2
        with:
          allowed-endpoints:
            github.com:443
            raw.githubusercontent.com:443
      - uses: actions/checkout@v4
      - name: check tests filenames
        run: ./rename_test.sh --check
