package controller

import (
	"_/dto"
	"_/models"
	params2 "_/params"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/oc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"google.golang.org/grpc/metadata"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	r "github.com/tricobbler/echo-tool/httpError"
	"github.com/tricobbler/echo-tool/validate"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
)

// @Summary 报表分页查询
// @Tags v6.14
// @Accept plain
// @Produce json
// @Param search_type query int false "订单搜索类型:1.订单号;2.外部单号;3.收货人姓名;4.收货人手机号;5.买家手机号;6.店铺名称;7.子订单号;"
// @Param keyword query string false "搜索关键字"
// @Param time_type query int false "时间类型:0.下单时间;1.完成时间;"
// @Param start_time query string false "下单范围开始时间"
// @Param end_time query string false "下单范围结束时间"
// @Param product_name query string false "商品名称"
// @Param order_status query int false "订单状态:10未支付;20101.未接单;20102.已接单;20201待发货;20202已发货20103.配送中;20104.已送达;20105.已取货;20106.已完成;20107.已取消;"
// @Param channel_id query int false "订单来源渠道:1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;"
// @Param sale_channel query int false "销售渠道:1.Android;2.iOS;3.小程序;4.公众号;5.Web;6.其它;7.竖屏;"
// @Param order_type query string false "订单类型(以逗号格式分隔):1普通订单;2预定订单;3门店自提;4拼团订单;5门店配送;"
// @Param delivery_type query int false "配送方式:1.快递;2.外卖;3.自提;4.同城送;"
// @Param pay_mode query int false "支付方式:1.支付宝;2.微信;3.美团;4.其他;5.饿了么;6.京东支付;"
// @Param pay_sn query string false "支付单号"
// @Param app_channel query int false "店铺类型 1新瑞鹏  2TP代运营"
// @param combine_type query int false "商品组合类型 0-全部 1-实物实物 2-虚拟虚拟 3-虚拟实物 4-无组合"
// @Param page_index query int false "当前页码"
// @Param page_size query int false "每页行数"
// @Param order_delivery_filter query int false "异常配送订单筛选 0全部  1正常  2异常"
// @Success 200 {object} oc.DeliveryReportResponse
// @Failure 400 {object} oc.DeliveryReportResponse
// @Router /boss/ordercenter/order/delivery/report [get]
func OrderDeliveryReportList(c echo.Context) error {
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	params := &oc.AwenParentOrderListRequest{
		SearchType:          cast.ToInt32(c.QueryParam("search_type")),
		Keyword:             c.QueryParam("keyword"),
		TimeType:            cast.ToInt32(c.QueryParam("time_type")),
		StartTime:           c.QueryParam("start_time"),
		EndTime:             c.QueryParam("end_time"),
		ProductName:         c.QueryParam("product_name"),
		CombineType:         cast.ToInt32(c.QueryParam("combine_type")),
		ChannelId:           cast.ToInt32(c.QueryParam("channel_id")),
		OrderStatus:         cast.ToInt32(c.QueryParam("order_status")),
		SaleChannel:         cast.ToInt32(c.QueryParam("sale_channel")),
		OrderType:           c.QueryParam("order_type"),
		DeliveryType:        cast.ToInt32(c.QueryParam("delivery_type")),
		PayMode:             cast.ToInt32(c.QueryParam("pay_mode")),
		PaySn:               c.QueryParam("pay_sn"),
		AppChannel:          cast.ToInt32(c.QueryParam("app_channel")),
		PageIndex:           cast.ToInt32(c.QueryParam("page_index")),
		PageSize:            cast.ToInt32(c.QueryParam("page_size")),
		OrderFilter:         cast.ToInt32(c.QueryParam("order_filter")),
		OrderDeliveryFilter: cast.ToInt32(c.QueryParam("order_delivery_filter")),
		Orgid:               cast.ToInt64(orgId),
	}

	//默认分页大小20
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageIndex <= 0 {
		params.PageIndex = 1
	}

	if len(params.StartTime) == 0 && len(params.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择下单时间范围")
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}

	params.UserNo = userInfo.UserNo

	if userInfo.FinancialCode != "" {
		params.Shopids = []string{userInfo.FinancialCode}
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.OrderDeliveryReportList(client.Ctx, params); err != nil {
		glog.Error("调用OrderDeliveryReportList失败，", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

/*************************************订单列表及详情 add by csf******************************************/
// @Summary 订单分页列表
// @Tags v6.14
// @Accept plain
// @Produce json
// @Param search_type query int false "订单搜索类型:1.订单号;2.外部单号;3.收货人姓名;4.收货人手机号;5.买家手机号;6.店铺名称;7.子订单号;"
// @Param keyword query string false "搜索关键字"
// @Param time_type query int false "时间类型:0.下单时间;1.完成时间;"
// @Param start_time query string false "下单范围开始时间"
// @Param end_time query string false "下单范围结束时间"
// @Param product_name query string false "商品名称"
// @Param order_status query int false "订单状态:10未支付;20101.未接单;20102.已接单;20201待发货;20202已发货20103.配送中;20104.已送达;20105.已取货;20106.已完成;20107.已取消;"
// @Param channel_id query int false "订单来源渠道:1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;"
// @Param sale_channel query int false "销售渠道:1.Android;2.iOS;3.小程序;4.公众号;5.Web;6.其它;7.竖屏;"
// @Param order_type query string false "订单类型(以逗号格式分隔):1普通订单;2预定订单;3门店自提;4拼团订单;5门店配送;"
// @Param delivery_type query int false "配送方式:1.快递;2.外卖;3.自提;4.同城送;"
// @Param pay_mode query int false "支付方式:1.支付宝;2.微信;3.美团;4.其他;5.饿了么;6.京东支付;"
// @Param pay_sn query string false "支付单号"
// @Param app_channel query int false "店铺类型 1新瑞鹏  2TP代运营"
// @param combine_type query int false "商品组合类型 0-全部 1-实物实物 2-虚拟虚拟 3-虚拟实物 4-无组合"
// @Param page_index query int false "当前页码"
// @Param page_size query int false "每页行数"
// @Param order_delivery_filter query int false "/异常配送订单筛选 0全部  1正常  2异常"
// @Success 200 {object} oc.AwenParentOrderListResponse
// @Failure 400 {object} oc.AwenParentOrderListResponse
// @Router /boss/ordercenter/order/parent/list [get]
func AwenParentOrderList(c echo.Context) error {
	params := &oc.AwenParentOrderListRequest{
		SearchType:          cast.ToInt32(c.QueryParam("search_type")),
		Keyword:             c.QueryParam("keyword"),
		TimeType:            cast.ToInt32(c.QueryParam("time_type")),
		StartTime:           c.QueryParam("start_time"),
		EndTime:             c.QueryParam("end_time"),
		ProductName:         c.QueryParam("product_name"),
		CombineType:         cast.ToInt32(c.QueryParam("combine_type")),
		ChannelId:           cast.ToInt32(c.QueryParam("channel_id")),
		OrderStatus:         cast.ToInt32(c.QueryParam("order_status")),
		SaleChannel:         cast.ToInt32(c.QueryParam("sale_channel")),
		OrderType:           c.QueryParam("order_type"),
		DeliveryType:        cast.ToInt32(c.QueryParam("delivery_type")),
		PayMode:             cast.ToInt32(c.QueryParam("pay_mode")),
		PaySn:               c.QueryParam("pay_sn"),
		AppChannel:          cast.ToInt32(c.QueryParam("app_channel")),
		PageIndex:           cast.ToInt32(c.QueryParam("page_index")),
		PageSize:            cast.ToInt32(c.QueryParam("page_size")),
		OrderFilter:         cast.ToInt32(c.QueryParam("order_filter")),
		OrderDeliveryFilter: cast.ToInt32(c.QueryParam("order_delivery_filter")),
		FinancialCode:       c.QueryParam("financial_code"),
	}

	//默认分页大小20
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageIndex <= 0 {
		params.PageIndex = 1
	}
	logPrefix := "获取订单列表===="
	glog.Info(logPrefix, kit.JsonEncode(params))

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	params.Orgid = cast.ToInt64(orgId)

	if len(params.StartTime) == 0 && len(params.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择下单时间范围")
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}
	glog.Info(logPrefix, "用户信息为", kit.JsonEncode(userInfo))
	params.UserNo = userInfo.UserNo

	// validateAndGetShopIds 验证店铺权限并返回店铺ID列表
	shopIds, err := validateAndGetShopIds(userInfo, params.FinancialCode, orgId, logPrefix)
	if err != nil {
		return err
	}
	params.Shopids = shopIds

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenParentOrderList(client.Ctx, params); err != nil {
		glog.Error("调用AwenOrderList失败，", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—父—基础信息 v6.5.0
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenParentOrderBaseDetailResponse
// @Failure 400 {object} oc.AwenParentOrderBaseDetailResponse
// @Router /boss/ordercenter/order/parent/basedetail [get]
func AwenParentOrderBaseDetail(c echo.Context) error {
	type params struct {
		Orderid string `query:"orderid" validate:"required" label:"订单id"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	//校验参数
	if err := c.Validate(p); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		return r.NewHTTPError(400, err.One())
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenParentOrderBaseDetail(client.Ctx, &oc.AwenAllOrderBaseDetailRequest{
		OrderId: p.Orderid,
	}); err != nil {
		glog.Errorf("查询订单详情基础信息错误1：%s", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—父—订单状态信息
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenOrderDeliveryStateResponse
// @Failure 400 {object} oc.AwenOrderDeliveryStateResponse
// @Router /boss/ordercenter/order/parent/deliverystate [get]
func AwenParentOrderDeliveryState(c echo.Context) error {
	orderid := c.QueryParam("orderid")

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenParentOrderDeliveryState(context.Background(), &oc.AwenOrderDeliveryStateRequest{
		Orderid: orderid,
	}); err != nil {
		glog.Error("查询订单详配送状态信息错误：", err.Error())
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—父—订单配送流程信息
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenOrderDeliveryDetailResponse
// @Failure 400 {object} oc.AwenOrderDeliveryDetailResponse
// @Router /boss/ordercenter/order/parent/deliverydetail [get]
func AwenParentOrderDeliveryDetail(c echo.Context) error {
	orderid := c.QueryParam("orderid")

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenParentOrderDeliveryDetail(context.Background(), &oc.AwenOrderDeliveryDetailRequest{
		Orderid: orderid,
	}); err != nil {
		glog.Error("查询订单详配送流程信息错误：", err.Error())
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 物流查询
// @Tags 订单中心
// @Accept json
// @Produce json
// @Param model query oc.ExpressInfoRequest  true " "
// @Success 200 {object} oc.ExpressInfoResponse
// @Failure 400 {object} oc.ExpressInfoResponse
// @Router /boss/ordercenter/order/expressinfo [get]
func AwenOrderExpressinfo(c echo.Context) error {
	ocClient := oc.GetOrderServiceClient()
	in := &oc.ExpressInfoRequest{
		ExpressNo:  c.QueryParam("express_no"),
		OrderSn:    c.QueryParam("order_sn"),
		RefundSn:   c.QueryParam("refund_sn"),
		SearchType: c.QueryParam("search_type"),
	}
	grpcRes, err := ocClient.RPC.ExpressInfo(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用ExpressInfo失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, grpcRes)
}

// @Summary 更新物流信息
// @Tags 订单中心
// @Accept json
// @Produce json
// @Param model body oc.ExpressInfoUpdateRequest  true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/update-expressinfo [post]
func AwenOrderExpressInfoUpdate(c echo.Context) error {
	ocClient := oc.GetOrderServiceClient()
	in := &oc.ExpressInfoUpdateRequest{}
	out := &oc.BaseResponse{}
	if err := c.Bind(in); err != nil {
		out.Code = 400
		out.Message = "参数错误" + err.Error()
		return c.JSON(400, out)
	}
	//boss后台暂时只允许更新正向订单的快递信息
	in.UpdateOrderType = 1
	rsp, err := ocClient.RPC.ExpressInfoUpdate(ocClient.Ctx, in)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	} else if rsp.Code != 200 {
		return c.JSON(cast.ToInt(rsp.Code), rsp)
	} else {
		return c.JSON(200, rsp)
	}
}

// @Summary 实物订单分页列表
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param search_type query int false "订单搜索类型:1.子订单号;2.父订单号;3.外部单号;4.手机号;"
// @Param keyword query string false "搜索关键字"
// @Param time_type query int false "时间类型:0.下单时间;1.完成时间;"
// @Param start_time query string false "下单范围开始时间"
// @Param end_time query string false "下单范围结束时间"
// @Param product_name query string false "商品名称"
// @Param order_status query int false "订单状态:20101.未接单;20102.已接单;20103.配送中;20104.已送达;20105.已取货;20106.已完成;20107.已取消;"
// @Param channel_id query int false "订单来源渠道:1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;"
// @Param order_type query string false "订单类型(以逗号格式分隔):1普通订单;2预定订单;3门店自提;4拼团订单;5门店配送;"
// @Param delivery_type query int false "配送方式:1.快递;2.外卖;3.自提;4.同城送;"
// @Param pay_mode query int false "支付方式:1.支付宝;2.微信;3.美团;4.其他;5.饿了么;6.京东支付;"
// @Param page_index query int false "当前页码"
// @Param page_size query int false "每页行数"
// @Success 200 {object} oc.AwenMaterOrderListResponse
// @Failure 400 {object} oc.AwenMaterOrderListResponse
// @Router /boss/ordercenter/order/mater/list [get]
func AwenMaterOrderList(c echo.Context) error {
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	params := &oc.AwenMaterOrderListRequest{
		SearchType:   cast.ToInt32(c.QueryParam("search_type")),
		Keyword:      c.QueryParam("keyword"),
		TimeType:     cast.ToInt32(c.QueryParam("time_type")),
		StartTime:    c.QueryParam("start_time"),
		EndTime:      c.QueryParam("end_time"),
		ProductName:  c.QueryParam("product_name"),
		ChannelId:    cast.ToInt32(c.QueryParam("channel_id")),
		OrderStatus:  cast.ToInt32(c.QueryParam("order_status")),
		OrderType:    c.QueryParam("order_type"),
		DeliveryType: cast.ToInt32(c.QueryParam("delivery_type")),
		PayMode:      cast.ToInt32(c.QueryParam("pay_mode")),
		PageIndex:    cast.ToInt32(c.QueryParam("page_index")),
		PageSize:     cast.ToInt32(c.QueryParam("page_size")),
		AppChannel:   cast.ToInt32(c.QueryParam("app_channel")),
		OrgId:        cast.ToInt64(orgId),
	}

	//默认分页大小20
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageIndex <= 0 {
		params.PageIndex = 1
	}

	if len(params.StartTime) == 0 && len(params.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择下单时间范围")
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}

	if userInfo.FinancialCode == "" {
		params.UserNo = userInfo.UserNo
	} else {
		params.Shopids = []string{userInfo.FinancialCode}
	}
	glog.Info("实物订单查询请求参数，userInfo:", kit.JsonEncode(userInfo), ",param:", kit.JsonEncode(params))
	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenMaterOrderList(client.Ctx, params); err != nil {
		glog.Error("调用AwenOrderList失败，", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—实物—基础信息 v6.5.0
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenMaterOrderBaseDetailResponse
// @Failure 400 {object} oc.AwenMaterOrderBaseDetailResponse
// @Router /boss/ordercenter/order/mater/basedetail [get]
func AwenMaterOrderBaseDetail(c echo.Context) error {
	type params struct {
		Orderid string `query:"orderid" validate:"required" label:"订单id"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	//校验参数
	if err := c.Validate(p); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		return r.NewHTTPError(400, err.One())
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenMaterOrderBaseDetail(client.Ctx, &oc.AwenAllOrderBaseDetailRequest{
		OrderId: p.Orderid,
	}); err != nil {
		glog.Errorf("查询订单详情基础信息错误：%s", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—实物—订单状态信息
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenOrderDeliveryStateResponse
// @Failure 400 {object} oc.AwenOrderDeliveryStateResponse
// @Router /boss/ordercenter/order/mater/deliverystate [get]
func AwenMaterOrderDeliveryState(c echo.Context) error {
	orderid := c.QueryParam("orderid")

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenMaterOrderDeliveryState(context.Background(), &oc.AwenOrderDeliveryStateRequest{
		Orderid: orderid,
	}); err != nil {
		glog.Error("查询订单详配送状态信息错误：", err.Error())
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—实物—订单配送流程信息
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenOrderDeliveryDetailResponse
// @Failure 400 {object} oc.AwenOrderDeliveryDetailResponse
// @Router /boss/ordercenter/order/mater/deliverydetail [get]
func AwenMaterOrderDeliveryDetail(c echo.Context) error {
	orderid := c.QueryParam("orderid")

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenMaterOrderDeliveryDetail(context.Background(), &oc.AwenOrderDeliveryDetailRequest{
		Orderid: orderid,
	}); err != nil {
		glog.Error("查询订单详配送流程信息错误：", err.Error())
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 虚拟订单分页列表
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param search_type query int false "订单搜索类型:1.子订单号;2.父订单号;3.外部单号;4.手机号;"
// @Param keyword query string false "搜索关键字"
// @Param time_type query int false "时间类型:0.下单时间;1.完成时间;"
// @Param start_time query string false "下单范围开始时间"
// @Param end_time query string false "下单范围结束时间"
// @Param product_name query string false "商品名称"
// @Param order_status query int false "订单状态:30100已取消,30101待核销,30102部分核销,30103已完成(已核销)"
// @Param channel_id query int false "订单来源渠道:1.阿闻到家;2.美团;3.饿了么;4.京东到家;5.阿闻电商;6.门店;7.物竞天择;"
// @Param order_type query string false "订单类型(以逗号格式分隔):1普通订单;2预定订单;3门店自提;4拼团订单;5门店配送;"
// @Param page_index query int false "当前页码"
// @Param page_size query int false "每页行数"
// @Success 200 {object} oc.AwenVirtualOrderListResponse
// @Failure 400 {object} oc.AwenVirtualOrderListResponse
// @Router /boss/ordercenter/order/virtual/list [get]
func AwenVirtualOrderList(c echo.Context) error {
	params := &oc.AwenVirtualOrderListRequest{
		SearchType:  cast.ToInt32(c.QueryParam("search_type")),
		Keyword:     c.QueryParam("keyword"),
		TimeType:    cast.ToInt32(c.QueryParam("time_type")),
		StartTime:   c.QueryParam("start_time"),
		EndTime:     c.QueryParam("end_time"),
		ProductName: c.QueryParam("product_name"),
		ChannelId:   cast.ToInt32(c.QueryParam("channel_id")),
		OrderStatus: cast.ToInt32(c.QueryParam("order_status")),
		OrderType:   c.QueryParam("order_type"),
		PageIndex:   cast.ToInt32(c.QueryParam("page_index")),
		PageSize:    cast.ToInt32(c.QueryParam("page_size")),
		PayMode:     cast.ToInt32(c.QueryParam("pay_mode")),
	}

	//默认分页大小20
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageIndex <= 0 {
		params.PageIndex = 1
	}

	if len(params.StartTime) == 0 && len(params.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择下单时间范围")
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}

	if userInfo.FinancialCode == "" {
		params.UserNo = userInfo.UserNo
	} else {
		params.Shopids = []string{userInfo.FinancialCode}
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenVirtualOrderList(client.Ctx, params); err != nil {
		glog.Error("调用AwenOrderList失败，", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—虚拟—基础信息
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenVirtualOrderBaseDetailResponse
// @Failure 400 {object} oc.AwenVirtualOrderBaseDetailResponse
// @Router /boss/ordercenter/order/virtual/basedetail [get]
func AwenVirtualOrderBaseDetail(c echo.Context) error {
	type params struct {
		Orderid string `query:"orderid" validate:"required" label:"订单id"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	//校验参数
	if err := c.Validate(p); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		return r.NewHTTPError(400, err.One())
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenVirtualOrderBaseDetail(client.Ctx, &oc.AwenAllOrderBaseDetailRequest{
		OrderId: p.Orderid,
	}); err != nil {
		glog.Errorf("查询订单详情基础信息错误：%s", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—虚拟—订单状态信息
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenOrderDeliveryStateResponse
// @Failure 400 {object} oc.AwenOrderDeliveryStateResponse
// @Router /boss/ordercenter/order/virtual/deliverystate [get]
func AwenVirtualOrderDeliveryState(c echo.Context) error {
	orderid := c.QueryParam("orderid")

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenVirtualOrderDeliveryState(client.Ctx, &oc.AwenOrderDeliveryStateRequest{
		Orderid: orderid,
	}); err != nil {
		glog.Error("查询订单详配送状态信息错误：", err.Error())
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻管家订单详情—虚拟—订单配送流程信息
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param orderid query string true "订单id"
// @Success 200 {object} oc.AwenOrderDeliveryDetailResponse
// @Failure 400 {object} oc.AwenOrderDeliveryDetailResponse
// @Router /boss/ordercenter/order/virtual/deliverydetail [get]
func AwenVirtualOrderDeliveryDetail(c echo.Context) error {
	orderid := c.QueryParam("orderid")

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenVirtualOrderDeliveryDetail(context.Background(), &oc.AwenOrderDeliveryDetailRequest{
		Orderid: orderid,
	}); err != nil {
		glog.Error("查询订单详配送流程信息错误：", err.Error())
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 预订单列表查询接口(已与实物订单列表查询接口合并)
// @Tags 订单中心
// @Param shopid query string true "门店id"
// @Param pageindex query int true "分页索引"
// @Param pagesize query int true "分页大小"
// @Param channelid query int false "订单来源"
// @Param deliverytime query int false "临近配送时间"
// @Param salesource query int false "销售渠道"
// @Success 200 {object} oc.BookingOrderResponse
// @Router /boss/ordercenter/order/bookingorder/list [get]
func GetBookingOrderList(c echo.Context) error {
	var res oc.BookingOrderResponse
	model := new(oc.BookingOrderRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	shopIds := getShopids(c, model.ChannelId)
	model.Shopids = shopIds

	conn := oc.GetOrderServiceClient()
	r, err := conn.RPC.BookingOrderList(context.Background(), model)
	if err != nil || r.Code != 200 {
		return c.JSON(400, r)
	}
	if len(r.Details) == 0 {
		r.Details = make([]*oc.SimpleCombineOrder, 0)
	}
	return c.JSON(200, r)
}

// @Summary 订单打印小票
// @Tags 订单中心
// @Param orderid body oc.PrintBookingOrderRequest true " "
// @Success 200 {object} oc.PrintBookingOrderResponse
// @Router /boss/ordercenter/order/bookingorder/print [post]
func PrintBookingOrder(c echo.Context) error {
	model := new(oc.PrintBookingOrderRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	//连接池勿关闭
	redis := GetRedisConn()

	lockKey := "boss:lock:PrintBookingOrder:" + model.OrderId
	if !redis.SetNX(lockKey, 1, 3*time.Second).Val() {
		return c.JSON(200, models.BaseResponse{Code: 200})
	}
	defer redis.Del(lockKey)

	client := oc.GetOrderServiceClient()
	res, err := client.RPC.PrintBookingOrder(client.Ctx, model)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	} else if res.Code != 200 {
		return r.NewHTTPError(400, res.Message)
	}

	return c.JSON(200, res)
}

// @Summary 阿闻订单导出数据到EXCEL
// @Tags 订单中心
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/exports [post]
func AwenOrdersExport(c echo.Context) error {
	exportType, err := strconv.Atoi(c.Request().Header.Get("export_type"))
	if err != nil {
		return r.NewHTTPError(200, "export_type 参数错误")
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}

	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}

	var modelJson string
	switch exportType {
	case 1:
		model := new(oc.AwenMaterOrderListRequest)
		if err := c.Bind(model); err != nil {
			return r.NewHTTPError(400, err.Error())
		}

		if len(userInfo.FinancialCode) > 0 {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
		model.UserNo = UserNo
		modelJson = kit.JsonEncode(model)
	case 2:
		model := new(oc.AwenMaterOrderListRequest)
		if err := c.Bind(model); err != nil {
			return r.NewHTTPError(400, err.Error())
		}

		if len(userInfo.FinancialCode) > 0 {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
		model.UserNo = UserNo
		modelJson = kit.JsonEncode(model)
	case 3:
		model := new(oc.RefundOrderListRequest)
		if err := c.Bind(model); err != nil {
			return r.NewHTTPError(400, err.Error())
		}

		if userInfo.FinancialCode == "" {
			model.Shopids = getShopids(c, model.ChannelId)
			if len(model.Shopids) <= 0 {
				return c.JSON(200, oc.AwenOrderListResponse{Code: 400, Message: "获取不到用户有权限的门店", Error: "获取不到用户有权限的门店"})
			}
		} else {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
		model.UserNo = UserNo
		modelJson = kit.JsonEncode(model)
	case 4:
		model := new(oc.AwenParentOrderListRequest)
		if err := c.Bind(model); err != nil {
			return r.NewHTTPError(400, err.Error())
		}

		if len(userInfo.FinancialCode) > 0 {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
		model.UserNo = UserNo
		modelJson = kit.JsonEncode(model)
	case 5:
		model := new(oc.AwenParentOrderListRequest)
		if err := c.Bind(model); err != nil {
			return r.NewHTTPError(400, err.Error())
		}
		model.UserNo = UserNo
	case 6:
		model := new(oc.AwenVirtualOrderListRequest)
		if err := c.Bind(model); err != nil {
			return r.NewHTTPError(400, err.Error())
		}

		if len(userInfo.FinancialCode) > 0 {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
		model.UserNo = UserNo
		modelJson = kit.JsonEncode(model)
	case 7:
		model := new(oc.AwenVirtualOrderListRequest)
		if err := c.Bind(model); err != nil {
			return r.NewHTTPError(400, err.Error())
		}

		if len(userInfo.FinancialCode) > 0 {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
		model.UserNo = UserNo
		modelJson = kit.JsonEncode(model)
	default:
		return r.NewHTTPError(200, "unknown export_type")
	}

	//ip,location
	ip := strings.Split(c.Request().RemoteAddr, ":")[0]
	ipLocation := GetIpAddress(ip)

	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, ip, ipLocation, modelJson, exportType, "", "", 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 阿闻订单导出数据到EXCEL
// @Tags 订单中心
// @Param AwenOrderExport body oc.AwenMaterOrderListRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/export [post]
func AwenOrderExport(c echo.Context) error {
	model := new(oc.AwenMaterOrderListRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if len(model.StartTime) == 0 || len(model.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择时间范围")
	}
	model.Ip = strings.Split(c.Request().RemoteAddr, ":")[0]
	model.IpLocation = GetIpAddress(model.Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}

	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}

	model.UserNo = UserNo
	//todo feature-export-0505
	skip := config.GetString("export-skip-user")
	if skip != "" && userInfo.UserNo == skip {
		model.UserNo = ""
	}

	if len(userInfo.FinancialCode) > 0 {
		model.Shopids = append(model.Shopids, userInfo.FinancialCode)
	}

	taskContent := 1
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, model.Ip, model.IpLocation, kit.JsonEncode(model), taskContent, "", "", 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 阿闻订单导出数据(商品明细)到EXCEL
// @Tags 订单中心
// @Param AwenOrderExport body oc.AwenMaterOrderListRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/product/export [post]
func AwenOrderProductExport(c echo.Context) error {
	model := new(oc.AwenMaterOrderListRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if len(model.StartTime) == 0 || len(model.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择时间范围")
	}
	model.Ip = strings.Split(c.Request().RemoteAddr, ":")[0]
	model.IpLocation = GetIpAddress(model.Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}

	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}

	model.UserNo = UserNo
	if len(userInfo.FinancialCode) > 0 {
		model.Shopids = append(model.Shopids, userInfo.FinancialCode)
	}

	taskContent := 2
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, model.Ip, model.IpLocation, kit.JsonEncode(model), taskContent, "", "", 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 导出配送报表
// @Tags v6.14
// @Param AwenParentOrderExport body oc.AwenParentOrderListRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/delivery-report-export [post]
func DeliveryReportExport(c echo.Context) error {
	model := new(oc.AwenParentOrderListRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	model.Ip = strings.Split(c.Request().RemoteAddr, ":")[0]
	model.IpLocation = GetIpAddress(model.Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}
	model.UserNo = UserNo

	if len(userInfo.FinancialCode) > 0 {
		model.Shopids = append(model.Shopids, userInfo.FinancialCode)
	}

	taskContent := 14
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, model.Ip, model.IpLocation, kit.JsonEncode(model), taskContent, "", "", 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 阿闻管家父订单-导出订单数据
// @Tags 订单中心
// @Param AwenParentOrderExport body oc.AwenParentOrderListRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/parent-export [post]
func AwenParentOrderExport(c echo.Context) error {
	model := new(oc.AwenParentOrderListRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	if len(model.StartTime) == 0 && len(model.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择时间范围")
	}

	model.Ip = strings.Split(c.Request().RemoteAddr, ":")[0]
	model.IpLocation = GetIpAddress(model.Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}
	model.UserNo = UserNo
	//todo feature-export-0505
	skip := config.GetString("export-skip-user")
	if skip != "" && UserNo == skip {
		model.UserNo = ""
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.Orgid = cast.ToInt64(orgId)
	// 宠物saas 走这里
	if orgId == "6" {
		// validateAndGetShopIds 验证店铺权限并返回店铺ID列表
		model.Shopids, err = validateAndGetShopIds(userInfo, model.FinancialCode, orgId, "父订单列表导出")
		if err != nil {
			return err
		}
	} else {
		// 当前选中的店铺
		if len(userInfo.FinancialCode) > 0 {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
	}

	financialCode := ""
	if len(model.Shopids) == 1 {
		financialCode = model.Shopids[0]
	}
	taskContent := 4
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, model.Ip, model.IpLocation, kit.JsonEncode(model), taskContent, "", "", cast.ToInt32(orgId), financialCode)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 阿闻管家父订单-导出(含商品明细)数据
// @Tags 订单中心
// @Param AwenParentOrderExport body oc.AwenParentOrderListRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/product/parent-export [post]
func AwenParentOrderProductExport(c echo.Context) error {
	model := new(oc.AwenParentOrderListRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if len(model.StartTime) == 0 || len(model.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择时间范围")
	}
	model.Ip = strings.Split(c.Request().RemoteAddr, ":")[0]
	model.IpLocation = GetIpAddress(model.Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.Orgid = cast.ToInt64(orgId)

	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}
	model.UserNo = UserNo

	// 宠物saas 走这里
	if orgId == "6" {
		// validateAndGetShopIds 验证店铺权限并返回店铺ID列表
		model.Shopids, err = validateAndGetShopIds(userInfo, model.FinancialCode, orgId, "阿闻管家父订单-导出(含商品明细)")
		if err != nil {
			return err
		}
	} else {
		// 当前选中的店铺
		if len(userInfo.FinancialCode) > 0 {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
	}

	financialCode := ""
	if len(model.Shopids) == 1 {
		financialCode = model.Shopids[0]
	}

	taskContent := 5
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, model.Ip, model.IpLocation, kit.JsonEncode(model), taskContent, "", "", cast.ToInt32(orgId), financialCode)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 阿闻管家虚拟订单-导出订单数据
// @Tags 订单中心
// @Param AwenVirtualOrderExport body oc.AwenVirtualOrderListRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/virtual-export [post]
func AwenVirtualOrderExport(c echo.Context) error {
	model := new(oc.AwenVirtualOrderListRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if len(model.StartTime) == 0 || len(model.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择时间范围")
	}
	model.Ip = strings.Split(c.Request().RemoteAddr, ":")[0]
	model.IpLocation = GetIpAddress(model.Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}
	model.UserNo = UserNo
	if len(userInfo.FinancialCode) > 0 {
		model.Shopids = append(model.Shopids, userInfo.FinancialCode)
	}

	taskContent := 6
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, model.Ip, model.IpLocation, kit.JsonEncode(model), taskContent, "", "", 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 阿闻管家虚拟订单-导出(含商品明细)数据
// @Tags 订单中心
// @Param AwenVirtualOrderProductExport body oc.AwenVirtualOrderListRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/product/virtual-export [post]
func AwenVirtualOrderProductExport(c echo.Context) error {
	model := new(oc.AwenVirtualOrderListRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if len(model.StartTime) == 0 || len(model.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择时间范围")
	}
	model.Ip = strings.Split(c.Request().RemoteAddr, ":")[0]
	model.IpLocation = GetIpAddress(model.Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}
	model.UserNo = UserNo

	//todo feature-export-0505
	skip := config.GetString("export-skip-user")
	if skip != "" && UserNo == skip {
		model.UserNo = ""
	}

	if len(userInfo.FinancialCode) > 0 {
		model.Shopids = append(model.Shopids, userInfo.FinancialCode)
	}

	taskContent := 7
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, model.Ip, model.IpLocation, kit.JsonEncode(model), taskContent, "", "", 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 美团接单
// @Tags 订单中心
// @Param PrintBookingOrder body oc.AcceptOrderRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/accept-order [post]
func AcceptOrder(c echo.Context) error {
	model := new(oc.AcceptOrderRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	// 用户信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	glog.Info("后台订单列表接单按钮:", model.OrderSn, userInfo)

	client := oc.GetOrderServiceClient()
	client.Ctx = metadata.AppendToOutgoingContext(client.Ctx,
		"source", "admin-accept",
	)
	res, err := client.RPC.AcceptOrder(client.Ctx, model)
	if err != nil {
		glog.Error("调用AcceptOrder失败，", err)
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, res)
}

// @Summary 美团取消接单 saas-v1.0
// @Tags 订单中心
// @Param PrintBookingOrder body oc.CancelAcceptOrderRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/cancel-accept-order [post]
func CancelAcceptOrder(c echo.Context) error {
	var res oc.BaseResponse
	model := new(oc.CancelAcceptOrderRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	client := oc.GetOrderServiceClient()
	r, err := client.RPC.CancelAcceptOrder(kit.SetTimeoutCtx(AppendToOutgoingContext(context.Background(), c)), model)
	if err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}

	if r.Code != 200 {
		return c.JSON(400, r)
	}
	return c.JSON(200, r)
}

// @Summary 美团拣货完成接口 saas-v1.0
// @Tags 订单中心
// @Param PrintBookingOrder body oc.PickingOrderRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/picking-order [post]
func PickingOrder(c echo.Context) error {
	var res oc.BaseResponse
	model := new(oc.PickingOrderRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}

	// storeMasterId, err := GetAppChannelByOrderSn(model.OrderSn)
	// if err != nil {
	// 	glog.Error("PickingOrder,", "GetAppChannelByOrderSn,", model.OrderSn)
	// 	res.Code = 400
	// 	res.Message = err.Error()
	// 	return c.JSON(400, res)
	// }
	// model.StoreMasterId = storeMasterId

	client := oc.GetOrderServiceClient()
	r, err := client.RPC.PickingOrder(client.Ctx, model)
	if err != nil || r.Code != 200 {
		return c.JSON(400, r)
	}
	return c.JSON(200, r)
}

// @Summary 保存业绩分配
// @Tags 订单中心
// @Param params body oc.SavePerformanceRequest true " "
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /boss/ordercenter/performance/save [post]
func SavePerformance(c echo.Context) error {
	type params struct {
		OrderSn   string `json:"order_sn" validate:"required" label:"订单编号"`
		StaffId   string `json:"staff_id" validate:"required" label:"员工id"`
		StaffName string `json:"staff_name" validate:"required" label:"员工名称"`
	}

	p := new(params)
	//绑定参数
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	//校验参数
	if err := c.Validate(p); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		return r.NewHTTPError(400, err.One())
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	client := oc.GetOrderServiceClient()
	if _, err := client.RPC.SavePerformance(client.Ctx, &oc.SavePerformanceRequest{
		OrderSn:      p.OrderSn,
		StaffId:      p.StaffId,
		StaffName:    p.StaffName,
		OperatorId:   userInfo.UserNo,
		OperatorName: userInfo.UserName,
		Orgid:        cast.ToInt64(orgId),
	}); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	out := &models.BaseResponse{}
	out.Code = 200
	return c.JSON(200, out)
}

// @Summary 重新发起第三方推送
// @Tags 订单中心
// @Param MtRePushThird body oc.MtRePushThirdRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/re-push-third [post]
func MtRePushThird(c echo.Context) error {
	var res oc.BaseResponse
	model := new(oc.MtRePushThirdRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}

	client := oc.GetOrderServiceClient()
	r, err := client.RPC.MtRePushThird(client.Ctx, model)
	if err != nil || r.Code != 200 {
		res.Code = 400
		if err != nil {
			res.Message = err.Error()
		} else {
			res.Code = r.Code
			res.Message = r.Message
		}
		return c.JSON(400, res)
	}
	return c.JSON(200, r)
}

// @Summary 饿了么订单拣货完成
// @Tags 订单中心
// @Param ElmOrderPickCompleteReq body et.ElmOrderPickcompleteRequest true " "
// @Success 200 {object} et.ElmOrderPickcompleteResponse
// @Router /boss/noAuth/order/elm-pick-complete [post]
func ElmOrderPickComplete(c echo.Context) error {
	logPrefix := "饿了么订单拣货完成===="
	type reqStru struct {
		AppChannel int32  `json:"app_channel"`
		OldOrderSn string `json:"old_order_sn"`
	}
	var res oc.BaseResponse
	model := new(reqStru)
	if err := c.Bind(model); err != nil {
		glog.Error(logPrefix, "绑定参数失败", err.Error())
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(200, res)
	}
	glog.Info(logPrefix, "入参为：", kit.JsonEncode(model))
	client := et.GetExternalClient()
	req := et.ElmOrderPickcompleteRequest{
		AppChannel: model.AppChannel,
		OrderId:    model.OldOrderSn,
	}

	r, err := client.ELMORDER.ElmOrderPickcomplete(client.Ctx, &req)
	if err != nil {
		glog.Error(logPrefix, "，手动拣货通知饿了么错误！", err.Error())
	}
	glog.Info(logPrefix, "client.ELMORDER.ElmOrderPickcomplete手动拣货通知饿了么错误,返回结果", kit.JsonEncode(res))
	if r.Code != 200 && r.Code != 0 {
		err = errors.New(res.Error)
	}
	if err != nil || r.Code != 200 {
		res.Code = 400
		if err != nil {
			res.Message = err.Error()
		} else {
			res.Code = r.Code
			res.Message = r.Message
		}
		return c.JSON(200, res)
	}
	return c.JSON(200, r)
}

// @Summary 美团订单拣货完成
// @Tags 订单中心
// @Param MtOrderPickCompleteReq body et.MtOrderPickcompleteRequest true " "
// @Success 200 {object} et.MtOrderPickcompleteResponse
// @Router /boss/noAuth/order/mt-pick-complete [post]
func MtOrderPickComplete(c echo.Context) error {
	logPrefix := "美团订单拣货完成===="
	var res oc.BaseResponse
	type reqStru struct {
		ChannelId  int    `json:"channel_id"`
		OldOrderSn string `json:"old_order_sn"`
		AppChannel int    `json:"app_channel"`
	}
	model := new(reqStru)

	if err := c.Bind(model); err != nil {
		glog.Error(logPrefix, "解析参数失败", err.Error())
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(200, res)
	}
	glog.Info(logPrefix, "入参为", kit.JsonEncode(model))
	req := et.PreparationMealComplete{
		OrderId:       cast.ToInt64(model.OldOrderSn),
		StoreMasterId: int32(model.AppChannel),
	}
	client := et.GetExternalClient()
	r, err := client.MtOrder.OrderPreparationMealComplete(client.Ctx, &req)
	glog.Info(logPrefix, "client.MtOrder.OrderPreparationMealComplete返回", kit.JsonEncode(r))
	if err != nil {
		glog.Error(logPrefix, " 手动拣货通知美团错误！", err)
	}
	if res.Code != 200 && res.Code != 0 {
		err = errors.New(res.Error)
		glog.Warning(logPrefix, ",手动拣货通知美团错误！", kit.JsonEncode(res))
	}
	if err != nil || r.Code != 200 {
		res.Code = 400
		if err != nil {
			res.Message = err.Error()
		} else {
			res.Code = r.Code
			res.Message = r.Message
		}
		return c.JSON(200, res)
	}
	return c.JSON(200, r)
}

// @Summary 订单拣货完成通知第三方
// @Tags 订单中心
// @Param MtRePushThird body oc.MtRePushThirdRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/noAuth/order/push-delivery [post]
func PushDelivery(c echo.Context) error {
	var res oc.BaseResponse
	model := new(oc.MtRePushThirdRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(200, res)
	}

	client := oc.GetOrderServiceClient()
	r, err := client.RPC.PushDelivery(client.Ctx, model)
	if err != nil || r.Code != 200 {
		res.Code = 400
		if err != nil {
			res.Message = err.Error()
		} else {
			res.Code = r.Code
			res.Message = r.Message
		}
		return c.JSON(200, res)
	}
	return c.JSON(200, r)
}

// @Summary 查询各渠道的配送费v6.14
// @Tags v6.14
// @Param MtRePushThird body oc.MtRePushThirdRequest true " "
// @Success 200 {object} oc.SearchDeliveryPriceResponse
// @Router /boss/ordercenter/order/search-delivery-price [post]
func SearchDeliveryPrice(c echo.Context) error {
	var res oc.BaseResponse
	model := new(oc.MtRePushThirdRequest)

	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}

	client := oc.GetOrderServiceClient()
	r, err := client.RPC.SearchDelivery(client.Ctx, model)
	if err != nil || r.Code != 200 {
		res.Code = 400
		if err != nil {
			res.Message = err.Error()
		} else {
			res.Code = r.Code
			res.Message = r.Message
		}
		return c.JSON(400, res)
	}
	return c.JSON(200, r)
}

// OrderGetPartRefundFoods
// @Summary 查询可被部分退款的商品详情 -v6.0 saas-v1.0
// @Tags 美团退款
// @Param gy_order_sn query string true "美团订单号"
// @Success 200 {object} oc.OrderPartRefuFoodsResponse
// @Router /boss/ordercenter/order/refund-foods [get]
func OrderGetPartRefundFoods(c echo.Context) error {
	baseResponse := &oc.OrderPartRefuFoodsResponse{
		Code:    400,
		Message: "失败",
	}

	storeMasterId, err := GetAppChannelByOrderSn(c.QueryParam("gy_order_sn"))
	if err != nil {
		glog.Error("OrderGetPartRefundFoods,", "GetAppChannelByOrderSn", c.QueryParam("gy_order_sn"), err)
		baseResponse.Message = "获取店铺主体信息失败"
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.AfterSale.OrderGetPartRefundFoods(client.Ctx, &oc.OrderGetPartRefundFoodsRequest{
		OrderSn:       c.QueryParam("gy_order_sn"),
		StoreMasterId: storeMasterId,
	}); err != nil {
		glog.Error(err)
		baseResponse.Message = err.Error()
		return c.JSON(200, baseResponse)
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 发起部分退款
// @Tags 美团退款
// @Param OrderApplyPartRefund body oc.OrderApplyPartRefundRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/apply-part-refund [post]
func OrderApplyPartRefund(c echo.Context) error {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "失败",
	}

	model := new(oc.OrderApplyPartRefundRequest)

	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	// storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	// if err != nil {
	// 	glog.Error("OrderApplyPartRefund,", "GetAppChannelByOrderSn", model.OrderId, err)
	// 	out.Message = "获取店铺主体信息失败"
	// 	return c.JSON(400, out)
	// }
	// model.StoreMasterId = storeMasterId

	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.OrderApplyPartRefund(kit.SetTimeoutCtx(AppendToOutgoingContext(context.Background(), c)), model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 驳回订单退款申请
// @Tags 美团退款
// @Param MtOrderRefundReject body oc.MtOrderRefundRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/MtOrderRefundReject [post]
func MtOrderRefundReject(c echo.Context) error {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "失败",
	}

	model := new(oc.MtOrderRefundRequest)

	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	laims, err := utils.GetPayloadDirectly(c)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	}
	model.Operationer = fmt.Sprintf("%s", laims["name"])

	storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	if err != nil {
		glog.Error("MtOrderRefundReject,", "GetAppChannelByOrderSn", model.OrderId, err)
		out.Message = "获取店铺主体信息失败"
		return c.JSON(http.StatusBadRequest, out)
	}
	model.StoreMasterId = storeMasterId

	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.MtOrderRefundReject(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 订单确认退款请求
// @Tags 美团退款
// @Param MtOrderRefundAgree body oc.MtOrderRefundRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/MtOrderRefundAgree [post]
func MtOrderRefundAgree(c echo.Context) error {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "失败",
	}

	model := new(oc.MtOrderRefundRequest)
	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	laims, err := utils.GetPayloadDirectly(c)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	}
	model.Operationer = fmt.Sprintf("%s", laims["name"])

	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.MtOrderRefundAgree(client.Ctx, model); err != nil {
		glog.Error(model.OrderId, ", ", model.Refundsn, ", 退款审核同意失败, ", err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 驳回订单退款申请
// @Tags 美团退款
// @Param MtOrderRefundReject body oc.MtOrderRefundRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/OrderRefundReject [post]
func OrderRefundReject(c echo.Context) error {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "失败",
	}

	model := new(oc.MtOrderRefundRequest)

	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	if err != nil {
		glog.Error("OrderRefundReject,", "GetAppChannelByOrderSn", model.OrderId, err)
		out.Message = "获取店铺主体信息失败"
		return c.JSON(http.StatusBadRequest, out)
	}
	model.StoreMasterId = storeMasterId

	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.MtOrderRefundReject(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 订单确认退款请求
// @Tags 美团退款
// @Param MtOrderRefundAgree body oc.MtOrderRefundRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/OrderRefundAgree [post]
func OrderRefundAgree(c echo.Context) error {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "失败",
	}

	model := new(oc.MtOrderRefundRequest)

	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	// model.Operationer = model.Operationer

	// storeMasterId, err := GetAppChannelByOrderSn(model.ExternalOrderId)
	// if err != nil {
	// 	glog.Error("OrderRefundAgree,", "GetAppChannelByOrderSn,", model.ExternalOrderId)
	// 	out.Message = "获取店铺主体信息失败"
	// 	return c.JSON(200, out)
	// }
	// model.StoreMasterId = storeMasterId

	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.MtOrderRefundAgree(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 获取订单退款信息有多个退款信息
// @Tags 美团退款
// @Param OrderRetrunGetList body oc.RetrunOrderListRequest true " "
// @Success 200 {object} oc.RetrunOrderListResponse
// @Router /boss/ordercenter/order/OrderRetrunGetList [POST]
func OrderRetrunGetList(c echo.Context) error {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "失败",
	}

	model := new(oc.RetrunOrderListRequest)

	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.OrderRetrunGetList(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 获取退款信息-v6.0
// @Tags 美团退款
// @Param OrderRetrunGet body oc.RetrunOrderDetailRequest true " "
// @Success 200 {object} oc.RetrunOrderDetailResponse
// @Router /boss/ordercenter/order/OrderRetrunGet [POST]
func OrderRetrunGet(c echo.Context) error {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "失败",
	}

	model := new(oc.RetrunOrderDetailRequest)

	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.OrderRetrunGetDetail(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 退款列表
// @Tags 美团退款
// @Param RefundOrderList body oc.RefundOrderInfoRequest true " "
// @Success 200 {object} oc.RefundOrderInfoResponse
// @Router /boss/ordercenter/order/refund-order-list [POST]
func RefundOrderList(c echo.Context) error {
	out := &oc.RefundOrderInfoResponse{
		Code:    400,
		Message: "失败",
	}

	model := new(oc.RefundOrderInfoRequest)
	if err := c.Bind(model); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	// 根据主体标识判断使用哪种验证逻辑
	if orgId == "6" { // 宠物saas使用新的验证方法
		shopIds, err := validateAndGetShopIds(userInfo, model.FinancialCode, orgId, "RefundOrderList")
		if err != nil {
			return c.JSON(400, &oc.RefundOrderInfoResponse{Code: 400, Message: err.Error()})
		}
		model.Shopids = shopIds
	} else { // 其他主体使用原有逻辑
		if userInfo.FinancialCode == "" {
			model.Shopids = getShopids(c, model.ChannelId)
			if len(model.Shopids) <= 0 {
				return c.JSON(200, oc.AwenOrderListResponse{Code: 400, Message: "获取不到用户有权限的门店", Error: "获取不到用户有权限的门店"})
			}
		} else {
			model.Shopids = append(model.Shopids, userInfo.FinancialCode)
		}
	}

	model.Orgid = cast.ToInt64(orgId)
	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.RefundOrderList(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 退款导出excel
// @Tags 美团退款
// @Param RefundOrderExport body oc.RefundOrderInfoRequest true " "
// @Success 200 {object} oc.RefundOrderListResponse
// @Router /boss/ordercenter/order/refund-order-export [post]
func RefundOrderExport(c echo.Context) error {
	model := new(oc.RefundOrderInfoRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if len(model.StartTime) == 0 && len(model.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择导出时间范围")
	}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}
	model.UserNo = UserNo

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	taskContent := 3
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, "", "", kit.JsonEncode(model), taskContent, "", "", cast.ToInt32(orgId), userInfo.FinancialCode)

	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 退款单重推第三方
// @Tags 美团退款
// @Param RefundRePushThirdRequest body oc.RefundRePushThirdRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/refund-re-push-third [post]
func RefundRePushThird(c echo.Context) error {
	model := new(oc.RefundRePushThirdRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	client := oc.GetOrderServiceClient()
	if res, err := client.AfterSale.RefundRePushThird(client.Ctx, model); err != nil {
		return c.JSON(200, oc.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 订单导出任务——获取列表
// @Tags 订单中心
// @Accept json
// @Param create_time query string true "创建时间"
// @Param task_content query string true "任务内容（以逗号分隔开的字符串）:任务内容:1.实物订单-导出订单数据;2.实物订单-导出(含商品明细)数据;3.退货退款管理-导出订单数据;4.父订单-导出订单数据;5.父订单-导出(含商品明细)数据;6.虚拟订单-导出订单数据;7.虚拟订单-导出(含商品明细)数据;8.店铺配送9积分订单-导出 10团长制拼团-导出团员订单, 11.团长制拼团-导出团员实物订单，12.团长制拼团-导出团员虚拟订单"
// @param search_type query int true "任务归类 : 1.自己 2.所有人"
// @Param page_size query int true "每页大小"
// @Param page_index query int true "当前页"
// @Success 200 {object} dac.GetOrderExportTaskListResponse
// @Failure 400 {object} dac.GetOrderExportTaskListResponse
// @Router /boss/ordercenter/order/get-task-list [get]
func GetOrderExportTaskList(c echo.Context) error {
	out := new(dac.GetOrderExportTaskListResponse)
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	}
	userNo := userInfo.UserNo
	if userInfo.ScrmId != "" {
		userNo = userInfo.ScrmId
	}
	if len(userNo) == 0 {
		out.Message = "用户不存在"
		return c.JSON(200, out)
	}
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	//接收参数
	createTime := c.QueryParam("create_time")
	taskContent := c.QueryParam("task_content")
	pageSize := cast.ToInt32(c.QueryParam("page_size"))
	pageIndex := cast.ToInt32(c.QueryParam("page_index"))
	searchType := cast.ToInt32(c.QueryParam("search_type"))

	//调用rpc服务
	client := dac.GetDataCenterClient()
	grpcRes, err := client.RPC.GetOrderExportTaskList(context.Background(), &dac.GetOrderExportTaskListRequest{
		CreateId:      userNo,
		Createtime:    createTime,
		TaskContent:   taskContent,
		PageSize:      pageSize,
		PageIndex:     pageIndex,
		SearchType:    searchType,
		OrgId:         cast.ToInt32(orgId),
		FinancialCode: userInfo.FinancialCode,
	})
	if err != nil {
		return c.JSON(400, grpcRes)
	}

	return c.JSON(200, grpcRes)
}

// @Summary 订单详情取消配送
// @Tags v6.14
// @Param RefundOrderExport body oc.CancelDeliveryRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/CancelDelivery [post]
func CancelDelivery(c echo.Context) error {
	out := new(oc.BaseResponse)
	out.Code = 400
	model := new(oc.CancelDeliveryRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	//因为前端没有长整形，所以用字符串接受转换一下
	model.DeliveryId = cast.ToInt64(model.DeliveryStr)

	if model.DeliveryId <= 0 {
		out.Message = "配送ID不能为0"
		return c.JSON(400, out)
	}

	client := oc.GetOrderServiceClient()
	if res, err := client.RPC.CancelDelivery(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 达达骑手送回商品
// @Tags v6.14
// @Param DaDaformalCancelRequst body et.DaDaformalCancelRequst true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/DaDaConfirmGoodsReturn [post]
func DaDaConfirmGoodsReturn(c echo.Context) error {
	out := new(oc.BaseResponse)
	out.Code = 400
	model := new(et.DaDaformalCancelRequst)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	if len(model.OrderId) <= 0 {
		out.Message = "订单号不能为空"
		return c.JSON(400, out)
	}
	db := GetOrderCenterDBConn()

	//订单流转日志
	var orderLogs []models.OrderLog
	orderLogs = append(orderLogs, models.OrderLog{
		OrderSn:         model.OrderId,
		LogType:         39,
		OperateTypeName: "达达",
	},
	)

	//子单号
	ordersn := ""
	db.SQL("select order_sn from order_main where parent_order_sn=? and is_virtual=0", model.OrderId).Get(&ordersn)
	if ordersn == "" {
		out.Message = "未查询到子单号"
		return c.JSON(400, out)
	} else {
		model.OrderId = ordersn
	}
	orderLogs = append(orderLogs, models.OrderLog{
		OrderSn:         ordersn,
		LogType:         39,
		OperateTypeName: "达达",
	},
	)
	session := db.NewSession()
	session.Begin()
	_, err := session.Insert(&orderLogs)
	if err != nil {
		out.Message = "插入达达日志失败"
		return c.JSON(400, out)
	}

	client := et.GetExternalClient()
	if res, err := client.DaDa.ConfirmGoods(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		session.Rollback()
		return c.JSON(200, out)
	} else {
		if res.Code == 0 {
			session.Commit()
			res.Code = 200
		} else {
			session.Rollback()
		}
		return c.JSON(200, res)
	}
}

// @Summary 骑手送回商品
// @Tags 订单中心
// @Param RefundOrderExport body oc.ConfirmGoodsReturnRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/ConfirmGoodsReturn [post]
func ConfirmGoodsReturn(c echo.Context) error {
	out := new(oc.BaseResponse)
	out.Code = 400
	model := new(oc.ConfirmGoodsReturnRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	if model.DeliveryId <= 0 {
		out.Message = "配送ID不能为0"
		return c.JSON(400, out)
	}
	if len(model.OrderSn) <= 0 {
		out.Message = "订单号不能为空"
		return c.JSON(400, out)
	}

	client := oc.GetOrderServiceClient()
	if res, err := client.RPC.ConfirmGoodsReturn(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 修改货号
// @Tags 修改货号
// @Accept json
// @Produce json
// @Param UpdateOrderSkuReq body oc.UpdateOrderSkuReq true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/update-order-sku [post]
func UpdateOrderSku(c echo.Context) error {
	out := &oc.BaseResponse{
		Code:    400,
		Message: "失败",
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	glog.Error("UpdateOrderSku用户信息：", kit.JsonEncode(userInfo))
	if err != nil {
		return r.NewHTTPError(400, "获取登陆用户信息失败")
	}
	if len(userInfo.UserNo) == 0 {
		return r.NewHTTPError(400, "用户不存在")
	}

	model := new(oc.UpdateOrderSkuReq)
	if err := c.Bind(model); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	if cast.ToInt32(model.OrderProductModelId) == 0 || model.OrderSn == "" || model.SkuId == "" || model.ThirdSkuId == "" {
		out.Message = "参数错误"
		return c.JSON(400, out)
	}
	model.UserNo = userInfo.UserNo

	client := oc.GetOrderServiceClient()
	if res, err := client.RPC.UpdateOrderSku(client.Ctx, model); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(200, out)
	} else {
		return c.JSON(200, res)
	}
}

// BatchRefundDirectlyFee
// @Summary 批量退配送费
// @Tags 订单中心
// @Param RefundOrderExport body params.BatchRefundDistributionFee true " "
// @Success 200 {object} models.NewBaseResponse
// @Failure 400 {object} models.NewBaseResponse
// @Router /boss/ordercente/order/directlyFee/refund [post]
func BatchRefundDirectlyFee(c echo.Context) error {
	var (
		err error
	)
	params := new(params2.BatchRefundDistributionFee)
	if err := c.Bind(params); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	var userInfo models.LoginUserInfo
	if userInfo, err = utils.GetPayloadDirectlyToInterface(c); err != nil {
		return c.JSON(400, models.NewBaseResponse{Code: 400, Message: err.Error()})
	}

	client := oc.GetOrderServiceClient()
	UserNo := userInfo.UserNo
	if userInfo.ScrmId != "" {
		UserNo = userInfo.ScrmId
	}
	res, err := client.ROC.BatchRefundDeliveryFee(context.Background(), &oc.BatchRefundDeliveryFeeRequest{
		FileUrl:  params.FileUrl,
		UserNo:   UserNo,
		UserName: userInfo.UserName,
		UserIp:   c.RealIP(),
	})
	if err != nil {
		return c.JSON(400, models.NewBaseResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(200, models.NewBaseResponse{Code: res.Code, Message: res.Message})
}

// GetRefundDirectlyFee
// @Summary 查询退配送费
// @Tags 订单中心
// @Param keyword query string true "关键字"
// @Param page_size query int true "每页大小"
// @Param page_index query int true "当前页"
// @Success 200 {object} dto.GetRefundDirectlyFeeResponse
// @Failure 400 {object} dto.GetRefundDirectlyFeeResponse
// @Router /boss/ordercente/order/directlyFee/get [get]
func GetRefundDirectlyFee(c echo.Context) error {
	var (
		err error
	)

	//接收参数
	keyword := c.QueryParam("keyword")
	pageSize := cast.ToInt32(c.QueryParam("page_size"))
	pageIndex := cast.ToInt32(c.QueryParam("page_index"))

	client := oc.GetOrderServiceClient()
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	res, err := client.ROC.GetRefundDeliveryFee(context.Background(), &oc.GerRefundDeliveryFeeRequest{
		Keyword:   keyword,
		PageIndex: pageIndex,
		PageSize:  pageSize,
		OrgId:     cast.ToInt64(orgId),
	})
	if err != nil {
		return c.JSON(400, dto.GetRefundDirectlyFeeResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(200, dto.GetRefundDirectlyFeeResponse{Code: 200, TotalCount: res.TotalCount, Data: res.Data})
}

// ExportRefundDirectlyFee
// @Summary 导出退配送费记录
// @Tags 订单中心
// @Param keyword query string true "关键字"
// @Success 200 {object} dto.ExportRefundDeliveryFeeResponse
// @Failure 400 {object} dto.ExportRefundDeliveryFeeResponse
// @Router /boss/ordercente/order/directlyFee/export [get]
func ExportRefundDirectlyFee(c echo.Context) error {
	var (
		err error
	)
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	//接收参数
	keyword := c.QueryParam("keyword")

	client := oc.GetOrderServiceClient()

	res, err := client.ROC.ExportRefundDeliveryFee(context.Background(), &oc.GerRefundDeliveryFeeRequest{
		Keyword: keyword,
		OrgId:   cast.ToInt64(orgId),
	})
	if err != nil {
		return c.JSON(400, dto.ExportRefundDeliveryFeeResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(200, dto.ExportRefundDeliveryFeeResponse{Code: 200, Data: res})
}

// @Summary 订单设置发货
// @Tags 订单中心
// @Param model query oc.OrderSetDeliveryRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/setDelivery [post]
func OrderSetDelivery(c echo.Context) error {
	out := &oc.BaseResponse{Code: 400}
	params := &oc.OrderSetDeliveryRequest{}
	if err := c.Bind(params); err != nil {
		out.Message = "订单号错误"
		return c.JSON(400, out)
	}
	client := oc.GetOrderServiceClient()
	rsp, err := client.RPC.OrderSetDelivery(client.Ctx, params)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	return c.JSON(int(rsp.Code), rsp)
}

// @Summary 确认已发货 设置订单为已完成
// @Tags 订单中心
// @Param model body oc.ConfirmDeliveredOrderRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/confirmDelivered [post]
func OrderConfirmDelivered(c echo.Context) error {
	params := &oc.ConfirmDeliveredOrderRequest{}
	out := &oc.BaseResponse{Code: 400}
	if err := c.Bind(params); err != nil {
		out.Message = "参数错误"
		return c.JSON(400, out)
	}
	if len(params.OrderSn) == 0 {
		out.Message = "订单号错误"
		return c.JSON(400, out)
	}
	client := oc.GetOrderServiceClient()
	rsp, err := client.RPC.ConfirmDeliveredOrder(client.Ctx, params)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	return c.JSON(int(rsp.Code), rsp)
}

// @Summary 手机号解密
// @Tags 订单中心
// @Param model query dto.MobileDecryptReq true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/ordercenter/order/mobile/decrypt [post]
func MobileDecrypt(c echo.Context) error {
	req := &dto.MobileDecryptReq{}
	out := &dto.MobileDecryptRes{Code: 400}
	if err := c.Bind(req); err != nil {
		out.Message = "参数错误"
		return c.JSON(400, out)
	}
	if err := c.Validate(req); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}
	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		out.Message = "获取操作人信息失败!"
		return c.JSON(http.StatusBadRequest, out)
	}
	out.Data.Mobile = utils.MobileDecrypt(req.Ciphertext)
	go func() {
		logs := models.DecryptHistory{}
		logs.AppId = dto.PlatformId
		logs.Ip = c.RealIP()
		logs.Operator = userInfo.UserName
		logs.OperationTime = time.Now().Format("2006-01-02 15:04:05")
		logs.Ciphertext = req.Ciphertext
		if _, err := GetDatacenterDBConn().Insert(&logs); err != nil {
			glog.Error("MobileDecrypt 手机号解密失败,密文:", req.Ciphertext, ",", err.Error())
		}
	}()
	out.Code = 200
	return c.JSON(int(out.Code), out)
}

// @Summary 重新发起配送
// @Tags 订单中心
// @Param model query oc.ReDeliveryRequest true " "
// @Success 200 {object} oc.OrderExceptionResponse
// @Failure 400 {object} oc.OrderExceptionResponse
// @Router /boss/ordercenter/order/reDelivery [post]
// ReDelivery 订单列表-发起配送
func ReDelivery(c echo.Context) error {
	glog.Info("订单列表-发起配送")
	req := new(oc.ReDeliveryRequest)
	var res oc.OrderExceptionResponse
	if err := c.Bind(req); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := oc.GetOrderServiceClient()
	grpcRes, err := client.RPC.ReDelivery(context.Background(), req)
	if err != nil || grpcRes.Code != 200 {
		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("订单列表-发起配送：" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 自提订单手动完成
// @Tags 订单中心
// @Accept json
// @Produce json
// @Param model body oc.UpetDjConfirmRequest  true " "
// @Success 200 {object} oc.UpetDjConfirmResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/zt-confirm [post]
func ConfirmUpetDj(c echo.Context) error {
	ocClient := oc.GetOrderServiceClient()
	in := &oc.UpetDjConfirmRequest{}
	out := &oc.BaseResponse{}
	if err := c.Bind(in); err != nil {
		out.Code = 400
		out.Message = "参数错误" + err.Error()
		return c.JSON(400, out)
	}
	//把主单ID转子单ID
	db := GetOrderCenterDBConn()
	//子单号
	orderid := ""
	db.SQL("select id from order_main where parent_order_sn=? and is_virtual=0", in.OrderId).Get(&orderid)
	if orderid == "" {
		out.Message = "未查询到子单号"
		return c.JSON(400, out)
	} else {
		in.OrderId = orderid
	}

	ret, err := ocClient.UpetDJ.ConfirmUpetDj(ocClient.Ctx, in)
	if err != nil {
		out.Code = 400
		out.Message = "参数错误" + err.Error()
		return c.JSON(400, out)
	}
	return c.JSON(200, ret)
}

// validateAndGetShopIds 验证店铺权限并返回店铺ID列表
func validateAndGetShopIds(userInfo models.LoginUserInfo, financialCode string, orgId string, logPrefix string) ([]string, error) {
	// 右上角有选中门店
	if userInfo.FinancialCode != "" {
		// 如果搜索条件里有选中店铺，且右上角有选中店铺，它们必须相同
		if financialCode != "" && userInfo.FinancialCode != financialCode && orgId == "6" {
			glog.Error(logPrefix, "店铺入参有误")
			return nil, r.NewHTTPError(400, "店铺入参有误")
		}
		return []string{userInfo.FinancialCode}, nil
	}

	// 右上角没有选中门店
	if financialCode != "" {
		// 搜索条件里有选中店铺
		if !utils.InStringSlice(financialCode, userInfo.TenantIds) {
			glog.Error(logPrefix, "无店铺权限")
			return nil, r.NewHTTPError(400, "无店铺权限。")
		}
		return []string{financialCode}, nil
	} else if len(userInfo.TenantIds) > 0 {
		// 用户没有选中右上角店铺，且搜索列表用户没有选中
		return strings.Split(userInfo.TenantIds, ","), nil
	}

	return []string{}, nil
}
