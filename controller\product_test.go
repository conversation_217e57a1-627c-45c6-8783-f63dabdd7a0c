package controller

import (
	"_/proto/et"
	"_/proto/pc"
	"testing"

	"github.com/maybgit/glog"
)

/* func TestQueryProduct(t *testing.T) {
	conn, ctx, client, cf := getBaseClient2("", "")
	defer conn.Close()
	defer cf()

	if out, err := client.ImportThirdStock(ctx, &pb.ThirdInfoRequest{WarehouseId: 123}); err != nil {
		glog.Error(err)

	} else {
		println(out.Code)
	}
}

func getBaseClient2(grpcserverkey, defalutvalue string) (*grpc.ClientConn, context.Context, pb.InventoryServiceClient, context.CancelFunc) {
	productAddress := "**************:11003"
	// productAddress = "*************:11003"
	//productAddress := "127.0.0.1:11003"
	//productAddress = "**********:11003"
	productAddress = "**********:11007"

	if conn, err := grpc.Dial(productAddress, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil, nil, nil, nil
	} else {
		client := pb.NewInventoryServiceClient(conn)
		ctx, cf := context.WithTimeout(context.Background(), time.Second*300)
		return conn, ctx, client, cf
	}
}  ctx, client, cf
	}
} */

func TestGrpcContext(t *testing.T) {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	id := 1000100
	channel_id := 1

	req := &pc.OneofIdRequest{ChannelId: int32(channel_id), Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}

	if res, err := client.RPC.QueryChannelProductAttr(client.Ctx, req); err != nil {
		glog.Error(err)
	} else {
		println(len(res.Details))
	}
	t.Error()
}

func TestRetailCatUpdate(t *testing.T) {
	client := et.GetExternalClient()
	defer client.Close()

	syncData := &et.RetailCatUpdateRequest{
		AppPoiCode:            "4889_2701013",
		CategoryCodeOrigin:    "",
		CategoryNameOrigin:    "",
		CategoryCode:          "",
		CategoryName:          "测试一级分类55",
		SecondaryCategoryCode: "",
		SecondaryCategoryName: "",
		Sequence:              0,
		TargetLevel:           0,
		TargetParentName:      "",
		TopFlag:               0,
		WeeksTime:             "",
		Period:                "",
	}

	if res, err := client.RPC.RetailCatUpdate(client.Ctx, syncData); err != nil {
		t.Error(err)
	} else {
		t.Log(res)
	}
}
