package controller

import (
	"_/services"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/xuri/excelize/v2"
)

// @summary 导出商城在售商品的头图 feature-20231012-goodsimage
// @Tags 工具
// @Accept json
// @Produce json
// @Router /boss/tool/goods/image [GET]
func ExportGoodsImage(c echo.Context) error {
	//读取excel 总共43258张图片
	logPrefix := "导出商城在售商品的头图"
	now := time.Now()
	file, err := excelize.OpenFile("D:\\images\\20231020重新生成\\upet_goods_image里的主图.xlsx")
	if err != nil {
		glog.Error(logPrefix, "打开图片excel文件报错：", err.Error())
		return err
	}

	imageExcel := &services.ExcelStruct{
		File: file,
	}
	if err := imageExcel.Read(); err != nil {
		glog.Error(logPrefix, "imageExcel.Read()失败：", err.Error())
		return err
	}
	glog.Info("长度：", len(imageExcel.Data))
	//拉取阿里云或者七牛云的图片到本地
	unDownloadLink := make(chan []string, len(imageExcel.Data))
	glog.Info(logPrefix, "总图片数:", len(imageExcel.Data))
	var lastSpu string
	var i int
	for _, row := range imageExcel.Data {
		if len(lastSpu) == 0 {
			lastSpu = row[0]
		}
		if lastSpu != row[0] {
			lastSpu = row[0]
			i = 0
		}
		i++
		from := row[1]
		to := fmt.Sprintf("D:\\images\\20231020重新生成\\20231020spu\\%s\\头图", row[0])
		if !strings.HasPrefix(from, "http://") && !strings.HasPrefix(from, "https://") {
			from = fmt.Sprintf("%s/%s", "https://oss.upetmart.com/www/shop/store/goods/1", from)
		}
		to = fmt.Sprintf("%s\\头图%d.jpg", to, i)
		unDownloadLink <- []string{from, to}
	}

	close(unDownloadLink)

	var wg sync.WaitGroup
	wg.Add(20)
	//用40个goroutine来工作
	for i := 0; i < 20; i++ {
		go func() {
			defer wg.Done()
			for link := range unDownloadLink {
				if err := services.DownloadPic(link[0], link[1]); err != nil {
					glog.Error(logPrefix, err.Error())
				}

			}

		}()
	}
	wg.Wait()
	glog.Info("===================================================================================================================================主协程done", time.Since(now).Minutes())

	return nil

}

// @summary 导出商城在售商品的详情页图 feature-20231012-goodsimage
// @Tags 工具
// @Accept json
// @Produce json
// @Router /boss/tool/goods/image [GET]
func ExportGoodsDetailImage(c echo.Context) error {
	//读取excel 总共43258张图片
	logPrefix := "导出商城在售商品的详情图"
	now := time.Now()
	file, err := excelize.OpenFile("D:\\images\\20231020重新生成\\upet_goods_common里的详情页.xlsx")
	if err != nil {
		glog.Error(logPrefix, "打开图片excel文件报错：", err.Error())
		return err
	}

	imageExcel := &services.ExcelStruct{
		File: file,
	}
	if err := imageExcel.Read(); err != nil {
		glog.Error(logPrefix, "imageExcel.Read()失败：", err.Error())
		return err
	}
	glog.Info("长度：", len(imageExcel.Data))
	//拉取阿里云或者七牛云的图片到本地
	unDownloadLink := make(chan []string)
	var wg2 sync.WaitGroup
	wg2.Add(1)
	go func() {
		defer wg2.Done()
		for _, row := range imageExcel.Data {
			list := services.Crawl(row[1])

			for k, v := range list {
				from := v
				to := fmt.Sprintf("D:\\images\\20231020重新生成\\20231020spu\\%s\\详情图", row[0])
				if !strings.HasPrefix(v, "http://") && !strings.HasPrefix(v, "https://") {
					from = fmt.Sprintf("%s/%s", "https://oss.upetmart.com/www/shop/store/goods/1", from)
				}
				to = fmt.Sprintf("%s\\详情%d.jpg", to, k)

				unDownloadLink <- []string{from, to}
			}

		}
	}()
	go func() {
		wg2.Wait()
		close(unDownloadLink)
	}()

	var wg sync.WaitGroup
	wg.Add(20)
	//用40个goroutine来工作
	for i := 0; i < 20; i++ {
		go func() {
			defer wg.Done()
			for link := range unDownloadLink {
				if err := services.DownloadPic(link[0], link[1]); err != nil {
					glog.Error(logPrefix, err.Error())
				}

			}

		}()
	}
	wg.Wait()
	glog.Info("===================================================================================================================================主协程导出详情页图done", time.Since(now).Minutes())

	return nil

}

func CopyGoodsImage(c echo.Context) error {
	now := time.Now()
	logPrefix := "复制spu到sku"
	file2, err := excelize.OpenFile("D:\\images\\20231020重新生成\\skuspu.xlsx")
	if err != nil {
		glog.Error(logPrefix, "打开sku和spu映射关系excel文件报错：", err.Error())
		return err
	}

	skuSpuExcel := &services.ExcelStruct{
		File: file2,
	}
	if err := skuSpuExcel.Read(); err != nil {
		glog.Error(logPrefix, "skuSpuExcel.Read()失败：", err.Error())
		return err
	}
	spuSkuMap := make(map[string][]string, len(skuSpuExcel.Data))
	glog.Info(logPrefix, "sku总数：", len(skuSpuExcel.Data))
	for _, row := range skuSpuExcel.Data {
		if _, ok := spuSkuMap[row[1]]; !ok {
			spuSkuMap[row[1]] = make([]string, 0)
		}
		spuSkuMap[row[1]] = append(spuSkuMap[row[1]], row[0])
	}
	glog.Info(logPrefix, "spu总数:", len(spuSkuMap))

	//读取图片名称到通道里， 然后一个一个复制到对应的地方
	var paths = make(chan string)
	var sw sync.WaitGroup
	sw.Add(1)
	go func() {
		defer sw.Done()
		if err := services.WalkDir("D:\\images\\20231020重新生成\\20231020spu", paths); err != nil {
			glog.Error(logPrefix, "services.WalkDir出错:", err.Error())
		}
	}()
	go func() {
		sw.Wait()
		close(paths)
	}()
	var sw2 sync.WaitGroup
	sw2.Add(20)
	for i := 0; i < 20; i++ {
		go func() {
			defer sw2.Done()
			for path := range paths {
				srcPath := path
				pathSli := strings.Split(srcPath, "\\")
				spuId := pathSli[4]

				for _, skuId := range spuSkuMap[spuId] {
					dst := fmt.Sprintf("D:\\images\\20231020重新生成\\主图+详情页图\\%s\\%s\\%s", skuId, pathSli[5], pathSli[6])
					if err := services.CopyImage(srcPath, dst); err != nil {
						glog.Errorf("%s 复制图片从%s到%s出错:%s", logPrefix, srcPath, dst, err.Error())
					}
				}

			}
		}()
	}
	sw2.Wait()
	glog.Info("执行时长:", time.Since(now).Minutes())
	return nil
}

func UpdateRefundInfoExtend(c echo.Context) error {
	refundInfoIdStr := c.QueryParam("refund_info_id")
	refundInfoId, _ := strconv.Atoi(refundInfoIdStr)
	dealNumStr := c.QueryParam("deal_num")
	dealNum, _ := strconv.Atoi(dealNumStr)
	createdAt := c.QueryParam("created_at")
	logPrefix := fmt.Sprintf("更新退款扩展表的处理次数为0,refund_info_id=%d,dealNum=%d,createdAt=%s", refundInfoId, dealNum, createdAt)

	glog.Info(logPrefix)

	if refundInfoId > 0 {
		db := GetDatacenterDBConn()

		if len(createdAt) > 0 {
			_, err := db.Exec("update pay_center.refund_info_extend set deal_num=?,created_at=? where refund_info_id=?", dealNum, createdAt, refundInfoId)
			if err != nil {
				glog.Error(logPrefix, err.Error())
			}
		} else {
			_, err := db.Exec("update pay_center.refund_info_extend set deal_num=? where refund_info_id=?", dealNum, refundInfoId)
			if err != nil {
				glog.Error(logPrefix, err.Error())
			}
		}

	}
	return c.JSON(200, "操作成功")

}
