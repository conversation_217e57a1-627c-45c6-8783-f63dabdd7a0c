package controller

import (
	"_/models"
	"_/params"
	"_/proto/ac"
	"_/proto/mk"
	"_/utils"
	"bytes"
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/status"

	"github.com/maybgit/glog"
	"github.com/tricobbler/echo-tool/validate"

	"github.com/labstack/echo/v4"
)

// CreateSeckill
// @summary 创建秒杀活动 v6.23.0
// @tags 秒杀
// @accept json
// @produce json
// @param title body string true "活动名称"
// @param begin_time body string true "活动开始时间"
// @param end_time body string true "活动结束时间"
// @param seckill_order_limit body int true "购买上限"
// @param is_show body int false "是否显示 1：是 0：否 不传默认为0"
// @param is_shipping_free body int false "是否免邮费 0否1是"
// @Param user_id body string false "用户Id，即userno"
// @Param user_name body string false "userName,即登录人姓名"
// @Param seckill_order_cnt body int false "单用户该活动下单上限"
// @success 200 {object} params.BaseResponseNew
// @failure 400 {object} params.BaseResponseNew
// @router /boss/market/seckill/add [POST]
func CreateSeckill(c echo.Context) error {
	//解析获取的请求参数
	p := new(params.SeckillRequest)
	rpcRqt := new(ac.SeckillRequest)
	out := new(params.BaseResponseNew)
	var err error
	if err = c.Bind(p); err != nil {
		glog.Error("boss-eva-seckill-CreateSeckill,错误-Bind=", err.Error())
		out.Msg = "请求参数格式有误"
		return c.JSON(400, out)

	}
	logStr := "boss-eva-seckill-CreateSeckill,入参=" + kit.JsonEncode(p)
	glog.Info(logStr)
	// 请求参数数据验证
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",错误-Validate=", validateErrList.All())
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}

	//组装rpc请求参数
	if err = utils.MapTo(p, rpcRqt); err != nil {
		glog.Error(logStr, "错误-MapTo=", err.Error())
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	if p.UserId == "" {
		userInfo, err := utils.GetPayloadDirectlyToInterface(c)
		if err != nil {
			glog.Error("CreateSeckill-获取用户信息出错", err)
		}
		rpcRqt.UserId = userInfo.UserNo
		rpcRqt.UserName = userInfo.UserName
	}

	glog.Info(logStr, ", 数据记录-rpcRqt=", kit.JsonEncode(rpcRqt))
	// 获取grpc连接、调用grpc函数
	activityCenterClient := ac.GetActivityCenterClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	defer activityCenterClient.Close()
	if _, err = activityCenterClient.SK.CreateSeckill(ctx, rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, ", rpc-错误=", s.Message())
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
		out.Msg = "系统异常"
		return c.JSON(400, out)
	}

	// 返回数据
	return c.JSON(200, out)
}

// GetSeckillList
// @Summary 获取秒杀活动列表 v6.23.0
// @Tags 秒杀
// @Accept json
// @Produce json
// @Param model query params.SeckillListRequest true " "
// @Success 200 {object} params.SeckillListResponse
// @Failure 400 {object} params.SeckillListResponse
// @Router /boss/market/seckill/list [GET]
func GetSeckillList(c echo.Context) error {

	p := new(params.SeckillListRequest)
	rpcRqt := new(ac.SeckillListRequest)
	rpcRps := new(ac.PromotionListResponse)
	rps := new(params.SeckillListResponse)
	rps.Data = make([]*models.Promotion, 0)
	var err error

	//获取请求参数
	if err = c.Bind(p); err != nil {
		glog.Error("boss-eva-seckill-GetSeckillList,错误-Bind=", err.Error())
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}
	logStr := "boss-eva-seckill-GetSeckillList,入参=" + kit.JsonEncode(p)
	glog.Info(logStr)

	//验证参数
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ", Validate-错误=", validateErrList.All())
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}

	// 组装rpc请求参数
	if err = utils.MapTo(p, rpcRqt); err != nil {
		glog.Error(logStr, " | utils.MapTo-错误=", err.Error())
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	// 调用rpc函数
	activityCenterClient := ac.GetActivityCenterClient()
	defer activityCenterClient.Close()
	if rpcRps, err = activityCenterClient.SK.GetSeckillPagingList(activityCenterClient.Ctx, rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, " | rpc-错误=", s.Message())
			rps.Msg = s.Message()
			return c.JSON(400, rps)
		}
		rps.Msg = "系统异常"
		return c.JSON(400, rps)

	}
	glog.Info(logStr, "数据记录-rpcRps=", kit.JsonEncode(rpcRps))
	rps.Total = rpcRps.Total
	if len(rpcRps.Data) == 0 {
		return c.JSON(200, rps)
	}

	//组装返回数据

	for _, item := range rpcRps.Data {
		tmp := new(models.Promotion)
		if err = utils.MapTo(item, tmp); err != nil {
			glog.Error(logStr, "  | utils.MapTo-错误=", err.Error())
			rps.Msg = err.Error()
			return c.JSON(400, rps)
		}
		//如果活动状态是已结束，且不是定时器结束的活动（通过后台人为终止的活动）
		if tmp.Status == 3 && tmp.IsSystemEnd == 0 {
			tmp.Status = 4
		}
		tmp.StatusText = models.PromotionStatusTextMap[tmp.Status]
		rps.Data = append(rps.Data, tmp)
	}

	glog.Info(logStr, " | 函数返回=", kit.JsonEncode(rps))

	return c.JSON(200, rps)

}

// UpdateSeckill
// @Summary 编辑秒杀活动 v6.23.0
// @Tags 秒杀
// @Accept json
// @Produce json
// @Param id body integer true "活动id"
// @Param title body string true "活动名称"
// @Param begin_time body string true "活动开始时间"
// @Param end_time body string true "活动结束时间"
// @param seckill_order_limit body int true "购买上限"
// @param is_show body int false "是否显示 1：是 0：否 不传默认为0"
// @param is_shipping_free body int false "是否免邮费 0否1是"
// @Param user_id body string false "用户Id，即userno"
// @Param user_name body string false "userName,即登录人姓名"
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/market/seckill/edit [POST]
func UpdateSeckill(c echo.Context) error {
	//获取请求参数
	p := new(params.SeckillRequest)
	rpcRqt := new(ac.SeckillRequest)

	var (
		out params.BaseResponseNew
		err error
	)
	if err = c.Bind(p); err != nil {
		glog.Error("boss-eva-seckill-UpdateSeckill，Bind-错误", err.Error())
		out.Msg = "请求参数格式有误"
		return c.JSON(400, out)

	}
	logStr := "boss-eva-seckill-UpdateSeckill,入参=" + kit.JsonEncode(p)
	glog.Info(logStr)
	// 验证参数
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, " | validate-错误=", validateErrList.All())
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}

	//组装rpc请求参数
	if err = utils.MapTo(p, rpcRqt); err != nil {
		glog.Error(logStr, " ,MapTo-错误=", err.Error())
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	if p.UserId == "" {
		userInfo, err := utils.GetPayloadDirectlyToInterface(c)
		if err != nil {
			glog.Error("CreateSeckill-获取用户信息出错", err)
		}
		rpcRqt.UserId = userInfo.UserNo
		rpcRqt.UserName = userInfo.UserName
	}
	//调用rpc函数

	activityCenterClient := ac.GetActivityCenterClient()
	defer activityCenterClient.Close()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	if _, err = activityCenterClient.SK.UpdateSeckill(ctx, rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, " ,rpc-错误=", s.Message())
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
		out.Msg = "系统异常"
		return c.JSON(400, out)

	}
	return c.JSON(200, out)
}

// StopSeckill
// @Summary 停止秒杀活动
// @Tags 秒杀
// @Accept x-www-form-urlencoded
// @Produce json
// @Param id query integer true "秒杀活动ID"
// @Param user_id query string false "用户Id，即userno"
// @Param user_name query string false "userName,即登录人姓名"
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/market/seckill/stop [POST]
func StopSeckill(c echo.Context) error {
	p := new(params.StopSeckillRequest)
	rpcRqt := new(ac.StopSeckillRequest)
	var err error
	var out params.BaseResponseNew
	if err = c.Bind(p); err != nil {
		glog.Error("boss-eva-seckill-StopSeckill,Bind-错误=", err.Error())
		out.Msg = "请求参数格式有误"
		return c.JSON(400, out)
	}
	logStr := "boss-eva-seckill-StopSeckill，入参=" + kit.JsonEncode(p)
	glog.Info(logStr)

	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",Validate-错误=", validateErrList.All())
		out.Msg = validateErrList.One()
		return c.JSON(400, out)

	}

	if err = utils.MapTo(p, rpcRqt); err != nil {
		glog.Error(logStr, ", MapTo-错误=", err.Error())
		out.Msg = "数据映射错误"
		return c.JSON(400, out)

	}
	if p.UserId == "" {
		userInfo, err := utils.GetPayloadDirectlyToInterface(c)
		if err != nil {
			glog.Error("CreateSeckill-获取用户信息出错", err)
		}
		rpcRqt.UserId = userInfo.UserNo
		rpcRqt.UserName = userInfo.UserName
	}
	activityCenterClient := ac.GetActivityCenterClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	if _, err = activityCenterClient.SK.StopSeckill(ctx, rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, " ,rpc-错误=", s.Message())
			out.Msg = s.Message()
			return c.JSON(400, out)

		}
		out.Msg = "系统异常"
		return c.JSON(400, out)

	}
	return c.JSON(200, out)
}

// StopSeckill
// @Summary 显示/隐藏秒杀活动
// @Tags 秒杀
// @Accept x-www-form-urlencoded
// @Produce json
// @Param id query integer true "秒杀活动ID"
// @Param is_show query integer false "秒杀活动ID 不传默认为0"
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/market/seckill/show [POST]
func ShowSeckill(c echo.Context) error {
	p := new(params.ShowSeckillRequest)
	rpcRqt := new(ac.ShowSeckillRequest)
	var err error
	var out params.BaseResponseNew
	if err = c.Bind(p); err != nil {
		glog.Error("boss-eva-seckill-ShowSeckill,Bind-错误=", err.Error())
		out.Msg = "请求参数格式有误"
		return c.JSON(400, out)
	}
	logStr := "boss-eva-seckill-ShowSeckill，入参=" + kit.JsonEncode(p)
	glog.Info(logStr)

	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",Validate-错误=", validateErrList.All())
		out.Msg = validateErrList.One()
		return c.JSON(400, out)

	}

	if err = utils.MapTo(p, rpcRqt); err != nil {
		glog.Error(logStr, ", MapTo-错误=", err.Error())
		out.Msg = "数据映射错误"
		return c.JSON(400, out)

	}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("CreateSeckill-获取用户信息出错", err)
	}
	rpcRqt.UserId = userInfo.UserNo
	rpcRqt.UserName = userInfo.UserName
	activityCenterClient := ac.GetActivityCenterClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	if _, err = activityCenterClient.SK.ShowSeckill(ctx, rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, " ,rpc-错误=", s.Message())
			out.Msg = s.Message()
			return c.JSON(400, out)

		}
		out.Msg = "系统异常"
		return c.JSON(400, out)
	}
	return c.JSON(200, out)
}

// GetSeckill
// @Summary 获取秒杀活动详情 v6.23.0
// @Tags 秒杀
// @Accept x-www-form-urlencoded
// @Produce json
// @Param id query integer true "秒杀活动ID"
// @Success 200 {object} params.SeckillDetailResponse
// @Failure 400 {object} params.SeckillDetailResponse
// @Router /boss/market/seckill/detail [GET]
func GetSeckillDetail(c echo.Context) error {
	p := new(params.GetSeckillDetailRequest)
	rpcRqt := new(ac.GetSeckillDetailRequest)
	rpcRps := new(ac.Promotion)
	rps := new(params.SeckillDetailResponse)

	var err error

	if err = c.Bind(p); err != nil {
		glog.Error("boss-eva-seckill-GetSeckillDetail，,错误=", err.Error())
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}
	logStr := "boss-eva-seckill-GetSeckillDetail，入参=" + kit.JsonEncode(p)
	glog.Info(logStr)
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",错误-validate=", validateErrList.All())
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)

	}

	rpcRqt.Id = p.Id
	activityCenterClient := ac.GetActivityCenterClient()
	if rpcRps, err = activityCenterClient.SK.GetSeckillDetail(activityCenterClient.Ctx, rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, ",rpc-错误=", s.Message())
			rps.Msg = s.Message()
			return c.JSON(400, rps)
		}
		rps.Msg = "系统异常"
		return c.JSON(400, rps)
	}
	rps.Data = new(models.Promotion)
	if err = utils.MapTo(rpcRps, rps.Data); err != nil {
		glog.Error(logStr, ", MapTo-错误=", err.Error())
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	return c.JSON(200, rps)
}

// ---------------------------------秒杀商品管理部分----------------------------
// GetSeckillProductList
// @Summary 获取秒杀商品列表
// @Tags 秒杀商品管理
// @Accept plain
// @Produce json
// @Param model query params.GetSeckillProductListRequest false " "
// @Success 200 {object} params.GetSeckillProductListResponse
// @Failure 400 {object} params.GetSeckillProductListResponse
// @Router /boss/market/seckill/product/list [GET]
func GetSeckillProductList(c echo.Context) error {
	var (
		p      params.GetSeckillProductListRequest
		rpcRqt ac.GetSeckillProductListRequest
		rpcRps *ac.GetSeckillProductListResponse
		err    error
		rps    params.GetSeckillProductListResponse
	)
	// 如果是导出数据
	if c.QueryParam("export") == "1" {
		//client := micro.NewGrpcClient(ac.ActivityCenter, ac.NewSeckillServiceClient)
		client := ac.GetActivityCenterClient()
		out, err := client.SK.SeckillProductExport(client.Ctx, &ac.SeckillProductExportReq{
			Id: cast.ToInt32(c.QueryParam("promotion_id")),
		})
		if err != nil {
			rps.Msg = err.Error()
			return c.JSON(400, rps)
		}
		if out.Code != 200 {
			rps.Msg = out.Message
			return c.JSON(400, rps)
		}
		fileName := "秒杀活动商品导出-" + time.Now().Format("20060102150405") + ".xlsx"
		c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
		return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(out.File))
	}

	rps.Data = make([]*models.PromotionProduct, 0)

	if err = c.Bind(&p); err != nil {
		glog.Error("boss-eva-seckill-GetSeckillProductList,c.Bind-错误=", kit.JsonEncode(err))
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}
	logStr := "boss-eva-seckill-GetSeckillProductList,入参=" + kit.JsonEncode(p)
	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",c.Validate-错误=", kit.JsonEncode(validateErrList.All()))
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}
	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		glog.Error(logStr, "utils.MapTo-错误=", kit.JsonEncode(err))
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	client := ac.GetActivityCenterClient()

	rpcRqt.Type = cast.ToInt32(c.QueryParam("type"))
	if rpcRps, err = client.SK.GetSeckillProductList(client.Ctx, &rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, ",grpc-错误=", kit.JsonEncode(s.Message()))
			rps.Msg = s.Message()
			return c.JSON(400, rps)
		}
	}
	if rpcRps == nil {
		return c.JSON(200, rps)
	}

	rps.Total = rpcRps.Total
	if len(rpcRps.Data) > 0 {
		if err = utils.MapTo(rpcRps.Data, &rps.Data); err != nil {
			glog.Error(logStr, "utils.MapTo-错误=", kit.JsonEncode(err))
			return c.JSON(400, err.Error())
		}
	}
	glog.Info(logStr, "，函数返回=", kit.JsonEncode(rps))
	return c.JSON(200, rps)
}

// GetSeckillProductDetail
// @Summary 秒杀活动商品详细信息
// @Tags 秒杀商品管理
// @Accept plain
// @Produce json
// @Param id query int true "活动产品表id"
// @Success 200 {object} params.GetSeckillProductDetailResponse
// @Failure 400 {object} params.GetSeckillProductDetailResponse
// @Router /boss/market/seckill/product/detail [GET]
func GetSeckillProductDetail(c echo.Context) error {
	var err error
	p := new(params.GetSeckillProductDetailRequest)
	out := new(params.GetSeckillProductDetailResponse)

	rpcRequest := new(ac.GetSeckillProductDetailRequest)
	rpcResponse := new(ac.GetSeckillProductDetailResponse)

	if err = c.Bind(p); err != nil {
		glog.Error("boss-eva-seckill-GetSeckillProductDetail，错误-Bind=", err.Error())
		out.Msg = "请求参数格式有误"
		return c.JSON(400, out)
	}

	logStr := "boss-eva-seckill-GetSeckillProductDetail，入参=" + strconv.Itoa(int(p.Id))
	glog.Info(logStr)

	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",错误-Validate=", validateErrList.All())
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}

	rpcRequest.Id = p.Id
	client := ac.GetActivityCenterClient()
	defer client.Close()
	if rpcResponse, err = client.SK.GetSeckillProductDetail(client.Ctx, rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, ",错误-rpc=", s.Message())
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}

	if rpcResponse == nil {
		return c.JSON(200, out)
	}
	out.Data = new(models.PromotionProduct)
	if err = utils.MapTo(rpcResponse.Data, out.Data); err != nil {
		glog.Error(logStr, ",错误-MapTo=", err.Error())
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	glog.Info(logStr, ",函数返回=", kit.JsonEncode(out))
	return c.JSON(200, out)
}

// CreateOrUpdateSeckillProduct
// @Summary 创建或编辑秒杀活动商品
// @Tags 秒杀商品管理
// @Accept json
// @Produce json
// @Param spu_id body int true "产品spu id"
// @Param product_name body int true "产品名称"
// @Param sku_id body int true "商品 sku id"
// @Param price body int true "秒杀价（单位分)"
// @Param stock body int true "秒杀活动库存"
// @Param promotion_id body int true "活动id"
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/market/seckill/product/add-or-edit [POST]
func CreateOrUpdateSeckillProduct(c echo.Context) error {
	var (
		p      params.CreateOrUpdateSeckillProductRequest
		err    error
		rpcRqt ac.CreateOrUpdateSeckillProductRequest
		rpcRps *ac.SeckillResponse
		out    params.BaseResponseNew
	)
	//获取参数
	if err = c.Bind(&p); err != nil {
		glog.Error("boss-eva-seckill-CreateOrUpdateSeckillProduct,错误=" + err.Error())
		out.Msg = "请求参数格式有误"
		return c.JSON(400, out)
	}
	logStr := "boss-eva-seckill-CreateOrUpdateSeckillProduct,入参=" + kit.JsonEncode(p)
	glog.Info(logStr)

	//数据验证
	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",validate-错误=", validateErrList.All())
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		glog.Error(logStr, " , MapTo-错误=", err.Error())
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	rpcRqt.OrgId = cast.ToInt32(c.Request().Header.Get("org_id"))
	glog.Info(logStr, " , 数据记录=", kit.JsonEncode(rpcRqt))

	//调用远程rpc函数
	client := ac.GetActivityCenterClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	if rpcRps, err = client.SK.CreateOrUpdateSeckillProduct(ctx, &rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, " , client.SK.CreateSeckillProduct-错误=", s.Message())
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}
	glog.Info(logStr, " rpc-函数返回=", rpcRps)
	return c.JSON(200, out)

}

// DeleteSeckillProduct
// @Summary 删除秒杀活动商品信息
// @Tags 秒杀商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param id query int true "活动商品表id"
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/market/seckill/product/delete [POST]
func DeleteSeckillProduct(c echo.Context) error {
	var (
		p          params.DeleteSeckillProductRequest
		err        error
		out        params.BaseResponseNew
		rpcRequest ac.DeleteSeckillProductRequest
	)
	if err = c.Bind(&p); err != nil {
		glog.Error("boss-eva-seckill-DeleteSeckillProduct，Bind-错误=", err.Error())
		out.Msg = "请求参数格式有误"
		return c.JSON(400, out)
	}
	logStr := "boss-eva-seckill-DeleteSeckillProduct，入参=" + kit.JsonEncode(p)
	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",validate-错误=", validateErrList.All())
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}

	rpcRequest.Id = p.Id
	client := ac.GetActivityCenterClient()
	defer client.Close()
	ctx := kit.SetTimeoutCtx(AppendToOutgoingContext(context.Background(), c))
	if _, err = client.SK.DeleteSeckillProduct(ctx, &rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, " rpc-错误=", s.Message())
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}
	return c.JSON(200, out)

}

// GetSeckillUPetProductSelectList
// @Summary 获取可以参加秒杀活动的阿闻商城的商品
// @Tags 秒杀商品管理
// @Accept json
// @Produce json
// @Param page_index query int true "当前多少页 从1开始"
// @Param page_size query int true "每页多少条数据"
// @Param promotion_id query int true "秒杀活动id"
// @Param product_name query string false "商品名称"
// @Param sku_id query int false "商品sku id"
// @Param spu_id query int false "商品的产品id"
// @Success 200 {object} params.GetSeckillUPetProductSelectListResponse
// @Failure 400 {object} params.GetSeckillUPetProductSelectListResponse
// @Router /boss/market/seckill/product/select-list [GET]
func GetSeckillUPetProductSelectList(c echo.Context) error {

	var (
		p      params.GetSeckillUPetProductSelectListRequest
		err    error
		out    params.GetSeckillUPetProductSelectListResponse
		rpcRps *ac.GetUPetProductSelectListResponse
	)
	rpcRqt := new(ac.GetUPetProductSelectListRequest)
	out.Data = make([]*params.SelectUPetProductData, 0)
	if err = c.Bind(&p); err != nil {
		glog.Error("boss-eva-seckill-GetSeckillUPetProductSelectList，错误=", kit.JsonEncode(err))
		out.Msg = "请求参数格式有误"
		return c.JSON(400, out)
	}
	logStr := "boss-eva-seckill-GetSeckillUPetProductSelectList，入参=" + kit.JsonEncode(p)
	glog.Info(logStr)

	if err = c.Validate(&p); err != nil {
		glog.Error(logStr, " , validate-错误=", err.Error())
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	//组装参数
	if err = utils.MapTo(&p, rpcRqt); err != nil {
		glog.Error(logStr, ", MapTo-错误=", err.Error())
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRqt.OrgId = cast.ToInt32(orgId)

	glog.Info(logStr, ", 数据记录-rpcRqt=", kit.JsonEncode(rpcRqt))

	//调用远程rpc函数
	client := ac.GetActivityCenterClient()
	if rpcRps, err = client.SK.GetSeckillUPetProductSelectList(client.Ctx, rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, " , rpc-错误=", s.Message())
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}

	if rpcRps.Total == 0 {
		return c.JSON(200, out)
	}

	//组装返回数据
	out.Total = rpcRps.Total
	err = utils.MapTo(rpcRps.Data, &out.Data)
	if err != nil {
		glog.Error(logStr, " , MapTo-错误=", err.Error())
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	return c.JSON(200, out)
}

// @Summary 异常折扣配置
// @Tags 运营中心
// @Accept json
// @Produce plain
// @Param in body params.SaveSettingReq false " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/setting [post]
func SaveDiscountSetting(c echo.Context) error {
	var in params.SaveSettingReq
	var out params.SettingResponse
	if err := c.Bind(&in); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}
	//校验
	if err := c.Validate(&in); err != nil {
		validateErrList := validate.Translate(err)
		out.Message = validateErrList.One()
		return c.JSON(400, out)
	}

	redisKey := fmt.Sprintf("discount:setting:%v", in.Type)
	Redis := GetRedisConn()

	redisDiscount := Redis.Get(redisKey).Val()
	discount := cast.ToString(in.Discount)
	if redisDiscount == discount {
		out.Message = "设置成功！"
		out.Discount = in.Discount
		return c.JSON(int(out.Code), out)
	}
	var res = Redis.Set(redisKey, discount, -1)
	if _, err := res.Result(); err != nil {
		out.Code = 401
		out.Message = "保存失败！"
		glog.Info("秒杀活动配置保存失败,限制次数设置为%v", in.Discount)
	}
	//更新活动折扣率，相关相关也重置标识
	var sql string
	switch in.Type {
	case GroupType:
		sql = `UPDATE dc_activity.group_buy c LEFT JOIN  dc_activity.group_buy_product p ON c.id = p.gid SET p.mark_discount =0,p.mark_purchase_price=0 WHERE c.status IN (-3,-2,1,2,3) AND p.mark_discount >0`
	case CycleType:
		sql = `UPDATE dc_activity.cycle_buy c  LEFT JOIN dc_activity.cycle_buy_product p ON c.id = p.cid SET p.mark_discount =0,p.mark_purchase_price=0 WHERE c.status IN (-3,-2,0,1) AND p.mark_discount >0`
	case BookbuyType:
		sql = `UPDATE dc_activity.book_buy_product SET mark_discount =0,mark_purchase_price=0 WHERE STATUS IN (-3,-2,0,1) AND mark_discount >0`
	case SeckillType:
		sql = `UPDATE dc_activity.promotion c LEFT JOIN dc_activity.promotion_product p ON c.id = p.promotion_id SET p.mark_discount =0,p.mark_purchase_price=0 WHERE c.status IN (-3,-2,0,1,2) AND p.mark_discount >0`
	case HelpsType:
		sql = `UPDATE datacenter.market_activity a LEFT JOIN datacenter.market_activity_product p ON p.ma_activity_id = a.id SET p.mark_discount =0,p.mark_purchase_price=0 WHERE a.ma_status IN (-3,-2,1) AND p.mark_discount > 0`
	}
	if len(sql) > 0 {
		if _, err := GetDcActivityDBConn().Exec(sql); err != nil {
			out.Message = err.Error()
			out.Code = 500
			return c.JSON(int(out.Code), out)
		}
	}

	out.Message = "设置成功！"
	out.Discount = in.Discount
	return c.JSON(int(out.Code), out)
}

// @Summary 获取促销活动配置
// @Tags 运营中心
// @Accept json
// @Produce plain
// @Param in body params.GetSettingReq false " "
// @Success 200 {object} params.SettingResponse
// @Failure 400 {object} params.SettingResponse
// @Router /boss/market/setting [get]
func GetDiscountSetting(c echo.Context) error {
	var in params.GetSettingReq
	var out params.SettingResponse
	if err := c.Bind(&in); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	//校验
	if err := c.Validate(&in); err != nil {
		validateErrList := validate.Translate(err)
		out.Message = validateErrList.One()
		return c.JSON(400, out)
	}

	redisKey := fmt.Sprintf(DiscountSettingKey, in.Type)
	Redis := GetRedisConn()
	redisDiscount := Redis.Get(redisKey).Val()
	if len(redisDiscount) > 0 {
		out.Message = "获取成功"
		out.Discount = cast.ToFloat64(redisDiscount)
	} else {
		out.Code = 400
		out.Message = "获取失败"
	}
	out.Code = 200
	return c.JSON(int(out.Code), out)
}
