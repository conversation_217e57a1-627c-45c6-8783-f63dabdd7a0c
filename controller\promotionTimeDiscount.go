package controller

import (
	mk "_/proto/mk"
	"net/http"
	"reflect"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// grpc 函数调用
type TimediscountGrpcFuncCall func(grpc *mk.Client) (response interface{}, err error)

// 请求处理
func PromotionTimediscountGrpcProcess(c echo.Context, request interface{}, grpcFun TimediscountGrpcFuncCall) error {
	// 400 错误代码返回
	var badResponse = new(mk.BaseResponse)
	badResponse.Code = mk.Code_parameterError

	//获取请求参数到实体对象
	err := c.Bind(request)
	if err != nil {
		glog.Error(err)
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 获取grpc 链接
	var conn = mk.GetTimeDiscountServiceClient()
	// 关闭链接
	defer conn.Close()
	if conn == nil {
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = "内部Grpc通讯错误"
		return c.JSON(http.StatusBadRequest, badResponse)
	}
	//调用Grpc方法
	response, err := grpcFun(conn)
	if err != nil {
		glog.Error(err)
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 反射查询Grpc响应
	var responseType = reflect.ValueOf(response)
	if responseType.Kind() == reflect.Ptr {
		responseType = responseType.Elem()
	}
	// 查询code 属性
	codePrototy := responseType.FieldByName("Code")
	if codePrototy.Kind() != reflect.Invalid {
		grpcCode := codePrototy.Int()
		// 解析错误代码
		if grpcCode > 400 {
			badResponse.Code = 400
			if grpcCode == int64(mk.Code_queryDbException) {
				badResponse.Error = "查询数据库异常"
			}
			if grpcCode == int64(mk.Code_saveDbException) {
				badResponse.Error = "保存数据库异常"
			}
			if grpcCode == int64(mk.Code_businessError) {
				// 是否有Message 信息
				var messageFiled = codePrototy.FieldByName("Message")
				if messageFiled.Kind() != reflect.Invalid {
					badResponse.Error = messageFiled.String()
				}
			}
			return c.JSON(http.StatusBadRequest, badResponse)
		}
	}

	return c.JSON(http.StatusOK, response)
}

/////////////////////////////////////////////////////// Query //////////////////////////////////////////////////////////////////////////////////

// QueryPromotionProducByQuery
// @Summary 限时折扣--获取限时折扣活动列表
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.TimeDiscountProductRequest true "查询参数组合"
// @Success 200 {object} mk.TimeDiscountProductResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryPromotionProducByQuery [Post]
func QueryPromotionProducByQuery(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.TimeDiscountProductRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.QueryPromotionProducByQuery(grpc.Ctx, request)
	})
}

// QueryPromotionProducById
// @Summary 限时折扣--根据活动Id查询活动信息(编辑活动信息前获取)
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request query mk.QueryByIdRequest true "查询参数组合"
// @Success 200 {object} mk.TimeDiscountByIdResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryPromotionProducById [get]
func QueryPromotionProducById(c echo.Context) error {
	var request = new(mk.QueryByIdRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.QueryById(grpc.Ctx, request)
	})
}

//////////////////////////////////////////////////////////// Command //////////////////////////////////////////////////////////////////////////////

// @Summary 限时折扣--更新活动信息
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.TimeDiscountUpdateRequest true "营销活动相关"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/UpdateTimeDiscountPromotion [post]
func UpdateTimeDiscountPromotion(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.TimeDiscountUpdateRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.Update(grpc.Ctx, request)
	})
}

// @Summary 限时折扣--删除店铺和活动的关联关系（根据条件删除）
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.TimeDiscountProductRequest true "商品和活动的关联关系id列表"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/DeleteTimeDiscountProductByQuery [Post]
func DeleteTimeDiscountProductByQuery(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.TimeDiscountProductRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.DeletePromotionProducByQuery(grpc.Ctx, request)
	})
}

// @Summary 限时折扣--根据条件批量删除店铺和活动的关联关系（根据ids删除）
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.DeleteRequest true "满足条件的查询语句"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/DeleteTimeDiscountProductByShopIds [Post]
func DeleteTimeDiscountProductByShopIds(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.DeleteRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.DeletePromotionProducById(grpc.Ctx, request)
	})
}

// @Summary 限时折扣--获取全局信息
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.GetPromotionConfigurationDetailRequest true "营销活动相关"
// @Success 200 {object} mk.GetPromotionConfigurationDetailResponse
// @Success 400 {object} mk.GetPromotionConfigurationDetailResponse
// @Router /boss/Promotion/GetPromotionConfigurationDetail [Get]
func GetPromotionConfigurationDetail(c echo.Context) error {
	var request = new(mk.GetPromotionConfigurationDetailRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.GetPromotionConfigurationDetail(grpc.Ctx, request)
	})
}

// @Summary 限时折扣--创建全局配置
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.PromotionConfigurationCreateInfoRequest true "营销活动相关"
// @Success 200 {object} mk.PromotionConfigurationCreateInfoResponse
// @Success 400 {object} mk.PromotionConfigurationCreateInfoResponse
// @Router /boss/Promotion/PromotionConfigurationCreateInfo [Post]
func PromotionConfigurationCreateInfo(c echo.Context) error {
	var request = new(mk.PromotionConfigurationCreateInfoRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.PromotionConfigurationCreateInfo(grpc.Ctx, request)
	})
}

// @Summary 限时折扣--更新全局配置
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.PromotionConfigurationUpdateInfoRequest true "营销活动相关"
// @Success 200 {object} mk.PromotionConfigurationUpdateInfoResponse
// @Success 400 {object} mk.PromotionConfigurationUpdateInfoResponse
// @Router /boss/Promotion/PromotionConfigurationUpdateInfo [Post]
func PromotionConfigurationUpdateInfo(c echo.Context) error {
	var request = new(mk.PromotionConfigurationUpdateInfoRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.PromotionConfigurationUpdateInfo(grpc.Ctx, request)
	})
}

// @Summary 限时折扣--自定义判断全局配置
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.PromotionConfigurationReplaceInfoRequest true "营销活动相关"
// @Success 200 {object} mk.PromotionConfigurationReplaceInfoResponse
// @Success 400 {object} mk.PromotionConfigurationReplaceInfoResponse
// @Router /boss/Promotion/PromotionConfigurationReplaceInfo  [Post]
func PromotionConfigurationReplaceInfo(c echo.Context) error {
	var request = new(mk.PromotionConfigurationReplaceInfoRequest)
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.TimeDiscount.PromotionConfigurationReplaceInfo(grpc.Ctx, request)
	})
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// BatchAddTimediscountPromotion
// @Summary 限时折扣--批量新建（重构）
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.PromotionTimeDiscountAddRequest true "营销活动相关"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/BatchAddTimediscountPromotion [post]
func BatchAddTimediscountPromotion(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.PromotionTimeDiscountAddRequest)
	client := GetDcProductClient(c)
	defer client.Close()
	// 调用封装的函数
	return PromotionTimediscountGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {

		if len(request.PromotionShop) == 0 {
			return &mk.BaseResponse{Code: mk.Code_businessError, Message: "请选择活动涉及店铺信息"}, nil
		}
		if len(request.AddParams) == 0 {
			return &mk.BaseResponse{Code: mk.Code_businessError, Message: "请选择活动涉及商品信息"}, nil
		}

		for i := 0; i < len(request.AddParams); i++ {
			request.AddParams[i].Promotion.Types = mk.PromotionTypes_timeDiscount
		}

		return grpc.TimeDiscount.Add(grpc.Ctx, request)
	})
}
