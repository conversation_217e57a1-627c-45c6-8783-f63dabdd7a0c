package controller

import (
	"_/models"
	"_/proto/dac"
	"_/proto/oc"
	"_/utils"
	"context"
	"encoding/json"
	"path"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
	kit "github.com/tricobbler/rp-kit"
)

// @Summary 获取门店数据本地和配置
// @Tags 门店信息
// @Accept json
// @Produce json
// @Param model body dac.ShopSetListRequest true " "
// @Success 200 {object} dac.ShopSetListResponse
// @Failure 400 {object} dac.ShopSetListResponse
// @Router /boss/shopsetcontroller/ShopSetList [POST]
func ShopSetList(c echo.Context) error {

	glog.Info("获取门店配置分页数据")
	model := new(dac.ShopSetListRequest)
	var res dac.ShopSetListResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetDataCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	grpcRes, err := client.RPC.ShopSetList(client.Ctx, model)
	if err != nil || grpcRes.Code != 200 {

		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("获取门店配置分页数据:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 添加或修改门店系统配置
// @Tags 门店信息
// @Accept json
// @Produce json
// @Param model body dac.ShopSetAddRequest true " "
// @Success 200 {object}  dac.ShopSetAddResponse
// @Failure 400 {object}  dac.ShopSetAddResponse
// @Router /boss/shopsetcontroller/ShopSetAdd [POST]
func ShopSetAdd(c echo.Context) error {
	glog.Info("添加或修改门店配置")
	model := new(dac.ShopSetAddRequest)
	var res dac.ShopSetAddResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetDataCenterClient(c)
	defer client.Close()

	grpcRes, err := client.RPC.ShopSetAdd(client.Ctx, model)
	if err != nil || grpcRes.Code != 200 {

		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("添加或修改门店配置:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 新增店铺管理-系统设置-订单提醒接口
// @Tags 门店信息
// @Accept json
// @Produce json
// @Param model body dac.RemindSetAddRequest true " "
// @Success 200 {object}  dac.RemindSetResponse
// @Failure 400 {object}  dac.RemindSetResponse
// @Router /boss/shopsetcontroller/RemindSetAdd [POST]
func RemindSetAdd(c echo.Context) error {

	glog.Info("添加或修改门店配置")
	model := new(dac.RemindSetAddRequest)
	var res dac.RemindSetResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetDataCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	grpcRes, err := client.RPC.RemindSetAdd(client.Ctx, model)
	if err != nil || grpcRes.Code != 200 {

		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("添加或修改门店配置:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 新增店铺管理-系统设置-自动接单设置接口
// @Tags 门店信息
// @Accept json
// @Produce json
// @Param model body dac.AutoSetAddRequest true " "
// @Success 200 {object}  dac.AutoSetAddResponse
// @Failure 400 {object}  dac.AutoSetAddResponse
// @Router /boss/shopsetcontroller/AutoSetAdd [POST]
func AutoSetAdd(c echo.Context) error {

	glog.Info("添加或修改门店配置")
	model := new(dac.AutoSetAddRequest)
	var res dac.AutoSetAddResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetDataCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	grpcRes, err := client.RPC.AutoSetAdd(client.Ctx, model)
	if err != nil || grpcRes.Code != 200 {

		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("添加或修改门店配置:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 获取单个门店基础数据
// @Tags 门店信息
// @Param shop_id query string true "门店ID"
// @Success 200 {object}  dac.ShopSetGetResponse
// @Failure 400 {object}  dac.ShopSetGetResponse
// @Router /boss/shopsetcontroller/ShopSetGet [Get]
func ShopSetGet(c echo.Context) error {
	glog.Info("获取单个门店基础数据")
	model := new(dac.ShopSetGetRequest)
	shop_id := c.QueryParam("shop_id")
	model.ShopId = shop_id

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.ShopSetGet(context.Background(), model)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	} else if out.Code != 200 {
		return r.NewHTTPError(400, out.Message)
	}

	return c.JSON(200, out)
}

// @Summary 获取北京门店树形结构
// @Tags 门店信息
// @Accept json
// @Produce plain
// @Success 200 {object} dac.ShopTreeResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/shopsetcontroller/GetBJShopTree [get]
func GetBJShopTree(c echo.Context) error {
	userNo := c.QueryParam("user_no")
	if userNo == "" {
		if claims, err := utils.GetPayloadDirectly(c); err != nil {
			glog.Error(err)
			return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
		} else {
			userNo = cast.ToString(claims["userno"])
		}
	}
	model := new(dac.ShopTreeRequest)
	model.UserNo = userNo

	client := GetDataCenterClient()
	if client == nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: "Failed to connect to data center"})
	}
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.GetStoreTree(context.Background(), model)
	if err != nil || out == nil || out.Code != 200 {
		errMsg := "Unknown error"
		if err != nil {
			errMsg = err.Error()
		}
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: errMsg})
	}
	return c.JSON(200, out)
}

// @Summary 获取店铺配送列表
// @Tags 店铺配送
// @Accept json
// @Produce plain
// @Param finance_code query string true "财务编码"
// @Param store_name query string true "店铺名称"
// @Param status query string true "店铺是否启用 1否 2 是"
// @Param page_size query string true "每页大小"
// @Param page_index query string true "当前页"
// @Success 200 {object} dac.GetStoreDeliveryListResponse
// @Failure 400 {object} dac.GetStoreDeliveryListResponse
// @Router /boss/shop/GetStoreDeliveryList [get]
func GetStoreDeliveryList(c echo.Context) error {
	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()
	model := dac.GetStoreDeliveryListRequest{
		FinanceCode: c.QueryParam("finance_code"),
		Status:      cast.ToInt32(c.QueryParam("status")),
		StoreName:   c.QueryParam("store_name"),
		PageSize:    cast.ToInt32(c.QueryParam("page_size")),
		PageIndex:   cast.ToInt32(c.QueryParam("page_index")),
	}

	out, err := client.RPC.GetStoreDeliveryList(context.Background(), &model)
	if err != nil || out.Code != 200 {
		return c.JSON(400, dac.GetStoreDeliveryListResponse{Code: 400, Message: err.Error()})
	}

	for i, k := range out.Data {
		out.Data[i].Lat = k.SparkLat
		out.Data[i].Lng = k.SparkLng
	}

	return c.JSON(200, out)
}

// @Summary 新增/修改店铺配送
// @Tags 店铺配送
// @Accept json
// @Produce plain
// @Param model body dac.StoreDelivery true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/shop/SetStoreDelivery [post]
func SetStoreDelivery(c echo.Context) error {
	glog.Info("添加或修改店铺配送")
	model := new(dac.StoreDelivery)
	var res dac.BaseResponse
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	if len(model.FinanceCode) <= 0 {
		res.Code = 400
		res.Message = "财务编码不能为空"
		return c.JSON(400, res)
	}
	if len(model.ContactPerson) <= 0 {
		res.Code = 400
		res.Message = "联系人不能为空"
		return c.JSON(400, res)
	}
	if len(model.ContactInformation) <= 0 {
		res.Code = 400
		res.Message = "联系方式不能为空"
		return c.JSON(400, res)
	}
	if len(model.StoreName) <= 0 {
		res.Code = 400
		res.Message = "店铺名称不能为空"
		return c.JSON(400, res)
	}
	if len(model.StoreAddress) <= 0 {
		res.Code = 400
		res.Message = "店铺地址不能为空"
		return c.JSON(400, res)
	}
	if len(model.Lng) <= 0 || len(model.Lat) <= 0 {
		res.Code = 400
		res.Message = "店铺经纬度不能为空"
		return c.JSON(400, res)
	}
	if len(model.City) <= 0 {
		res.Code = 400
		res.Message = "城市不能为空"
		return c.JSON(400, res)
	}

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.SetStoreDelivery(context.Background(), model)
	if err != nil || out.Code != 200 {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(200, out)
}

// @Summary 新增/修改店铺配送
// @Tags 店铺配送
// @Accept json
// @Produce plain
// @Param model body dac.StoreDelivery true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/shop/SetStoreDeliveryStatus [post]
func SetStoreDeliveryStatus(c echo.Context) error {
	glog.Info("修改店铺配送开启状态")
	model := new(dac.StoreDelivery)
	var res dac.BaseResponse
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if len(model.FinanceCode) <= 0 {
		res.Code = 400
		res.Message = "财务编码不能为空"
		return c.JSON(400, res)
	}

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.SetStoreDeliveryStatus(context.Background(), model)
	if err != nil || out.Code != 200 {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(200, out)
}

// @Summary 批量新增店铺配送
// @Tags 店铺配送
// @Accept json
// @Produce plain
// @Param model body models.BatchTaskRequest true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/shop/BatchStoreDeliveryTask [post]
func BatchStoreDeliveryTask(c echo.Context) error {
	glog.Info("批量新增店铺配送")
	var res dac.BaseResponse

	model := new(models.BatchTaskRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	ext := path.Ext(model.OperationFileUrl)

	if ext != ".xls" && ext != ".xlsx" {
		res.Code = 400
		res.Message = "请上传正确的文件"
		return c.JSON(400, res)
	}
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}
	taskContent := model.TaskContent
	//创建导出任务,在datacenter处理
	err = createOrderExportTask(UserNo, userInfo.UserName, c.Request().RemoteAddr, "", kit.JsonEncode(model), taskContent, model.OperationFileUrl, model.TaskName, 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 获取店铺配送方式列表
// @Tags 店铺配送方式
// @Accept json
// @Produce plain
// @Param model query dac.StoreDeliveryMethodListRequest true " "
// @Success 200 {object} dac.StoreDeliveryMethodListResponse
// @Failure 400 {object} dac.StoreDeliveryMethodListResponse
// @Router /boss/datacenter/shop/delivery-method-list [get]
func GetStoreDeliveryMethodList(c echo.Context) error {
	req := &dac.StoreDeliveryMethodListRequest{
		PageIndex:      cast.ToInt32(c.QueryParam("page_index")),
		PageSize:       cast.ToInt32(c.QueryParam("page_size")),
		DeliveryMethod: cast.ToInt32(c.QueryParam("delivery_method")),
		Bigregion:      c.QueryParam("bigregion"),
		City:           c.QueryParam("city"),
		Keyword:        c.QueryParam("keyword"),
	}
	if req.Bigregion == "全部大区" {
		req.Bigregion = ""
	}
	if req.City == "全部城市" {
		req.City = ""
	}
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	client := dac.GetDataCenterClient()
	if rsp, err := client.RPC.StoreDeliveryMethodList(client.Ctx, req); err != nil {
		return c.JSON(400, &dac.StoreDeliveryMethodListResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(rsp.Code), rsp)
	}
}

// @Summary 设置店铺配送方式
// @Tags 店铺配送方式
// @Accept json
// @Produce plain
// @Param model body dac.SetStoreDeliveryMethodRequest true " "
// @Success 200 {object} dac.SetStoreDeliveryMethodResponse
// @Failure 400 {object} dac.SetStoreDeliveryMethodResponse
// @Router /boss/datacenter/shop/set-delivery-method [post]
func SetStoreDeliveryMethod(c echo.Context) error {
	out := &dac.SetStoreDeliveryMethodResponse{Code: 400}
	params := &dac.SetStoreDeliveryMethodRequest{}
	if err := c.Bind(params); err != nil {
		out.Message = "参数错误，" + err.Error()
		return c.JSON(400, out)
	}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	params.UserNo = userInfo.UserNo
	params.UserName = userInfo.UserName
	params.IpAddr = c.RealIP()
	params.IpLocation = GetIpAddress(params.IpAddr)

	client := dac.GetDataCenterClient()
	rsp, err := client.RPC.SetStoreDeliveryMethod(client.Ctx, params)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	return c.JSON(int(rsp.Code), rsp)
}

// @Summary 获取操作日志
// @Tags 店铺
// @Accept json
// @Produce plain
// @Param model query dac.StoreOperateLogRequest true " "
// @Success 200 {object} dac.StoreOperateLogResponse
// @Failure 400 {object} dac.StoreOperateLogResponse
// @Router /boss/datacenter/shop/operate-logs [get]
func GetStoreOperateLogs(c echo.Context) error {
	out := &dac.StoreOperateLogResponse{Code: 400}
	params := &dac.StoreOperateLogRequest{
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
		SearchAll: cast.ToInt32(c.QueryParam("search_all")),
		Type:      cast.ToInt32(c.QueryParam("type")),
		Keyword:   c.QueryParam("keyword"),
	}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	params.UserNo = userInfo.UserNo
	client := dac.GetDataCenterClient()
	rsp, err := client.RPC.StoreOperateLog(client.Ctx, params)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	return c.JSON(int(rsp.Code), rsp)
}

// @Summary 获取店铺配置的快递公司列表
// @Tags 店铺
// @Accept json
// @Produce plain
// @Param model query dac.StoreExpressListRequest true " "
// @Success 200 {object} dac.StoreExpressListResponse
// @Failure 400 {object} dac.StoreExpressListResponse
// @Router /boss/datacenter/shop/extend-express-list [get]
func StoreExpressList(c echo.Context) error {
	out := &dac.StoreExpressListResponse{Code: 400}
	params := &dac.StoreExpressListRequest{
		FinanceCode: c.QueryParam("finance_code"),
	}
	if len(params.FinanceCode) == 0 {
		out.Message = "参数错误"
		return c.JSON(400, out)
	}
	client := dac.GetDataCenterClient()
	rsp, err := client.RPC.StoreExpressList(client.Ctx, params)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	return c.JSON(int(rsp.Code), rsp)
}

// @Summary 获取店铺大区、城市列表信息
// @Tags 店铺
// @Accept json
// @Produce plain
// @Param model query dac.StoreRegionAndCityOptionsRequest true " "
// @Success 200 {object} dac.StoreRegionAndCityOptionsResponse
// @Failure 400 {object} dac.StoreRegionAndCityOptionsResponse
// @Router /boss/datacenter/shop/regionAndCityOptions [get]
func StoreRegionAndCityOptions(c echo.Context) error {
	out := &dac.StoreRegionAndCityOptionsResponse{Code: 400}
	params := &dac.StoreRegionAndCityOptionsRequest{}
	client := dac.GetDataCenterClient()
	rsp, err := client.RPC.StoreRegionAndCityOptions(client.Ctx, params)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	return c.JSON(int(rsp.Code), rsp)
}
