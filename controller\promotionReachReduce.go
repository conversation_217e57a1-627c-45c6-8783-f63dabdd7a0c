package controller

import (
	mk "_/proto/mk"
	"net/http"
	"reflect"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// grpc 函数调用
type ReachReduceGrpcFuncCall func(grpc *mk.Client) (response interface{}, err error)

// 请求处理
func PromotionReachReduceGrpcProcess(c echo.Context, request interface{}, grpcFun ReachReduceGrpcFuncCall) error {
	// 400 错误代码返回
	var badResponse = new(mk.BaseResponse)
	badResponse.Code = mk.Code_parameterError

	//获取请求参数到实体对象
	err := c.Bind(request)
	if err != nil {
		glog.Error(err)
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 获取grpc 链接
	var conn = mk.GetReachReduceServiceClient()
	// 关闭链接
	defer conn.Close()
	if conn == nil {
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = "内部Grpc通讯错误"
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	//调用Grpc方法
	response, err := grpcFun(conn)
	if err != nil {
		glog.Error(err)
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 反射查询Grpc响应
	var responseType = reflect.ValueOf(response)
	if responseType.Kind() == reflect.Ptr {
		responseType = responseType.Elem()
	}
	// 查询code 属性
	codePrototy := responseType.FieldByName("Code")
	if codePrototy.Kind() != reflect.Invalid {
		grpcCode := codePrototy.Int()
		// 解析错误代码
		if grpcCode > 400 {
			badResponse.Code = 400
			if grpcCode == int64(mk.Code_queryDbException) {
				badResponse.Error = "查询数据库异常"
			}
			if grpcCode == int64(mk.Code_saveDbException) {
				badResponse.Error = "保存数据库异常"
			}
			if grpcCode == int64(mk.Code_businessError) {
				// 是否有Message 信息
				var messageFiled = codePrototy.FieldByName("Message")
				if messageFiled.Kind() != reflect.Invalid {
					badResponse.Error = messageFiled.String()
				}
			}
			return c.JSON(http.StatusBadRequest, badResponse)
		}
	}

	return c.JSON(http.StatusOK, response)
}

/////////////////////////////////////////////////////// Query //////////////////////////////////////////////////////////////////////////////////

// QueryPromotionByShopIds
// @Summary 根据登录用户Id与店铺id列表查询优惠活动列表
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request query mk.ReachReducePromotionShopQuery true "查询参数组合"
// @Success 200 {object} mk.ReachReducePromotionShopResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryPromotionByShopIds [Post]
func QueryReachReduceByShopIds(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.ReachReducePromotionShopQuery)
	// 调用封装的函数
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReachReduce.QueryPromotionShopByShopIds(grpc.Ctx, request)
	})
}

// QueryPromotionById
// @Summary 根据活动Id查询活动信息(编辑活动信息前获取)
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request query mk.QueryByIdRequest true "查询参数组合"
// @Success 200 {object} mk.ReachReduceByIdResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryPromotionById [get]
func QueryReachReduceById(c echo.Context) error {
	var request = new(mk.QueryByIdRequest)
	// 调用封装的函数
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReachReduce.QueryById(grpc.Ctx, request)
	})
}

// QueryPromotionProduct
// @Summary 根据活动Id查询活动参与商品信息(编辑活动信息获取)
// @Tags v4.4
// @Accept json
// @Produce json
// @param request query mk.ReachReduceProductQueryRequest true "查询参数组合"
// @Success 200 {object} mk.ReachReduceProductQueryResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryPromotionProduct [get]
func QueryPromotionProduct(c echo.Context) error {
	var request = new(mk.ReachReduceProductQueryRequest)
	// 调用封装的函数
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReachReduce.QueryPromotionProduct(grpc.Ctx, request)
	})
}

// QueryOptionalSelectPromotionProduct
// @Summary 根据活动Id查询活动可以再参与的商品信息(编辑活动时查询可选的商品信息)
// @Tags v4.4
// @Accept json
// @Produce json
// @param request query mk.ReachReduceProductQueryRequest true "查询参数组合"
// @Success 200 {object} mk.ReachReduceProductQueryResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryOptionalSelectPromotionProduct [get]
func QueryOptionalSelectPromotionProduct(c echo.Context) error {
	var request = new(mk.ReachReduceProductQueryRequest)
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (response interface{}, err error) {
		return grpc.ReachReduce.QueryOptionalSelectPromotionProduct(grpc.Ctx, request)
	})
}

//////////////////////////////////////////////////////////// Command //////////////////////////////////////////////////////////////////////////////

// AddPromotion
// @Summary 新增活动
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.ReachReduceAddRequest true "营销活动相关"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/AddPromotion [post]
func AddPromotion(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.ReachReduceAddRequest)
	// 调用封装的函数
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {

		if len(request.PromotionShop) == 0 {
			return &mk.BaseResponse{Code: mk.Code_businessError, Message: "请选择活动涉及店铺信息"}, nil
		}

		request.Promotion.Types = mk.PromotionTypes_reachReduce
		return grpc.ReachReduce.Add(grpc.Ctx, request)
	})
}

// @Summary 更新活动信息
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.ReachReduceUpdateRequest true "营销活动相关"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/UpdatePromotion [PUT]
func UpdatePromotion(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.ReachReduceUpdateRequest)
	// 调用封装的函数
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReachReduce.Update(grpc.Ctx, request)
	})
}

// @Summary 更改活动状态(撤销)
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.UpdateIsEnableRequest true "活动修改信息"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/UpdatePromotionIsEnable [Put]
func UpdateReachReduceIsEnable(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.UpdateIsEnableRequest)
	// 调用封装的函数
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReachReduce.UpdateIsEnable(grpc.Ctx, request)
	})
}

// @Summary 删除店铺和活动的关联关系
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.ReachReducePromotionShopQuery true "店铺和活动的关联关系id列表"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/DeletePromotionShopByShopIds [Post]
func DeleteReachReduceShopByQuery(c echo.Context) error {
	// 新建具体的请求响应对象
	var request = new(mk.ReachReducePromotionShopQuery)
	// 调用封装的函数
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReachReduce.DeletePromotionShopByQuery(grpc.Ctx, request)
	})
}

// @Summary 根据条件批量删除店铺和活动的关联关系
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.DeletePromotonShopByIds true "满足条件的查询语句"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/DeletePromotionShop [Post]
func DeleteReachReduceShopByShopIds(c echo.Context) error {

	// 新建具体的请求响应对象
	var request = new(mk.DeletePromotonShopByIds)
	// 调用封装的函数
	return PromotionReachReduceGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReachReduce.DeletePromotionShopById(grpc.Ctx, request)
	})
}
