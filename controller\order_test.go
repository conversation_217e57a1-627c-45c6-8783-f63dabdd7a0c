package controller

import (
	"github.com/labstack/echo/v4"
	"testing"
)

func TestOrderGetPartRefundFoods(t *testing.T) {
	type args struct {
		c echo.Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "",
			args: args{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := OrderGetPartRefundFoods(tt.args.c); (err != nil) != tt.wantErr {
				t.Errorf("OrderGetPartRefundFoods() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
