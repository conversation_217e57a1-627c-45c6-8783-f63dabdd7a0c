name: urchin tests

on: [push, pull_request]

permissions:
  contents: read

jobs:
  tests:
    permissions:
      contents: write

    name: "tests"
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: 'script -q -e -c "${{ matrix.shell }} {0}"'

    strategy:
      fail-fast: false
      matrix:
        exclude:
          - shell: sh
            suite: install_script
          - shell: dash
            suite: install_script
          - shell: zsh
            suite: install_script
          - shell: ksh
            suite: install_script
        suite:
          - install_script
          - sourcing
          - slow
          - installation_iojs
        shell:
          - sh
          - bash
          - dash
          - zsh
          # - ksh

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@v2
        with:
          allowed-endpoints:
            github.com:443
            registry.npmjs.org:443
            raw.githubusercontent.com:443
            nodejs.org:443
            iojs.org:443
            azure.archive.ubuntu.com:80
            packages.microsoft.com:443
      - uses: actions/checkout@v4
      - run: sudo apt-get update; sudo apt-get install ${{ matrix.shell }}
        if: matrix.shell == 'zsh' || matrix.shell == 'ksh'
        # zsh (https://github.com/actions/runner-images/issues/264) and ksh are not in the ubuntu image
        shell: bash
      - run: sudo ${{ matrix.shell }} --version 2> /dev/null || dpkg -s ${{ matrix.shell }} 2> /dev/null || which ${{ matrix.shell }}
      - run: curl --version
      - run: wget --version
      - uses: ljharb/actions/node/install@main
        name: 'npm install && version checks'
        with:
          node-version: 'lts/*'
          skip-ls-check: true
      - run: npm ls urchin
      - run: npx which urchin
      - run: env
      - run: make TERM=xterm-256color TEST_SUITE="${{ matrix.suite }}" SHELL="${{ matrix.shell }}" URCHIN="$(npx which urchin)" test-${{ matrix.shell }}

  nvm:
    permissions:
      contents: none
    name: 'all test suites, all shells'
    needs: [tests]
    runs-on: ubuntu-latest
    steps:
      - run: true
