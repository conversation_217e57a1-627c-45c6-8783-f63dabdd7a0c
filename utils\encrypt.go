package utils

import (
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"io"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

func GetErrorString(err interface{}) (msg string) {
	switch err.(type) {
	case string:
		msg = err.(string)
	case int:
		msg = strconv.Itoa(err.(int))
	case error:
		msg = err.(error).Error()
	}
	return
}

func Md5(str string) string {
	h := md5.New()
	io.WriteString(h, str)
	return fmt.Sprintf("%x", h.Sum(nil))
}

//生成随机数字
func GenValidateCode(width int) string {
	numeric := [10]byte{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
	r := len(numeric)
	rand.Seed(time.Now().UnixNano())
	var sb strings.Builder
	for i := 0; i < width; i++ {
		fmt.Fprintf(&sb, "%d", numeric[ rand.Intn(r) ])
	}
	return sb.String()
}

//验证手机号
func VerificationPhone(phone string) bool {
	//reg := `^1([38][0-9]|14[579]|5[^4]|16[6]|7[1-35-8]|9[189])\d{8}$`
	//rgx := regexp.MustCompile(reg)
	//return rgx.MatchString(phone)
	if len(phone) == 11{
		return true
	}else {
		return false
	}
}

//base64解密
func Base64Decrypt(encodeString string)(val string, err error) {
	// 对上面的编码结果进行base64解码
	decodeBytes, err := base64.StdEncoding.DecodeString(encodeString)
	if err != nil {
		 return "",err
	}

	return string(decodeBytes),err
}

//防止有-符号导致转义报错
func Base64RawDecrypt(encodeString string)(val string, err error) {
	// 对上面的编码结果进行base64解码
	decodeBytes, err := base64.RawStdEncoding.DecodeString(encodeString)
	if err != nil {
		return "",err
	}

	return string(decodeBytes),err
}