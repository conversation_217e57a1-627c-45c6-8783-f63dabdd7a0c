package controller

import (
	"_/dto"
	"_/models"
	"_/utils"
	"net/http"
	"strings"
	"time"

	"github.com/ppkg/kit"
	"github.com/ppkg/kit/echo/validate"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @summary 获取风控配置信息
// @tags 风控管理
// @Accept json
// @Produce json
// @success 200 {object} models.RiskManagement
// @failure 400 {object} models.RiskManagement
// @success 200 {object} dto.RiskManagementRes
// @failure 400 {object} dto.RiskManagementRes
// @router /boss/risk/setting [GET]
func RiskSetting(c echo.Context) error {
	out := &dto.RiskManagementRes{Code: 400}
	riskManagement := &models.RiskManagement{}
	_, err := GetDcCustomerDBConn().Table("risk_management").Get(riskManagement)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	Data := dto.RiskManagementData{
		Id:              riskManagement.Id,
		OrderDayMax:     riskManagement.OrderDayMax,
		ReceviveDayMax:  riskManagement.ReceviveDayMax,
		CumulativeTimes: riskManagement.CumulativeTimes,
	}
	out.Code = 200
	out.Data = append(out.Data, &Data)
	return c.JSON(200, out)
}

// @summary 风控配置保存
// @tags 风控管理
// @Accept json
// @Produce json
// @Param param body dto.RiskManagementReq true " "
// @success 200 {object} dto.BaseRes
// @failure 400 {object} dto.BaseRes
// @router /boss/risk/setting [POST]
func SaveRiskSetting(c echo.Context) error {
	out := &dto.BaseRes{Code: 400}
	in := new(dto.RiskManagementReq)
	if err := c.Bind(in); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	//校验
	if err := c.Validate(in); err != nil {
		validateErrList := validate.Translate(err)
		out.Message = validateErrList.One()
		return c.JSON(400, out)
	}
	db := GetDcCustomerDBConn()
	riskManagement := &models.RiskManagement{}
	ok, err := db.Table("risk_management").Get(riskManagement)
	if err != nil {
		return c.JSON(400, err.Error())
	}
	riskManagement.OrderDayMax = in.OrderDayMax
	riskManagement.ReceviveDayMax = in.ReceviveDayMax
	riskManagement.CumulativeTimes = in.CumulativeTimes
	if ok {
		_, err := db.Table("risk_management").Where("id =?", riskManagement.Id).Update(riskManagement)
		if err != nil {
			out.Message = err.Error()
			return c.JSON(400, out)
		}
	} else {
		_, err := db.Table("risk_management").Insert(riskManagement)
		if err != nil {
			out.Message = err.Error()
			return c.JSON(400, out)
		}
	}

	out.Code = 200
	return c.JSON(200, out)
}

// @summary 风控名单列表
// @tags 风控管理
// @Accept json
// @Produce json
// @Param model body dto.RiskUserReq false " "
// @success 200 {object} dto.RiskUserRes
// @failure 400 {object} dto.RiskUserRes
// @router /boss/risk/user [GET]
func RiskUser(c echo.Context) error {
	out := &dto.RiskUserRes{Code: 400}

	req := new(dto.RiskUserReq)
	if err := c.Bind(req); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	q := GetDcCustomerDBConn().Table("risk_user")
	if req.Status != "" {
		q.Where("status = ?", req.Status)
	}
	req.Mobile = strings.TrimSpace(req.Mobile)
	if req.Mobile != "" {
		q.Where("mobile like ?", "%"+req.Mobile+"%")
	}

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}
	var riskUser []*models.RiskUser
	if count, err := q.OrderBy("id desc").Limit(int(req.PageSize), int(req.PageSize*(req.PageIndex-1))).FindAndCount(&riskUser); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else {
		out.Total = int32(count)
	}

	for _, v := range riskUser {
		if len(v.Mobile) >= 8 {
			v.Mobile = v.Mobile[0:3] + "****" + v.Mobile[7:]
		}
		data := &dto.RiskUser{
			Id:                  v.Id,
			Mobile:              v.Mobile,
			Status:              v.Status,
			Type:                v.Type,
			UserCumulativeTimes: v.UserCumulativeTimes,
			LockTime:            v.LockTime.Format(kit.DATETIME_LAYOUT),
		}
		out.Data = append(out.Data, data)
	}
	out.Code = 200
	return c.JSON(200, out)
}

// @summary 风控名单列表
// @tags 风控管理
// @Accept json
// @Produce json
// @Param model body dto.RiskUserLogReq false " "
// @success 200 {object} dto.RiskUserLogRes
// @failure 400 {object} dto.RiskUserLogRes
// @router /boss/risk/userlog [Get]
func RiskUserLog(c echo.Context) error {
	out := &dto.RiskUserLogRes{Code: 400}

	req := new(dto.RiskUserLogReq)
	if err := c.Bind(req); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	if req.PageIndex < 1 {
		req.PageIndex = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	var riskUserLog []*models.RiskUserLog

	if count, err := GetDcCustomerDBConn().Table("risk_user_log").Where("ruid = ?", req.Ruid).
		OrderBy("id desc").Limit(int(req.PageSize), int(req.PageSize*(req.PageIndex-1))).
		FindAndCount(&riskUserLog); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else {
		out.Total = int32(count)
	}

	for _, v := range riskUserLog {
		data := &dto.RiskUserLog{
			Id:         v.Id,
			Type:       v.Type,
			Ruid:       v.Ruid,
			OpterId:    v.OpterId,
			Opter:      v.Opter,
			Reason:     v.Reason,
			CreateTime: v.CreateTime.Format(kit.DATETIME_LAYOUT),
		}
		out.Data = append(out.Data, data)
	}

	out.Code = 200
	return c.JSON(200, out)
}

// @summary 解封
// @tags 风控管理
// @Accept json
// @Produce json
// @Param model body dto.RiskUnlockReq false " "
// @success 200 {object} dto.BaseRes
// @failure 400 {object} dto.BaseRes
// @router /boss/risk/unlock [POST]
func RiskUnlock(c echo.Context) error {
	out := &dto.BaseRes{Code: 400}
	req := new(dto.RiskUnlockReq)
	if err := c.Bind(req); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		out.Message = "获取操作人信息失败!"
		return c.JSON(http.StatusBadRequest, out)
	}

	riskUser := new(models.RiskUser)
	db := GetDcCustomerDBConn()
	_, err = db.Table("risk_user").Where("id = ?", req.Id).Get(riskUser)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else if riskUser.Id == 0 {
		out.Message = "Id不存在"
		return c.JSON(400, out)
	}

	var desc string
	//更新
	updData := map[string]interface{}{}
	updData["status"] = 0
	updData["unlock_time"] = nil
	// 账号风控解封
	if (riskUser.Type & 1) == 1 {
		updData["last_unlock_time"] = time.Now().Format(kit.DATETIME_LAYOUT)
		desc = "账号风控解封"
	}
	// 收货手机号风控解封
	if (riskUser.Type & 2) == 2 {
		updData["last_receiver_unlock_time"] = time.Now().Format(kit.DATETIME_LAYOUT)
		desc = "收货手机号风控解封"
	}
	if (riskUser.Type & 3) == 3 {
		updData["last_unlock_time"] = time.Now().Format(kit.DATETIME_LAYOUT)
		updData["last_receiver_unlock_time"] = time.Now().Format(kit.DATETIME_LAYOUT)
		desc = "账号跟收货手机号风控解封"
	}
	_, err = db.Table("risk_user").Where("id=?", req.Id).Update(&updData)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	// 添加日志
	if _, err = db.Insert(&dto.RiskUserLog{
		Type:       riskUser.Type,
		Ruid:       riskUser.Id,
		OpterId:    userInfo.UserNo,
		Opter:      userInfo.UserName,
		Reason:     desc,
		CreateTime: time.Now().Format(kit.DATETIME_LAYOUT),
		UpdateTime: time.Now().Format(kit.DATETIME_LAYOUT),
	}); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	out.Code = 200
	return c.JSON(200, out)
}

// @summary 添加白名单
// @tags 风控管理
// @Accept json
// @Produce json
// @Param model body dto.RiskAddReq true " "
// @success 200 {object} dto.BaseRes
// @failure 400 {object} dto.BaseRes
// @router /boss/risk/whitelist [POST]
func AddRiskWhitelist(c echo.Context) error {
	out := &dto.BaseRes{Code: 400}
	req := new(dto.RiskAddReq)
	if err := c.Bind(req); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	//校验
	if err := c.Validate(req); err != nil {
		validateErrList := validate.Translate(err)
		out.Message = validateErrList.One()
		return c.JSON(400, out)
	}
	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		out.Message = "获取操作人信息失败!"
		return c.JSON(http.StatusBadRequest, out)
	}
	db := GetDcCustomerDBConn()

	if has, err := db.Exist(&models.RiskWhitelist{Mobile: req.Mobile, Status: 1}); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else if has {
		out.Message = "手机号已存在，不允许重复添加"
		return c.JSON(400, out)
	}

	if _, err := db.Insert(&models.RiskWhitelist{Status: 1, Mobile: req.Mobile, Opter: userInfo.UserName, OpterId: userInfo.UserNo, Remark: req.Remark}); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	out.Code = 200
	return c.JSON(200, out)
}

// @summary 删除白名单
// @tags 风控管理
// @Accept json
// @Produce json
// @Param model body dto.RiskIdReq false " "
// @success 200 {object} dto.BaseRes
// @failure 400 {object} dto.BaseRes
// @router /boss/risk/whitelist [DELETE]
func DeleteRiskWhitelist(c echo.Context) error {
	out := &dto.BaseRes{Code: 400}
	req := &dto.RiskIdReq{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	db := GetDcCustomerDBConn()
	_, err := db.Table("risk_whitelist").Where("id=?", req.Id).Update(map[string]interface{}{"status": 0})
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	out.Code = 200
	return c.JSON(200, out)
}

// @summary 风控白名单列表
// @tags 风控管理
// @Accept json
// @Produce json
// @Param model body dto.RiskWhiteReq false " "
// @success 200 {object} dto.RiskWhiteRes
// @failure 400 {object} dto.RiskWhiteRes
// @router /boss/risk/whitelist [GET]
func RiskWhitelist(c echo.Context) error {
	out := &dto.RiskWhiteRes{Code: 400}
	req := new(dto.RiskWhiteReq)
	if err := c.Bind(req); err != nil {
		out.Message = "参数错误"
		return c.JSON(200, out)
	}
	if req.PageIndex == 0 {
		req.PageIndex = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 15
	}
	var riskWhitelist []*models.RiskWhitelist
	db := GetDcCustomerDBConn()

	if count, err := db.Table("risk_whitelist").Where("status = 1").OrderBy("id desc").
		Limit(int(req.PageSize), int(req.PageSize*(req.PageIndex-1))).FindAndCount(&riskWhitelist); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else {
		out.Total = int32(count)
	}
	for _, v := range riskWhitelist {
		if len(v.Mobile) >= 8 {
			v.Mobile = v.Mobile[0:3] + "****" + v.Mobile[7:]
		}
		data := &dto.RiskWhiteList{
			Id:         v.Id,
			Mobile:     v.Mobile,
			Status:     v.Status,
			OpterId:    v.OpterId,
			Opter:      v.Opter,
			Remark:     v.Remark,
			CreateTime: v.CreateTime.Format(kit.DATETIME_LAYOUT),
			UpdateTime: v.UpdateTime.Format(kit.DATETIME_LAYOUT),
		}
		out.Data = append(out.Data, data)
	}
	out.Code = 200
	return c.JSON(200, out)
}
