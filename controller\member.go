package controller

import (
	"_/dto"
	"_/proto/cc"
	"_/proto/mm"
	"regexp"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @Summary 会员等级列表
// @Tags 会员中心
// @Accept json
// @Produce json
// @Param page_index query int true "当前页"
// @Param page_size query int true "每页显示条数"
// @Success 200 {object} cc.UserLevelListRes
// @Failure 400 {object} cc.UserLevelListRes
// @Router /boss/member/level/list [get]
func UserLevelList(c echo.Context) error {
	pageIndex := cast.ToInt64(c.QueryParam("page_index"))
	pageSize := cast.ToInt64(c.QueryParam("page_size"))
	if pageIndex < 1 {
		pageIndex = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	client := cc.GetCustomerCenterClient()
	re, err := client.RPC.UserLevelList(client.Ctx, &cc.UserLevelListReq{
		PageIndex: pageIndex,
		PageSize:  pageSize,
	})
	if err != nil {
		return c.JSON(400, err.Error())
	}
	if re.Code != 200 {
		return c.JSON(400, re)
	}

	return c.JSON(200, re)
}

// @Summary 会员等级启用、停用
// @Tags 会员中心
// @Accept json
// @Produce json
// @Param model body cc.UserLevelSetReq true " "
// @Success 200 {object} cc.Response
// @Failure 400 {object} cc.Response
// @Router /boss/member/level/set [post]
func UserLevelSet(c echo.Context) error {
	p := &cc.UserLevelSetReq{}
	if err := c.Bind(p); err != nil {
		return c.JSON(400, "参数错误："+err.Error())
	}
	if p.Id <= 0 {
		return c.JSON(400, "id不能为空")
	}
	if p.Status != 1 && p.Status != 0 {
		return c.JSON(400, "status参数错误")
	}

	client := cc.GetCustomerCenterClient()
	re, err := client.RPC.UserLevelSet(client.Ctx, &cc.UserLevelSetReq{
		Id:     p.Id,
		Status: p.Status,
	})
	if err != nil {
		return c.JSON(400, err.Error())
	}
	if re.Code != 200 {
		return c.JSON(400, re)
	}
	return c.JSON(200, re)
}

// @Summary 获取会员等级编辑项
// @Tags 会员中心
// @Accept json
// @Produce json
// @Success 200 {object} cc.UserEditEquityListRes
// @Failure 400 {object} cc.UserEditEquityListRes
// @Router /boss/member/level/equity-list [get]
func GetUserEditEquityList(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	re, err := client.RPC.UserEditEquityList(client.Ctx, &cc.EmptyReq{})
	if err != nil {
		return c.JSON(400, err.Error())
	}
	if re.Code != 200 {
		return c.JSON(400, re)
	}
	return c.JSON(200, re)
}

// @Summary 获取会员关联的权益
// @Tags 会员中心
// @Accept json
// @Produce json
// @Param level_id query int true "会员等级id"
// @Success 200 {object} cc.UserLevelDetailRes
// @Failure 400 {object} cc.UserLevelDetailRes
// @Router /boss/member/level/equity-detail [get]
func GetUserEditEquityDetail(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	levelId := cast.ToInt64(c.QueryParam("level_id"))
	re, err := client.RPC.UserLevelDetail(client.Ctx, &cc.UserLevelDetailReq{UserLevelId: levelId})
	if err != nil {
		return c.JSON(400, err.Error())
	}
	if re.Code != 200 {
		return c.JSON(400, re)
	}
	return c.JSON(200, re)
}

// @Summary 会员等级编辑
// @Tags 会员中心
// @Accept json
// @Produce json
// @Param model body cc.UserLevelEditReq true " "
// @Success 200 {object} cc.Response
// @Failure 400 {object} cc.Response
// @Router /boss/member/level/edit [post]
func UserLevelEdit(c echo.Context) error {
	p := &cc.UserLevelEditReq{}
	if err := c.Bind(p); err != nil {
		return c.JSON(400, "参数错误："+err.Error())
	}

	client := cc.GetCustomerCenterClient()
	re, err := client.RPC.UserLevelEdit(client.Ctx, p)
	if err != nil {
		return c.JSON(400, err.Error())
	}
	if re.Code != 200 {
		return c.JSON(400, re)
	}
	return c.JSON(200, re)
}

// @Summary 更新会员等级
// @Tags 会员中心
// @Accept json
// @Produce json
// @Param model body cc.EmptyReq true " "
// @Success 200 {object} cc.Response
// @Failure 400 {object} cc.Response
// @Router /boss/member/level/refresh [post]
func RefreshUserLevel(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	re, err := client.RPC.RefreshUserLevel(client.Ctx, &cc.EmptyReq{})
	if err != nil {
		return c.JSON(400, err.Error())
	}
	if re.Code != 200 {
		return c.JSON(400, re)
	}
	return c.JSON(200, re)
}

// @Summary 用户合并记录查询
// @Tags 用户合并记录
// @Accept json
// @Produce plain
// @Param member_phone query int true "查询手机号"
// @Success 200 {object} mm.MemberMergeResponse
// @Failure 400 {object} mm.MemberMergeErrorResponse
// @Router /boss/member/merge [get]
func MemberMerge(c echo.Context) error {
	out := mm.MemberMergeResponse{Code: 400}
	member_phone := c.QueryParam("member_phone")
	result, _ := regexp.MatchString(`^(1[3|4|5|6|7|9|8][0-9]\d{4,8})$`, member_phone)
	if result == false {
		out.Message = "请输入正确手机号"
		return c.JSON(400, out)
	}

	client := GetUpetMemberCenter()
	defer client.Conn.Close()
	defer client.Cf()
	res, err := client.RPC.MemberMergeSearchList(client.Ctx, &mm.MemberMergeRequest{
		MemberPhone: member_phone,
	})
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(int(res.Code), res)
	}
}

// @Summary 任务列表
// @Tags 会员中心
// @Param TaskList body cc.TaskListReq true " "
// @Success 200 {object} cc.TaskListRes
// @Router /boss/member/task/list [get]
func TaskList(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	req := cc.TaskListReq{}
	if out, err := client.RPC.TaskList(client.Ctx, &req); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 任务保存
// @Tags 会员中心
// @Param TaskSave body cc.TaskSaveReq true " "
// @Success 200 {object} cc.BaseResponseNew
// @Router /boss/member/task/save [post]
func TaskSave(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	model := new(cc.TaskSaveReq)
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &cc.BaseResponseNew{Msg: err.Error()})
	}
	if out, err := client.RPC.TaskSave(client.Ctx, model); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 会员权益列表
// @Tags 会员中心
// @Param MemberEquityList body cc.MemberEquityListReq true " "
// @Success 200 {object} cc.MemberEquityListRes
// @Router /boss/member/equity/list [get]
func MemberEquityList(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	equityName := c.QueryParam("equity_name")
	isDisplay := cast.ToInt32(c.QueryParam("is_display"))
	pageSize := cast.ToInt32(c.QueryParam("page_size"))
	pageIndex := cast.ToInt32(c.QueryParam("page_index"))

	req := cc.MemberEquityListReq{
		EquityName: equityName,
		PageIndex:  pageIndex,
		PageSize:   pageSize,
		IsDisplay:  isDisplay,
	}
	if out, err := client.RPC.MemberEquityList(client.Ctx, &req); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 会员权益编辑
// @Tags 会员中心
// @Param MemberEquityEdit body cc.MemberEquityEditReq true " "
// @Success 200 {object} cc.BaseResponseNew
// @Router /boss/member/equity/edit [post]
func MemberEquityEdit(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	defer client.Close()
	out := new(cc.BaseResponseNew)

	model := new(cc.MemberEquityEditReq)
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &cc.BaseResponseNew{Msg: err.Error()})
	}

	if model.Id == 0 || model.EquityName == "" || model.EquityInfo == "" || model.EquityIcon == "" {
		return c.JSON(400, &cc.BaseResponseNew{Msg: "参数错误"})
	}

	if _, err := client.RPC.MemberEquityEdit(client.Ctx, model); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 会员权益详情
// @Tags 会员中心
// @Param MemberEquityDetail body cc.MemberEquityDetailReq true " "
// @Success 200 {object} cc.MemberEquityDetailRes
// @Router /boss/member/equity/detail [get]
func MemberEquityDetail(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	id := cast.ToInt32(c.QueryParam("id"))

	if id <= 0 {
		return c.JSON(400, &cc.MemberEquityDetailRes{Msg: "参数错误"})
	}

	req := cc.MemberEquityDetailReq{
		Id: id,
	}
	if out, err := client.RPC.MemberEquityDetail(client.Ctx, &req); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}
}

// MemberCounts
// @Summary 会员统计概况
// @Tags 会员
// @Accept json
// @Produce plain
// @Param begin_time query string true "开始时间"
// @Param end_time query string true "结束时间"
// @Success 200 {object} dto.MemberCountsRes
// @Failure 400 {object} dto.MemberCountsRes
// @Router /boss/member/counts [get]
func MemberCounts(c echo.Context) error {
	out := new(dto.MemberCountsRes)
	client := cc.GetCustomerCenterClient()
	defer client.Close()
	res, err := client.User.MemberCounts(client.Ctx, &cc.MemberCountsRequest{
		EndTime: c.QueryParam("end_time"),
		BeginTime: c.QueryParam("begin_time"),
	})
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	out.Data = res.Data
	for _, i := range out.Data {
		out.UserUpgradeNumTotal += i.UserUpgradeNum
		out.UserDowngradeNumTotal += i.UserDowngradeNum
	}
	return c.JSON(200, out)
}

// @Summary 会员权益显示或隐藏
// @Tags 会员中心
// @Param MemberEquitySet body cc.MemberEquitySetReq true " "
// @Success 200 {object} cc.BaseResponseNew
// @Router /boss/member/equity/set [post]
func MemberEquitySet(c echo.Context) error {
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	model := new(cc.MemberEquitySetReq)
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &cc.BaseResponseNew{Msg: err.Error()})
	}

	if model.IsDisplay != 0 && model.IsDisplay != 1 {
		return c.JSON(400, &cc.BaseResponseNew{Msg: "参数错误"})
	}

	if out, err := client.RPC.MemberEquitySet(client.Ctx, model); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}
}
