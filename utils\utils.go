package utils

import (
	"_/models"
	"_/proto/sv"
	"bytes"
	"context"
	"encoding/json"
	"io"
	"math/rand"
	"mime/multipart"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/metadata"
)

var (
	effectiveTemplate = []string{"A8编码", "仓库ID", "入库单号", "入库数量", "入库时间(YYYY-MM-DD HH:MM:SS)",
		"生产日期(YYYY-MM-DD)", "失效日期(YYYY-MM-DD)", "生产批次"}
	ExpiryTemplate            = [][]string{effectiveTemplate, []string{"A8S00241AYDL", "22", "单号1", "10", "2021-07-22 09:26:05", "2021-07-22", "2021-10-04", "批次1"}}
	SafePrecautionTemplate    = [][]string{[]string{"A8编码", "安全库存数量", "安全库存周转天数"}}
	HighStockTemplate         = [][]string{[]string{"A8编码", "滞销库存数量", "滞销库存周转天数"}}
	MinimumOrderTemplate      = [][]string{[]string{"A8编码", "最小补货量"}}
	ReplenishmentRateTemplate = [][]string{[]string{"A8编码", "补货系数"}}
	SheetDefault              = "Sheet1"

	AgencyTemplate = [][]string{[]string{"商品SKUID", "商品名称"}}
)

// InterfaceToJSON interface 转化成 json 字符串
func InterfaceToJSON(req interface{}) string {
	reqByte, _ := json.Marshal(req)
	return string(reqByte)
}

// 请求header添加到grpc上下文
func AppendToOutgoingContextLoginUserInfo(ctx context.Context, c ...echo.Context) context.Context {
	if len(c) == 0 {
		return context.Background()
	}

	grpcContext := models.GrpcContext{}

	//if claims, err := GetPayloadDirectly(c[0]); err != nil {
	//	//TODO 注释，错误频繁
	//	//glog.Error(err)
	//} else {
	//	grpcContext.UserInfo.UserNo = InterfaceToString(claims["userno"])
	//}
	//
	//ctx = metadata.AppendToOutgoingContext(ctx, "login_user_info", kit.JsonEncode(grpcContext.UserInfo))

	//channel,useragent
	grpcContext.Channel.ChannelId = cast.ToInt(c[0].Request().Header.Get("channel_id"))
	grpcContext.Channel.UserAgent = cast.ToInt(c[0].Request().Header.Get("user_agent"))
	grpcContext.UserInfo.UserNo = c[0].Request().Header.Get("userNo")
	grpcContext.UserInfo.RealName = c[0].Request().Header.Get("userRealName")

	return metadata.AppendToOutgoingContext(ctx, "grpc_context", kit.JsonEncode(grpcContext))
}

// AppendUserToOutgoingContext 通过token解析用户信息到ctx中
func AppendUserToOutgoingContext(ctx context.Context, c echo.Context) (context.Context, error) {
	userInfo, err := GetPayloadDirectlyToInterface(c)
	if err != nil {
		return ctx, err
	}
	ctx = metadata.AppendToOutgoingContext(ctx,
		"user-name", userInfo.UserName,
		"user-no", userInfo.UserNo,
	)
	return ctx, nil
}

// 模板excle下载
/**
sheetName: 表格的名称，默认为sheet1,传递空""
fileNme: 输出的文件名称 默认为sc_stock
header： 文件的头部模板
*/
func ExportExcel(c echo.Context, sheetName string, fileNme string, header [][]string) error {
	f := excelize.NewFile()
	if len(header) <= 0 {
		header = make([][]string, 0)
	}

	var index int
	if len(strings.TrimSpace(sheetName)) > 0 {
		index = f.NewSheet(sheetName)
		f.DeleteSheet(SheetDefault)
		f.SetActiveSheet(index)
	} else {
		sheetName = SheetDefault
	}

	for i, list := range header {
		for j, v := range list {
			f.SetCellValue("Sheet1", string(rune(65+j))+strconv.Itoa(i+1), v)
		}
	}

	var buff bytes.Buffer
	if err := f.Write(&buff); err != nil {
		return c.JSON(400, &models.BaseResponseNew{Msg: "导出文件失败"})
	}
	if len(strings.TrimSpace(fileNme)) <= 0 {
		fileNme = "sc_stock"
	}
	fileEncode := url.QueryEscape(fileNme)
	fileName := fileEncode + time.Now().Format("20060102150405") + ".xlsx"
	c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)

	return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))

}

// 读取excle数据
/**
file: multipart.FileHeader
sheetName：表格名称为空的话""  默认读取Sheet1
maxLen: 限制导入的条数 0的话默认不做限制
*/
func ReadExcelData(file *multipart.FileHeader, sheetName string, maxLen int) ([][]string, error) {

	src, err := file.Open()
	if err != nil {
		return [][]string{}, err
	}
	defer src.Close()

	if len(strings.TrimSpace(sheetName)) <= 0 {
		sheetName = SheetDefault // 默认读取sheet1
	}
	f, err := excelize.OpenReader(src)
	value := f.GetRows(sheetName)
	lenData := len(value)
	glog.Info("ReadExcleData len: ", lenData)
	if lenData-1 <= 0 {
		msg := "数据为空，请填写数据."
		glog.Error(msg)
		return [][]string{}, errors.New(msg)
	}
	if maxLen > 0 {
		if lenData-1 > maxLen {
			msg := "导入数据不能超过" + cast.ToString(maxLen) + "行。"
			glog.Error("maxLen: ", msg)
			return [][]string{}, errors.New(msg)
		}
	}

	return value, nil

}

// 校验模板数据正确
/***
通过模板头部校验excle的头部信息和模板的头部信息是否一样
excleHeader: 读取的excle头部
templateHeader： 模板的头部信息
*/
func CheckExcelTemplate(excleHeader []string, templateHeader []string) bool {
	glog.Info("CheckExcleTemplate: ", kit.JsonEncode(excleHeader), " 模板：", kit.JsonEncode(templateHeader))
	if (excleHeader == nil) != (templateHeader == nil) {
		return false
	}

	if len(excleHeader) != len(templateHeader) {
		return false
	}

	for i := range excleHeader {
		if excleHeader[i] != templateHeader[i] {
			return false
		}
	}

	return true
}

// 读取数据到结构体
func ReadExcelToStruct(excelData [][]string) error {
	glog.Info("ReadExcleToStruct: ", kit.JsonEncode(excelData))

	return nil
}

// 读取excle的数据返回
func ReadExcelToEffective(excelData [][]string, typeData int, warehouseId int) (interface{}, []map[string]string, int, error) {
	glog.Info("ReadExcleToEffective: ", kit.JsonEncode(excelData))
	trimHeader := excelData[1:] // 去除头部信息

	errList := make([]map[string]string, 0)
	mapThird := make(map[string]int32, 0)
	if typeData == 1 { // 返回效期的数据 todo 效期的数据改成异步导入，这里已经不做处理直接忽略

		responses := make([]sv.EffectiveManagementResponse, 0)
		for _, v := range trimHeader {
			res := sv.EffectiveManagementResponse{}
			//res.SkuId = v[0]
			res.ThirdSkuId = v[1]
			//res.ProductName = "效期测试商品1"
			//res.WarehouseId = cast.ToInt32(v[2])
			//res.StorageOrder = v[3]
			//res.StorageStock = cast.ToInt32(v[4])
			//res.StorageDate = v[5]
			//res.ProductionDate = v[6]
			//res.ExpirationDate = v[7]
			//res.ProductionBatch = v[8]
			responses = append(responses, res)
		}
		return responses, errList, len(responses), nil
	} else { // 返回商品的数据
		responses := make([]*sv.ProductRuleConfigurationResponse, 0)
		errs := make([]map[string]string, 0)
		for _, v := range trimHeader {
			res := sv.ProductRuleConfigurationResponse{}
			if len(strings.TrimSpace(v[0])) == 0 {
				errs = append(errs, map[string]string{"msg": "导入的数据A8不能为空"})
				continue
			}
			//res.SkuId = v[0]
			res.ThirdSkuId = v[0]
			res.Status = 1
			if typeData == 2 {
				res.Type = 1
				i, i2 := cast.ToInt32E(v[1])
				if i2 != nil {
					errs = append(errs, map[string]string{"msg": res.ThirdSkuId + " 安全库存数量只能为数字"})
					continue
				}
				if i <= 0 {
					errs = append(errs, map[string]string{"msg": res.ThirdSkuId + " 安全库存数量只能大于0"})
					continue
				}
				res.SafeStock = i
				e, i2 := cast.ToInt32E(v[2])
				if i2 != nil {
					errs = append(errs, map[string]string{"msg": res.ThirdSkuId + "安全库存周转天数只能为数字"})
					continue
				}
				if e <= 0 {
					errs = append(errs, map[string]string{"msg": res.ThirdSkuId + " 安全库存周转天数只能大于0"})
					continue
				}
				res.SafeDays = e
			} else if typeData == 3 {
				res.Type = 2
				i, i2 := cast.ToInt32E(v[1])
				if i2 != nil {
					errs = append(errs, map[string]string{"msg": "滞销库存数量只能为数字"})
					continue
				}
				if i <= 0 {
					errs = append(errs, map[string]string{"msg": res.ThirdSkuId + " 滞销库存数量只能大于0"})
					continue
				}
				res.DeadStock = i
				e, i2 := cast.ToInt32E(v[2])
				if i2 != nil {
					errs = append(errs, map[string]string{"msg": "滞销库存周转天数只能为数字"})
					continue
				}
				if e <= 0 {
					errs = append(errs, map[string]string{"msg": res.ThirdSkuId + " 滞销库存周转天数只能大于0"})
					continue
				}
				res.DeadStockDays = e
			} else if typeData == 4 {
				res.Type = 3
				i, i2 := cast.ToInt32E(v[1])
				if i2 != nil {
					errs = append(errs, map[string]string{"msg": "最小补货只能为数字"})
					continue
				}
				if i <= 0 {
					errs = append(errs, map[string]string{"msg": res.ThirdSkuId + " 最小补货只能大于0"})
					continue
				}
				res.MinimumOrderQuantity = i
			} else if typeData == 5 {
				//表格数据去重
				third := v[0]
				if _, ok := mapThird[third]; ok {
					continue
				} else {
					mapThird[third] = 1
				}

				res.Type = 4
				i, i2 := cast.ToFloat32E(v[1])
				if i2 != nil {
					errs = append(errs, map[string]string{"msg": "补货系数只能为小数"})
					continue
				}
				if i < 0 {
					errs = append(errs, map[string]string{"msg": res.ThirdSkuId + " 补货系数只能是0-2"})
					continue
				}
				res.ReplenishmentRate = i
			}
			responses = append(responses, &res)
		}

		client := sv.GetStockVisualClient()
		defer client.Close()

		vo := sv.ProductResVo{
			Data: responses,
		}
		response, err := client.RPC.CheckProductRule(client.Ctx, &vo)
		if err != nil {
			glog.Error("client.RPC.CheckProductRule(client.Ctx, &vo)", err.Error())
			return responses, errList, len(responses), err
		}

		responsesNew := make([]*sv.ProductRuleConfigurationResponse, 0)
		if len(response.Data) > 0 {
			for _, v := range response.Data {
				SkuId := v.SkuId
				ThirdSkuId := v.ThirdSkuId
				for _, vto := range responses {
					if ThirdSkuId == vto.ThirdSkuId {
						vto.SkuId = SkuId
						vto.WarehouseId = int32(warehouseId)
						vto.ThirdSkuId = ThirdSkuId
						vto.ProductName = v.ProductName
						responsesNew = append(responsesNew, vto)
					}
				}
			}
		}
		_ = json.Unmarshal([]byte(response.Msg), &errList)
		errList = append(errList, errs...)
		return responsesNew, errList, len(responsesNew), nil
	}

}

// 处理权限仓库
func HasPermissionWarehouse(warehouseId int32, in *sv.AuthInfoDetailResponse) bool {
	warehouseIds := make([]int32, 0)
	for _, v := range in.Data.RegionWarehouse {
		warehouseIds = append(warehouseIds, v.WarehouseId)
	}
	for _, v := range in.Data.PreposeWarehouse {
		warehouseIds = append(warehouseIds, v.WarehouseId)
	}
	glog.Info("所有的仓库权限：", len(warehouseIds), kit.JsonEncode(warehouseIds))
	var isTrue = false
	for _, v := range warehouseIds {
		if warehouseId == v {
			isTrue = true
		}
	}
	return isTrue
}

func JsonToString(data interface{}) string {
	bytes, _ := json.Marshal(data)
	return string(bytes)
}

func DoError(err string) string {
	return strings.ReplaceAll(err, "rpc error: code = Unknown desc = ", "")
}

// 用于文件上传接收转成字节
func AcceptFileToBytes(c echo.Context) (bts []byte, err error) {
	url := c.QueryParam("file")
	// A、url下载
	if strings.HasPrefix(url, "http") {
		bts, err = DownFile(url)
		return
	}

	// B、multipart/form-data上传
	file, _, err := c.Request().FormFile("file")
	if err != nil {
		return
	}
	defer file.Close()

	buf := bytes.NewBuffer(nil)
	if _, err = io.Copy(buf, file); err != nil {
		return
	}
	return buf.Bytes(), nil
}

// 手机号星号代替
func MobileReplaceWithStar(mobile string) string {
	if len(mobile) == 0 {
		return mobile
	}
	if len(mobile) < 8 {
		return mobile + strings.Repeat("*", 11-len(mobile))
	}
	return mobile[0:3] + "****" + mobile[7:]
}

// 随机字符串
func GenerateRandomStr(size int) string {
	iKind, kinds, result := 3, [][]int{{10, 48}, {26, 97}, {26, 65}}, make([]byte, size)
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < size; i++ {
		iKind = rand.Intn(3)
		scope, base := kinds[iKind][0], kinds[iKind][1]
		result[i] = uint8(base + rand.Intn(scope))
	}
	return string(result)
}
