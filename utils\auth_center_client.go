package utils

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gojek/heimdall/v7/httpclient"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
)

type AuthCenterClient struct {
	url        string
	appId      string
	secretCode string
	httpClient *httpclient.Client
}

func NewAuthCenterClient() *AuthCenterClient {
	instance := &AuthCenterClient{
		url:        config.GetString("http.auth-center"),
		appId:      config.GetString("auth-center.appId"),
		secretCode: config.GetString("auth-center.secretCode"),
	}
	instance.httpClient = httpclient.NewClient(
		httpclient.WithHTTPTimeout(30*time.Second),
		httpclient.WithRetryCount(1),
	)
	return instance
}

func (s *AuthCenterClient) GetAccessToken(env ...int) (string, error) {
	biz := make(url.Values)
	if len(env) > 0 {
		biz.Add("env", cast.ToString(env[0]))
	}
	resp, err := s.Get("/auth-center/wechat/get-access-token", biz)
	if err != nil {
		return "", err
	}
	var token string
	err = json.Unmarshal(resp, &token)
	if err != nil {
		glog.Errorf("AuthCenterClient/GetAccessToken 反序列化异常,resp:%s,err:%+v", resp, err)
		return "", err
	}
	return token, nil
}

// 生成签名信息
func (s *AuthCenterClient) generateSignMap(req *http.Request) map[string]string {
	myMap := make(map[string]string, 3)
	timestamp := time.Now().Unix()
	myMap["appId"] = s.appId
	myMap["timestamp"] = cast.ToString(timestamp)

	biz := make(map[string]string)
	s.mergeRequestParams(biz, req.URL.Query())
	s.mergeRequestParams(biz, req.PostForm)

	contentType := req.Header.Get("content-type")
	if strings.HasPrefix(contentType, echo.MIMEApplicationJSON) && req.Body != nil {
		body, _ := ioutil.ReadAll(req.Body)
		req.Body.Close()
		req.Body = ioutil.NopCloser(bytes.NewBuffer(body))
		myMap["sign"] = s.sign(int(timestamp), biz, string(body))
	} else {
		myMap["sign"] = s.sign(int(timestamp), biz)
	}

	return myMap
}

// 合并请求参数
func (s *AuthCenterClient) mergeRequestParams(biz map[string]string, reqParams url.Values) {
	for k, v := range reqParams {
		sort.Strings(v)
		biz[k] = strings.Join(v, ",")
	}
}

// auth-center接口签名生成
func (s *AuthCenterClient) sign(timestamp int, biz map[string]string, requestBody ...string) string {
	if biz == nil {
		biz = make(map[string]string)
	}
	// appId和timestamp参数加入到业务参数map对象
	biz["appId"] = s.appId
	biz["timestamp"] = strconv.FormatInt(int64(timestamp), 10)
	//取出map所有key进行正序排序
	keys := make([]string, 0, len(biz))
	for k := range biz {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	// 按顺序进行拼接字符串
	builder := new(bytes.Buffer)
	for _, k := range keys {
		builder.WriteString(k)
		builder.WriteString(biz[k])
	}
	// 如果content-type是application/json,则需要把请求体(@requestBody)进行拼接,注意：如果@requestBody为null则不需要进行拼接
	if len(requestBody) > 0 {
		builder.WriteString(requestBody[0])
	}
	// 最后拼接secretCode
	builder.WriteString(s.secretCode)
	// 生成md5
	h := md5.New()
	h.Write(builder.Bytes())
	signData := hex.EncodeToString(h.Sum(nil))
	glog.Infof("AuthCenterClient/sign 明文参数:%s,生成签名:%s", builder.String(), signData)
	return signData
}

func (s *AuthCenterClient) doRequest(req *http.Request) ([]byte, error) {
	signMap := s.generateSignMap(req)
	for k, v := range signMap {
		req.Header.Set(k, v)
	}
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	if resp.Body == nil {
		glog.Warningf("AuthCenterClient/doRequest 请求没有数据返回")
		return nil, nil
	}
	defer resp.Body.Close()
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		glog.Errorf("AuthCenterClient/doRequest 读取响应数据异常,err:%+v", err)
		return nil, err
	}
	var respData acCommonResponse
	err = json.Unmarshal(data, &respData)
	if err != nil {
		glog.Errorf("AuthCenterClient/doRequest 反序列化响应数据异常,响应:%s,err:%+v", data, err)
		return nil, err
	}
	if respData.Code != http.StatusOK {
		glog.Errorf("AuthCenterClient/doRequest 请求业务处理失败,响应:%s", data)
		return nil, fmt.Errorf("code:%d,message:%s", respData.Code, respData.Message)
	}
	return respData.Data, nil
}

func (s *AuthCenterClient) PostJson(path string, body interface{}) ([]byte, error) {
	jsonData, err := json.Marshal(body)
	if err != nil {
		glog.Errorf("AuthCenterClient/PostJson 序列化requestBody参数异常,path:%s,body:%v,err:%+v", path, body, err)
		return nil, err
	}
	req, err := http.NewRequest(http.MethodPost, s.url+path, bytes.NewBuffer(jsonData))
	if err != nil {
		glog.Errorf("AuthCenterClient/PostJson 构建请求参数异常,path:%s,body:%s,err:%+v", path, jsonData, err)
		return nil, err
	}
	req.Header.Set("content-type", echo.MIMEApplicationJSONCharsetUTF8)
	result, err := s.doRequest(req)
	if err != nil {
		glog.Errorf("AuthCenterClient/PostJson 请求服务异常,path:%s,body:%s,err:%+v", path, jsonData, err)
		return nil, err
	}
	return result, nil
}

func (s *AuthCenterClient) Get(path string, params url.Values) ([]byte, error) {
	urlObj, err := url.Parse(s.url + path)
	if err != nil {
		glog.Errorf("AuthCenterClient/Get 解析请求URL异常,path:%s,params:%s,err:%+v", path, kit.JsonEncode(params), err)
		return nil, err
	}
	query := urlObj.Query()
	for k, list := range params {
		for _, v := range list {
			query.Add(k, v)
		}
	}
	urlObj.RawQuery = query.Encode()
	req, err := http.NewRequest(http.MethodGet, urlObj.String(), nil)
	if err != nil {
		glog.Errorf("AuthCenterClient/Get 构建请求参数异常,path:%s,params:%s,err:%+v", path, kit.JsonEncode(params), err)
		return nil, err
	}
	req.Header.Set("content-type", echo.MIMEApplicationForm)
	result, err := s.doRequest(req)
	if err != nil {
		glog.Errorf("AuthCenterClient/Get 请求服务异常,path:%s,params:%s,err:%+v", path, kit.JsonEncode(params), err)
		return nil, err
	}
	return result, nil
}

type acCommonResponse struct {
	Message string          `json:"message"`
	Code    int             `json:"code"`
	Data    json.RawMessage `json:"data"`
}
