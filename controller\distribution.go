package controller

import (
	"_/proto/sh"
	"_/utils"
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @Summary 分销商品列表/商品分类
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param DisCategoriesRequest query sh.DisCategoriesRequest true " "
// @Success 200 {object} sh.DisCategoriesResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/categories [get]
func DisCategories(c echo.Context) error {
	req := &sh.DisCategoriesRequest{
		ParentId: cast.ToInt32(c.QueryParam("parent_id")),
	}

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.DIS.Categories(client.Ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分销商品列表/商品列表
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param DisSpuListRequest query sh.DisSpuListRequest true " "
// @Success 200 {object} sh.DisSpuListResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/spus [get]
func DisSpuList(c echo.Context) error {
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	req := &sh.DisSpuListRequest{
		Page:        cast.ToInt32(c.QueryParam("page")),
		PageSize:    cast.ToInt32(c.QueryParam("page_size")),
		MinRate:     cast.ToFloat32(c.QueryParam("min_rate")),
		MaxRate:     cast.ToFloat32(c.QueryParam("max_rate")),
		GoodsName:   strings.TrimSpace(c.QueryParam("goods_name")),
		SkuId:       strings.TrimSpace(c.QueryParam("sku_id")),
		SpuId:       strings.TrimSpace(c.QueryParam("spu_id")),
		CategoryId:  cast.ToInt32(c.QueryParam("category_id")),
		IsRecommend: cast.ToInt32(c.QueryParam("is_recommend")),
		OrgId:       cast.ToInt32(orgId),
	}
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.DIS.SpuList(client.Ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分销商品列表/spu详情
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param DisSpuDetailRequest query sh.DisSpuDetailRequest true " "
// @Success 200 {object} sh.DisSpuDetailResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/spu/detail [get]
func DisSpuDetail(c echo.Context) error {
	req := &sh.DisSpuDetailRequest{
		Page:     cast.ToInt32(c.QueryParam("page")),
		PageSize: cast.ToInt32(c.QueryParam("page_size")),
		SpuId:    cast.ToInt32(c.QueryParam("spu_id")),
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.DIS.SpuDetail(client.Ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分销商品列表/spu更新（包括 推荐/取消推荐到首页、更新首页排序）
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param spu_id body int true "商品spuId"
// @Param is_recommend body boolean false "是否首页推荐，true或者false（不更新不要传）"
// @Param sort body int false "首页排序（不更新不要传）"
// @Success 200 {object} sh.DisResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/spu/update [patch]
func DisSpuUpdate(c echo.Context) error {
	req := new(sh.DisSpuUpdateRequest)

	var params map[string]interface{}
	if err := json.NewDecoder(c.Request().Body).Decode(&params); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: "解析参数错误 " + err.Error()})
	}

	for k, v := range params {
		switch k {
		case "spu_id":
			req.SpuId = cast.ToInt32(v)
		case "is_recommend":
			req.IsRecommend = &wrappers.BoolValue{
				Value: cast.ToBool(v),
			}
		case "sort":
			req.Sort = &wrappers.Int32Value{
				Value: cast.ToInt32(v),
			}
		}
	}

	if req.SpuId < 1 {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: "SpuId不能为空"})
	}

	// 附加操作人信息
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	ctx, err := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	}
	if out, err := client.DIS.SpuUpdate(ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分销商品列表/spu操作日志
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param DisSpuLogsRequest query sh.DisSpuLogsRequest true " "
// @Success 200 {object} sh.DisSpuLogsResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/spu/logs [get]
func DisSpuLogs(c echo.Context) error {
	req := &sh.DisSpuLogsRequest{
		SpuId:    cast.ToInt32(c.QueryParam("spu_id")),
		Page:     cast.ToInt32(c.QueryParam("page")),
		PageSize: cast.ToInt32(c.QueryParam("page_size")),
	}
	if req.SpuId < 1 {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: "SpuId不能为空"})
	}

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.DIS.SpuLogs(client.Ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分销商品列表/sku更新（包括 添加分销、取消分销、推广文案变更、佣金变更）
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param sku_id body int32 true "商品skuId"
// @Param write body string false "推广文案（不更新不要传）"
// @Param normal_commission_rate body float32 false "日常佣金（不更新不要传）"
// @Param is_dis body boolean false "是否分销，true 添加分销，添加分销可选设置normal_commission_rate、false 取消分销（不更新不要传）"
// @Success 200 {object} sh.DisResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/sku/update [patch]
func DisSkuUpdate(c echo.Context) error {
	req := new(sh.DisSkuUpdateRequest)
	var params map[string]interface{}
	if err := json.NewDecoder(c.Request().Body).Decode(&params); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: "解析参数错误 " + err.Error()})
	}

	for k, v := range params {
		switch k {
		case "sku_id":
			req.SkuId = cast.ToInt32(v)
		case "write":
			req.Write = &wrappers.StringValue{
				Value: cast.ToString(v),
			}
		case "normal_commission_rate":
			req.NormalCommissionRate = &wrappers.FloatValue{
				Value: cast.ToFloat32(v),
			}
		case "is_dis":
			req.IsDis = &wrappers.BoolValue{
				Value: cast.ToBool(v),
			}
		}
	}
	if req.SkuId < 1 {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: "SkuId不能为空"})
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	// 附加操作人信息
	ctx, err := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	}

	if out, err := client.DIS.SkuUpdate(ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分销商品列表/非分销商品列表（添加分销商品）
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param DisNotDisSkuListRequest query sh.DisNotDisSkuListRequest true " "
// @Success 200 {object} sh.DisNotDisSkuListResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/not-dis-skus [get]
func DisNotDisSkus(c echo.Context) error {
	req := &sh.DisNotDisSkuListRequest{
		SkuId:    strings.TrimSpace(c.QueryParam("sku_id")),
		SpuId:    strings.TrimSpace(c.QueryParam("spu_id")),
		Name:     strings.TrimSpace(c.QueryParam("name")),
		Page:     cast.ToInt32(c.QueryParam("page")),
		PageSize: cast.ToInt32(c.QueryParam("page_size")),
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.DIS.NotDisSkuList(client.Ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 下载导入模板（文件，包括限时活动、批量导入分销商品）
// @Tags 分销商品
// @Accept json
// @Produce octet-stream
// @Param DisImportTemplateRequest query sh.DisImportTemplateRequest true " "
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/import/template [get]
func DisImportTemplate(c echo.Context) error {
	req := &sh.DisImportTemplateRequest{
		Type: cast.ToInt32(c.QueryParam("type")),
	}

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.DIS.ImportTemplate(client.Ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		if out.Code == 200 {
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename=导入模板.xlsx")
			return c.Blob(200, echo.MIMEOctetStream, out.Template)
		}
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: out.Message})
	}
}

// @Summary 分销商品列表/批量导入（multipart/form-data）
// @Tags 分销商品
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "上传的文件"
// @Param DisImportRequest query sh.DisImportRequest true " "
// @Success 200 {object} sh.DisResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/import [post]
func DisImport(c echo.Context) error {
	req := new(sh.DisImportRequest)

	file, _, err := c.Request().FormFile("file")
	if err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: "获取文件出错 " + err.Error()})
	}
	defer file.Close()

	buf := bytes.NewBuffer(nil)
	if _, err := io.Copy(buf, file); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: "复制文件出错 " + err.Error()})
	}
	req.File = buf.Bytes()
	req.Type = cast.ToInt32(c.FormValue("type"))
	req.Id = cast.ToInt32(c.FormValue("id"))

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	// 附加操作人信息
	ctx, err := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if out, err := client.DIS.Import(ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 批量导入历史（包括限时活动、批量导入分销商品）
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param sh.DisImportListRequest query sh.DisImportListRequest true " "
// @Success 200 {object} sh.DisImportListResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/import/list [get]
func DisImportList(c echo.Context) error {
	req := &sh.DisImportListRequest{
		Page:     cast.ToInt32(c.QueryParam("page")),
		PageSize: cast.ToInt32(c.QueryParam("page_size")),
		Type:     cast.ToInt32(c.QueryParam("type")),
		TypeId:   cast.ToInt32(c.QueryParam("type_id")),
	}
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.DIS.ImportList(client.Ctx, req); err != nil {
		return c.JSON(400, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分销商品列表/导出非分销的商品
// @Tags 分销商品
// @Accept json
// @Produce json
// @Success 200 {object} sh.DisResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/export-no-dis-goods [GET]
func ExportNoDisGoods(c echo.Context) error {
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.DIS.ExportNoDisGoods(client.Ctx, &wrappers.Int32Value{Value: cast.ToInt32(orgId)}); err != nil {
		return c.JSON(http.StatusBadRequest, &sh.DisResponse{Code: 400, Message: err.Error()})
	} else {
		if out.Code == 200 {
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename=非分销商品.xlsx")
			return c.Blob(http.StatusOK, echo.MIMEOctetStream, out.File)
		}
		return c.JSON(http.StatusBadRequest, &sh.DisResponse{Code: 400, Message: out.Message})
	}
}

// @Summary 分销设置全局默认佣金
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param DisSetGlobalCommissionRequest body sh.DisSetGlobalCommissionRequest true " "
// @Success 200 {object} sh.DisResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/set-global-commission [POST]
func DisSetGlobalCommission(c echo.Context) (err error) {
	out := new(sh.DisResponse)
	out.Code = 400
	in := new(sh.DisSetGlobalCommissionRequest)

	// A、参数绑定和校验
	if err := c.Bind(in); err != nil {
		out.Message = "参数解析失败，" + err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if in.DefaultCommission < 0.5 || in.DefaultCommission > 50 {
		out.Message = "默认佣金必须在0.5%-50%之间"
		return c.JSON(http.StatusBadRequest, out)
	}

	// B、RPC调用
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err = client.DIS.DisSetGlobalCommission(client.Ctx, in); err != nil {
		return c.JSON(http.StatusBadRequest, &sh.DisResponse{Code: 400, Message: err.Error()})
	}

	return c.JSON(http.StatusOK, out)
}

// @Summary 分销获取全局默认佣金
// @Tags 分销商品
// @Accept json
// @Produce json
// @Success 200 {object} sh.DisGlobalCommissionResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/global-commission [GET]
func DisGlobalCommission(c echo.Context) (err error) {
	out := new(sh.DisGlobalCommissionResponse)

	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err = client.DIS.DisGlobalCommission(client.Ctx, &sh.EmptyRequest{}); err != nil {
		return c.JSON(http.StatusBadRequest, &sh.DisResponse{Code: 400, Message: err.Error()})
	}

	return c.JSON(http.StatusOK, out)
}

// @Summary 限时佣金/分销商品列表
// @Tags 分销商品
// @Accept json
// @Produce json
// @Param DisGoodsListRequest body sh.DisGoodsListRequest true " "
// @Success 200 {object} sh.DisGoodsListResponse
// @Failure 400 {object} sh.DisResponse
// @Router /boss/distribution/goods-list [GET]
func DisGoodsList(c echo.Context) (err error) {
	out := new(sh.DisGoodsListResponse)

	// A、参数绑定和校验
	in := &sh.DisGoodsListRequest{
		ActivityId: cast.ToInt64(c.QueryParam("activity_id")),
		Page:       cast.ToInt32(c.QueryParam("page")),
		PageSize:   cast.ToInt32(c.QueryParam("page_size")),
		SearchType: cast.ToInt32(c.QueryParam("search_type")),
		SearchName: c.QueryParam("search_name"),
	}
	if in.Page < 1 || in.PageSize < 1 {
		return c.JSON(http.StatusBadRequest, &sh.DisResponse{Code: 400, Message: "分页参数错误！"})
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	in.OrgId = cast.ToInt32(orgId)

	// B、RPC调用
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err = client.DIS.DisGoodsList(client.Ctx, in); err != nil {
		return c.JSON(http.StatusBadRequest, &sh.DisResponse{Code: 400, Message: err.Error()})
	}

	return c.JSON(http.StatusOK, out)
}
