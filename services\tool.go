package services

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/maybgit/glog"
	"github.com/xuri/excelize/v2"
	"golang.org/x/net/html"
)

type ExcelStruct struct {
	File *excelize.File
	Data [][]string
}

const maxRow int = 80000

// 从excel里读取文件
func (e *ExcelStruct) Read() error {
	sheetName := e.File.GetSheetName(0)
	logPrefix := fmt.Sprintf("从excel中读数据(e *ExcelStruct) Read()====：SheetName:%s", sheetName)
	rows, err := e.File.Rows(sheetName)
	if err != nil {
		glog.Errorf("%sreadFile====获取行数据出错：%s", logPrefix, err.Error())
		return errors.New("获取行数据出错 " + err.Error())
	}

	for i := 0; rows.Next(); i++ {
		// 注意这里一定要读取行，不然内容会附加到下一行
		row, err := rows.Columns()
		// 表头不处理
		if i == 0 {
			continue
		}
		if i > int(maxRow) {
			glog.Errorf("%sreadFile====最多导入%v行数据", logPrefix, maxRow)
			return fmt.Errorf("最多导入%v行数据", maxRow)
		}
		if err != nil {
			glog.Errorf("%sreadFile====获取列数据出错:%s", logPrefix, err.Error())
			return errors.New("获取列数据出错 " + err.Error())
		}
		for k, v := range row {
			row[k] = strings.TrimSpace(v)
		}

		// 无效的空数忽略
		if len(row) == 0 || len(row[0]) < 1 {
			continue
		}
		e.Data = append(e.Data, row)

	}
	_ = rows.Close()
	return nil
}

func DownloadPic(from, to string) error {

	logPrefix := fmt.Sprintf("从%s到%s", from, to)
	//创建目录
	dirSli := strings.Split(to, "\\")
	dir := strings.Join(dirSli[:6], "\\")
	err := os.MkdirAll(dir, os.ModePerm)
	if err != nil {
		return fmt.Errorf("%s,错误1:%s", logPrefix, err.Error())
	}

	//imgName := kit.GetGuid36() + ".jpg"
	//imgName := path.Base(from) + ".jpg"
	f, err := os.Create(to)
	if err != nil {
		return fmt.Errorf("%s,错误2:%s", logPrefix, err.Error())
	}

	r, err := http.Get(from)
	if err != nil {
		return fmt.Errorf("%s,错误3:%s", logPrefix, err.Error())
	}
	body, err := io.ReadAll(r.Body)

	if err != nil {
		return fmt.Errorf("%s,错误4:%s", logPrefix, err.Error())
	}
	if _, err := io.Copy(f, bytes.NewReader(body)); err != nil {
		return fmt.Errorf("%s,错误5:%s", logPrefix, err.Error())
	}

	//释放资源
	r.Body.Close()

	f.Close()

	return nil
}

// 复制整个文件夹 到一个新的文件夹
func CopyImage(src, dst string) error {
	logPrefix := fmt.Sprintf("复制图片从%s到%s", src, dst)
	srcStat, err := os.Stat(src)
	if err != nil {
		return fmt.Errorf("%s 错误1：%s", logPrefix, err.Error())
	}
	if !srcStat.Mode().IsRegular() {
		return fmt.Errorf("%s 原图片不是一个标准的文件", logPrefix)
	}
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("%s 打开原图片出错：%s", logPrefix, err.Error())
	}
	defer srcFile.Close()

	if err := os.MkdirAll(filepath.Dir(dst), 0770); err != nil {
		return fmt.Errorf("%s 创建目标文件夹出错：%s", logPrefix, err.Error())
	}
	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("%s 创建目标图片出错：%s", logPrefix, err.Error())
	}
	defer dstFile.Close()
	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return fmt.Errorf("%s 复制图片出错：%s", logPrefix, err.Error())
	}
	return nil
}

var sema = make(chan struct{}, 20)

// dirents returns the entries of directory dir.
func Dirents(dir string) ([]os.DirEntry, error) {
	logPrefix := fmt.Sprintf("读取文件夹包含文件:%s", dir)
	sema <- struct{}{}        // acquire token
	defer func() { <-sema }() // release token
	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, fmt.Errorf("%s 出错：%s", logPrefix, err.Error())
	}

	return entries, nil
}
func WalkDir(dir string, paths chan<- string) error {
	dirents, err := Dirents(dir)
	if err != nil {
		return err
	}
	for _, entry := range dirents {
		if entry.IsDir() {
			subdir := filepath.Join(dir, entry.Name())
			WalkDir(subdir, paths)
		} else {
			paths <- filepath.Join(dir, entry.Name())
		}
	}
	return nil
}

// !+breadthFirst
// breadthFirst calls f for each item in the worklist.
// Any items returned by f are added to the worklist.
// f is called at most once for each item.
func breadthFirst(f func(item string) []string, worklist []string) {
	seen := make(map[string]bool)
	for len(worklist) > 0 {
		items := worklist
		worklist = nil
		for _, item := range items {
			if !seen[item] {
				seen[item] = true
				worklist = append(worklist, f(item)...)
			}
		}
	}
}

//!-breadthFirst

// !+crawl
func Crawl(htmlNode string) []string {

	list, err := Extract(htmlNode)
	if err != nil {
		glog.Error("获取html文档里的图片src失败：", err.Error())
	}
	return list
}

// Extract makes an HTTP GET request to the specified URL, parses
// the response as HTML, and returns the links in the HTML document.
func Extract(htmlNode string) ([]string, error) {
	buf := bytes.NewBuffer([]byte(htmlNode))
	doc, err := html.Parse(buf)
	if err != nil {
		return nil, fmt.Errorf("parsing %s as HTML: %v", htmlNode, err)
	}

	var links []string
	visitNode := func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "img" {
			for _, a := range n.Attr {
				if a.Key != "src" {
					continue
				}

				links = append(links, a.Val)
			}
		}
	}
	forEachNode(doc, visitNode, nil)
	return links, nil
}

//!-Extract

// Copied from gopl.io/ch5/outline2.
func forEachNode(n *html.Node, pre, post func(n *html.Node)) {
	if pre != nil {
		pre(n)
	}
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		forEachNode(c, pre, post)
	}
	if post != nil {
		post(n)
	}
}
