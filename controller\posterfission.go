package controller

import (
	"_/proto/ac"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @Summary 活动列表
// @Tags 海报裂变
// @Accept json
// @Produce json
// @Param model query ac.PosterFissionListReq true " "
// @Success 200 {object} ac.PosterFissionListRes
// @Failure 400 {object} ac.PosterFissionListRes
// @Router /boss/poster-fission/list [GET]
func PosterFissionList(c echo.Context) error {
	req := &ac.PosterFissionListReq{
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
	}

	client := ac.GetActivityCenterClient()
	if out, err := client.PF.List(client.Ctx, req); err != nil {
		return c.JSON(400, &ac.PosterFissionListRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动详情
// @Tags 海报裂变
// @Accept json
// @Produce json
// @Param model query ac.PosterFissionDetailReq true " "
// @Success 200 {object} ac.PosterFissionDetailRes
// @Failure 400 {object} ac.PosterFissionDetailRes
// @Router /boss/poster-fission/detail [GET]
func PosterFissionDetail(c echo.Context) error {
	req := &ac.PosterFissionDetailReq{
		Id: cast.ToInt32(c.QueryParam("id")),
	}

	client := ac.GetActivityCenterClient()
	if out, err := client.PF.Detail(client.Ctx, req); err != nil {
		return c.JSON(400, &ac.PosterFissionDetailRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动新增/编辑
// @Tags 海报裂变
// @Accept json
// @Produce json
// @Param model body ac.PosterFission true " "
// @Success 200 {object} ac.BaseRes
// @Failure 400 {object} ac.BaseRes
// @Router /boss/poster-fission/store [POST]
func PosterFissionStore(c echo.Context) error {
	req := new(ac.PosterFission)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &ac.BaseRes{Code: 400, Message: err.Error()})
	}

	client := ac.GetActivityCenterClient()
	if out, err := client.PF.Store(client.Ctx, req); err != nil {
		return c.JSON(400, &ac.BaseRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
