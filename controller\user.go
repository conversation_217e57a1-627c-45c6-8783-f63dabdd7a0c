package controller

import (
	"_/models"
	"_/proto/dac"
	"_/proto/dc"
	"_/utils"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/spf13/cast"

	"context"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
)

// @Summary 获取用户权限内的门店医院列表
// @Tags 用户
// @Accept plain
// @Produce plain
// @Param user_no query string false "用户编号（为空时，系统自动获取当前登录用户的编号）"
// @Success 200 {object} models.HospitalResponse
// @Failure 400 {object} models.HospitalResponse
// @Router /boss/user/hospital/list [get]
func GetHospitalListByUserNo(c echo.Context) error {
	userNo := c.QueryParam("user_no")
	if userNo == "" {
		if claims, err := utils.GetPayloadDirectly(c); err != nil {
			glog.Error(err)
			return c.JSON(400, models.HospitalResponse{Code: 400, Message: err.Error()})
		} else {
			userNo = utils.InterfaceToString(claims["userno"])
		}
	}

	var hospital models.HospitalResponse

	client := GetDataCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()
	var params dac.GetHospitalListByUserNoRequest
	params.UserNo = userNo
	rpc_data, err := client.RPC.GetDataByUserNo(client.Ctx, &params)
	if err != nil {
		glog.Error("rpc调用北京接口", err)
		return c.JSON(400, err.Error())
	}
	code := 200
	hospital = StuctToData(rpc_data.Data)

	//glog.Infof("调用北京获取用户权限下门店信息 %s %v %d %s ", url, mp, code, data)
	if code == 200 {
		if len(hospital.Data.List) > 0 {
			hospital.Code = 200

			//落地用户权限门店数据
			var sb strings.Builder

			var in dac.StoreUserAuthorityRequest
			for _, v := range hospital.Data.List {
				in.List = append(in.List, &dac.StoreUserAuthority{UserNo: userNo, FinanceCode: v.StructOuterCode})
				sb.WriteString(v.StructOuterCode)
				sb.WriteString(",")
			}

			var srr dac.StoreRelationRequest
			srr.ChannelId = 0
			srr.FinanceCode = strings.TrimRight(sb.String(), ",")

			//根据财务编码获取所有注册数据中心的门店数据-- 绑定了渠道id和渠道对应的id
			if out, err := client.RPC.SeachRelationList(client.Ctx, &srr); err != nil {
				return c.JSON(400, &models.HospitalResponse{Code: 400, Message: err.Error()})
			} else {
				var hasHospitalMap = make(map[string]interface{})
				var hasHospital []models.Hospital
				for _, v := range out.List {
					for _, v2 := range hospital.Data.List {
						if v.FinanceCode == v2.StructOuterCode {
							if _, ok := hasHospitalMap[v.FinanceCode]; !ok {
								hasHospitalMap[v.FinanceCode] = v.FinanceCode
								hasHospital = append(hasHospital, v2)
							}
						}
					}
				}
				hospital.Data.List = hasHospital
			}
			if _, err := client.RPC.SaveStoreUserAuthority(client.Ctx, &in); err != nil {
				return c.JSON(400, &models.HospitalResponse{Code: 400, Message: err.Error()})
			}
		}
	} else {
		hospital.Message = err.Error()
	}
	return c.JSON(200, hospital)
}

// @Summary 获取北京acp系统配置的用户的门店医院列表
// @Tags 用户
// @Accept plain
// @Produce plain
// @Param user_no query string false "用户编号（为空时，系统自动获取当前登录用户的编号）"
// @Success 200 {object} models.HospitalResponse
// @Failure 400 {object} models.HospitalResponse
// @Router /boss/user/hospital/bjlist [get]
func GetBjHospitalListByUserNo(c echo.Context) error {
	userNo := c.QueryParam("user_no")
	if userNo == "" {
		if claims, err := utils.GetPayloadDirectly(c); err != nil {
			glog.Error(err)
			return c.JSON(400, models.HospitalResponse{Code: 400, Message: err.Error()})
		} else {
			userNo = utils.InterfaceToString(claims["userno"])
		}
	}

	mp := createCommonBjAcpParam()
	mp["systemCode"] = config.GetString("BJAuth.SystemCode.api.out.priv.hospital.list")
	mp["func"] = config.GetString("BJAuth.func.api.out.priv.hospital.list")
	//根据SAAS版本要求优化
	mp["structOuterCode"] = c.Request().Header.Get("structOuterCode")

	var hospital models.HospitalResponse

	//落地用户权限门店数据
	client := GetDataCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	if res, err := client.RPC.GetDataByUserNo(client.Ctx, &dac.GetHospitalListByUserNoRequest{UserNo: userNo}); err != nil {
		glog.Error(err)
	} else if res.Code == 200 {
		if err := json.Unmarshal(res.OriginalData, &hospital.Data.List); err != nil {
			glog.Error(err)
		} else {
			var in dac.StoreUserAuthorityRequest
			for _, v := range hospital.Data.List {
				in.List = append(in.List, &dac.StoreUserAuthority{UserNo: userNo, FinanceCode: v.StructOuterCode})
			}
			if len(in.List) > 0 {
				if _, err := client.RPC.SaveStoreUserAuthority(client.Ctx, &in); err != nil {
					return c.JSON(400, &models.HospitalResponse{Code: 400, Message: err.Error()})
				}
			}
		}
	} else {
		return c.JSON(400, &models.HospitalResponse{Code: int(res.Code), Message: res.Message})
	}

	hospital.Code = 200
	return c.JSON(200, hospital)
}

// @Summary 获取用户权限内的门店医院列表
// @Tags 用户
// @Accept plain
// @Produce plain
// @Param user_no query string false "用户编号（为空时，系统自动获取当前登录用户的编号）"
// @Param finance_code query string false "财务编码"
// @Param is_all   query string false "门店类型(0-所有，1-部分)"
// @Param category query string false "仓库类型(3-门店仓，4-前置仓)"
// @Param channel_id query string false "渠道id(1-阿闻，2-美团，3-饿了么)"
// @Success 200 {object} models.HospitalResponse
// @Failure 400 {object} models.HospitalResponse
// @Router /boss/user/hospitalCondition/list [get]
func GetHospitalListByCondition(c echo.Context) error {
	userNo := c.QueryParam("user_no")
	if userNo == "" {
		if claims, err := utils.GetPayloadDirectly(c); err != nil {
			glog.Error(err)
			return c.JSON(400, models.HospitalResponse{Code: 400, Message: err.Error()})
		} else {
			userNo = utils.InterfaceToString(claims["userno"])
		}
	}

	var hospital models.HospitalResponse

	DataCenterClient := GetDataCenterClient(c)
	defer DataCenterClient.Conn.Close()
	defer DataCenterClient.Cf()
	var params dac.GetHospitalListByUserNoRequest
	params.UserNo = userNo
	params.From = c.QueryParam("from")
	rpc_data, err := DataCenterClient.RPC.GetDataByUserNo(DataCenterClient.Ctx, &params)
	if err != nil {
		glog.Error("rpc调用北京接口", err)
		return c.JSON(400, err.Error())
	}
	hospital = StuctToData(rpc_data.Data)
	//标记总账号信息
	var userInfo models.LoginUserInfo
	err = errors.New("")
	if userInfo, err = utils.GetPayloadDirectlyToInterface(c); err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	}

	//只保留正常营业状态的门店，过滤不正常状态的门店 add by csf产品要求
	//var availableHospitals []models.Hospital
	//for _, item := range hospital.Data.List {
	//	if item.ClinicStatus == 1 {
	//		availableHospitals = append(availableHospitals, item)
	//	}
	//}
	//hospital.Data.List = availableHospitals //用过滤出来的正常营业门店替换调接口查询出来的用户所有门店

	var hospitalList []models.Hospital
	if len(hospital.Data.List) > 0 {
		//北京全门店列表数据 map[财务编码]=hospital对象
		out_data_map := make(map[string]models.Hospital)
		for _, d := range hospital.Data.List {
			out_data_map[d.StructOuterCode] = d
		}
		//根据类型(前置仓门店或子龙门店)查询所有的仓库信息
		client := GetDispatchCenter()
		defer client.Conn.Close()
		defer client.Cf()
		model := new(dc.GetStoreListByCategoryRequest)
		model.Category = int32(cast.ToInt(c.QueryParam("category")))
		model.ChannelId = int32(cast.ToInt(c.QueryParam("channel_id")))
		grpcRes, err := client.RPC.GetStoreListByCategory(context.Background(), model)
		if err != nil || grpcRes.Code != 200 {
			return c.JSON(400, models.BaseResponse{Code: 400, Details: "获取所有的门店类型数据报错：" + err.Error()})
		}
		for _, v := range grpcRes.FinanceCode {
			if _, ok := out_data_map[v]; ok {
				if len(userInfo.FinancialCode) == 0 {
					hospitalList = append(hospitalList, out_data_map[v])
				} else if len(userInfo.FinancialCode) > 0 && userInfo.FinancialCode == v {
					hospitalList = append(hospitalList, out_data_map[v])
					break
				}
			}
		}
	}
	hospital.Code = 200
	hospital.Data.List = hospitalList
	return c.JSON(200, hospital)
}

// @Summary 获取用户权限内的门店医院列表 v6.23.0
// @Tags 用户
// @Accept plain
// @Produce plain
// @Param user_no query string false "用户编号（为空时，系统自动获取当前登录用户的编号）"
// @Success 200 {object} dac.GetOrgStoreListResponse
// @Failure 400 {object} dac.GetOrgStoreListResponse
// @Router /boss/user/org-store/list [get]
func GetOrgStoreList(c echo.Context) error {
	userNo := c.QueryParam("user_no")
	logPrefix := "boss-GetOrgStoreListByUserno获取用户权限内的门店医院列表===="
	out := &dac.GetOrgStoreListResponse{}
	if len(userNo) == 0 {
		if claims, err := utils.GetPayloadDirectly(c); err != nil {
			glog.Error(logPrefix, err.Error())
			out.Message = err.Error()
			return c.JSON(400, out)
		} else {
			userNo = utils.InterfaceToString(claims["userno"])
		}
	}
	logPrefix = fmt.Sprintf("%s, user_no:%s", logPrefix, userNo)
	glog.Info(logPrefix)
	DataCenterClient := GetDataCenterClient(c)
	defer DataCenterClient.Conn.Close()
	defer DataCenterClient.Cf()
	rpcReq := &dac.GetOrgStoreListRequest{
		UserNo: userNo,
	}
	rpcRes, err := DataCenterClient.RPC.GetOrgStoreList(DataCenterClient.Ctx, rpcReq)
	if err != nil {
		glog.Error(logPrefix, "DataCenterClient.RPC.GetOrgStoreList失败：", err.Error())
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	c.JSON(200, rpcRes)

	return nil

}

// @Summary 获取用户判断是否是总账号权限
// @Tags 用户
// @Accept plain
// @Produce plain
// @Param user_no query string false "用户编号（为空时，系统自动获取当前登录用户的编号）"
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /boss/user/permits/judge [get]
func JudgeUserPower(c echo.Context) error {
	// return c.JSON(200,"ok")
	userNo := c.QueryParam("user_no")
	if userNo == "" {
		if claims, err := utils.GetPayloadDirectly(c); err != nil {
			glog.Error(err)
			return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
		} else {
			userNo = utils.InterfaceToString(claims["userno"])
		}
	}
	//保存信息到渠道商品【具有总帐号权限】
	adminAccount := config.GetString("adminAccountList")
	adminAccountList := strings.Split(adminAccount, "|")
	isAdmin := "0"
	for _, v := range adminAccountList {
		if v == userNo {
			isAdmin = "1"
		}
	}
	return c.JSON(200, models.BaseResponse{Code: 200, Details: isAdmin})
}

// 转化结构
func StuctToData(Data []*dac.Hospital) (hospital models.HospitalResponse) {
	if len(Data) > 0 {
		for _, v := range Data {
			var model models.Hospital
			model.Id = int(v.Id)
			model.ClinicId = v.ClinicId
			model.ParentId = int(v.ParentId)
			model.ClinicName = v.ClinicName
			model.ClinicShortname = v.ClinicShortname
			model.Brand = v.Brand
			model.BrandId = int(v.BrandId)
			model.SubBrand = v.SubBrand
			model.BrandCode = v.BrandCode
			model.ClinicStatus = int(v.ClinicStatus)
			model.ClinicType = v.ClinicType
			model.CreateTime = v.CreateTime
			model.Province = v.Province
			model.City = v.City
			model.Address = v.Address
			model.Dean = v.Dean
			model.DeanNumber = v.DeanNumber
			model.InnerContactor = v.InnerContactor
			model.InnerContactorPhone = v.InnerContactorPhone
			model.HospitalPhone = v.HospitalPhone
			model.HasMedcine = int(v.HasMedcine)
			model.HasMeirong = int(v.HasMeirong)
			model.HasLingshou = int(v.HasLingshou)
			model.System = v.System
			model.SystemId = int(v.SystemId)
			model.BusinessLicenseName = v.BusinessLicenseName
			model.Longitude = v.Longitude
			model.Latitude = v.Latitude
			model.MeituanId = v.MeituanId
			model.StructOuterCode = v.StructOuterCode
			model.AddTime = v.AddTime
			model.LastModify = v.LastModify
			hospital.Data.List = append(hospital.Data.List, model)
		}
	}
	return hospital
}
