package controller

import (
	"_/dto"
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/tricobbler/echo-tool/validate"
	"net/http"
)

// @Summary 开票设置
// @Tags 基础设置
// @Accept json
// @Produce json
// @Param param body dto.SaveInvoiceSetRequest true " "
// @Success 200 {object} dto.BaseResponse
// @Failure 400 {object} dto.BaseResponse
// @Router /boss/product/invoice/set [post]
func InvoiceSet(c echo.Context) error {
	out := &dto.BaseResponse{}
	model := new(dto.SaveInvoiceSetRequest)
	if err := c.Bind(model); err != nil {
		out.Message = "参数错误"
		return c.JSON(http.StatusBadRequest, out)
	}
	if err := c.Validate(model); err != nil {
		errValidate := validate.Translate(err.(validator.ValidationErrors))
		out.Message = errValidate.One()
		return c.JSON(http.StatusBadRequest, out)
	}
	redisClient := GetRedisConn()

	//在此处设置，在order-center的业务逻辑代码中读取
	redisClient.HSet("set:invoice_set", "invoice_enable_mall", model.InvoiceEnableMall)
	redisClient.HSet("set:invoice_set", "invoice_enable_o2o", model.InvoiceEnableO2O)
	redisClient.HSet("set:invoice_set", "invoice_qr_code_o2o", model.InvoiceQRCodeO2O)
	out.Message = "保存成功"
	return c.JSON(http.StatusOK, out)
}

// @Summary 开票设置查询
// @Tags 基础设置
// @Accept plain
// @Accept json
// @Produce json
// @Success 200 {object} dto.GetInvoiceSetResponse
// @Failure 400 {object} dto.GetInvoiceSetResponse
// @Router /boss/product/invoice/info [get]
func InvoiceSetInfo(c echo.Context) error {
	out := &dto.GetInvoiceSetResponse{}
	redisClient := GetRedisConn()

	//在此处设置，在order-center的业务逻辑代码中读取
	invoiceEnableMall := redisClient.HGet("set:invoice_set", "invoice_enable_mall").Val()
	InvoiceEnableO2O := redisClient.HGet("set:invoice_set", "invoice_enable_o2o").Val()
	InvoiceQRCodeO2O := redisClient.HGet("set:invoice_set", "invoice_qr_code_o2o").Val()

	out.Data = new(dto.InvoiceSet)
	out.Data.InvoiceEnableMall = cast.ToInt32(invoiceEnableMall)
	out.Data.InvoiceEnableO2O = cast.ToInt32(InvoiceEnableO2O)
	out.Data.InvoiceQRCodeO2O = cast.ToInt32(InvoiceQRCodeO2O)

	out.Message = "获取成功"
	return c.JSON(http.StatusOK, out)
}
