package controller

import (
	"_/models"
	"_/proto/cc"
	"github.com/spf13/cast"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

func CallCustomerCenterGrpc(c echo.Context, request interface{}, grpc func(grpc *cc.Client) (response interface{}, err error)) error {
	// 400 错误代码返回
	var badResponse = new(models.BaseResponseV2)
	badResponse.Code = http.StatusBadRequest

	//获取请求参数到实体对象
	if request != nil {
		err := c.Bind(request)
		if err != nil {
			glog.Error(err)
			badResponse.Message = err.Error()
			return c.JSON(http.StatusBadRequest, badResponse)
		}
	}

	// 获取grpc 链接
	var conn = cc.GetCustomerCenterClient()
	// 关闭链接
	defer conn.Close()
	res, err := grpc(conn)
	if err != nil {
		glog.Error(err)
	}
	return c.JSON(http.StatusOK, res)

}

// QueryProductTags
// @Summary 商品可用标签列表
// @Tags 标签
// @Accept json
// @Produce json
// @Success 200 {object} models.TagsQueryResponse
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/tags/product [get]
func QueryProductTags(c echo.Context) error {
	var response = new(models.TagsQueryResponse)
	// 调用远程
	err := CallCustomerCenterGrpc(c, nil, func(grpc *cc.Client) (interface{}, error) {
		var grpcRequest = new(cc.TagsQueryRequest)
		grpcRequest.Groups = 1
		// 调用远程
		res, err := grpc.RPC.QueryTags(grpc.Ctx, grpcRequest)

		if err != nil {
			response.Code = 400
			response.Message = err.Error()
		}

		if res != nil {
			response.Code = res.Code
			response.Message = res.Message
			for _, tag := range res.Data {
				var dto = &models.TagsDto{Name: tag.Name, Values: tag.Value, IsRequired: int(tag.HasAsterisk), Sort: int(tag.Sort), DataUrl: tag.DataUrl}
				response.Data = append(response.Data, dto)
			}
		}

		return response, err
	})
	return err
}

// QueryPetTipsTags
// @Summary 贴士可用标签列表
// @Tags 标签
// @Accept json
// @Produce json
// @Param from query string false "来源，content-内容中心"
// @Success 200 {object} models.TagsQueryResponse
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/tags/pettips [get]
func QueryPetTipsTags(c echo.Context) error {
	var response = new(models.TagsQueryResponse)
	// 调用远程
	err := CallCustomerCenterGrpc(c, nil, func(grpc *cc.Client) (interface{}, error) {
		var grpcRequest = new(cc.TagsQueryRequest)
		grpcRequest.Groups = 1
		grpcRequest.From = strings.TrimSpace(c.QueryParam("from"))
		// 调用远程
		res, err := grpc.RPC.QueryTags(grpc.Ctx, grpcRequest)

		if err != nil {
			response.Code = 400
			response.Message = err.Error()
		}

		if res != nil {
			response.Code = res.Code
			response.Message = res.Message
			for _, tag := range res.Data {
				var dto = &models.TagsDto{Name: tag.Name, Values: tag.Value, IsRequired: int(tag.HasAsterisk), Sort: int(tag.Sort), DataUrl: tag.DataUrl}
				response.Data = append(response.Data, dto)
			}
		}

		return response, err
	})
	return err
}

// QueryTagSelectValues
// @Summary 查询物种标签
// @Tags 标签
// @Accept json
// @Produce json
// @Success 200 {object} models.TagsQueryResponse
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/tags/select-values [get]
func QueryTagSelectValues(c echo.Context) error {
	var response = new(models.TagSelectValuesResponse)
	response.Code = 200

	var request = new(cc.ScrmPetVarietyRequest)
	err := CallPetTipsGrpc(c, request, func(grpc *cc.Client) (interface{}, error) {
		res, err := grpc.PetTips.QueryScrmPetVariety(grpc.Ctx, request)
		if err != nil {
			response.Code = 400
			response.Message = err.Error()
		}
		if res != nil {
			response.Code = res.Code
			response.Message = res.Message
			for _, item := range res.Data {
				response.Data = append(response.Data, &models.TagSelectValues{Key: item.Name, Value: item.Name, Extend: item.Extend})
			}
		}
		return response, err
	})
	return err
}

// TagOperate
// @Summary 内容标签获取
// @Tags 标签
// @Accept json
// @Produce json
// @Success 200 {object} cc.ContentTagResponse
// @Failure 400 {object} cc.ContentTagResponse
// @Router /boss/tags/content [GET]
func QueryContentTag(c echo.Context) error {

	client := cc.GetCustomerCenterClient()
	defer client.Close()

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	res, err := client.RPC.QueryContentTag(client.Ctx, &cc.ContentTagRequest{OrgId: cast.ToInt32(orgId)})
	if err != nil {
		return c.JSON(400, err.Error())
	}

	return c.JSON(200, res)
}

// TagOperate
// @Summary 新增标签或标签分组
// @Tags 标签
// @Accept json
// @Produce json
// @Param group_name query string true "分组名"
// @Param name query string false "标签名"
// @Success 200 {object} cc.TagOperateResponse
// @Failure 400 {object} cc.TagOperateResponse
// @Router /boss/tags/operate [POST]
func TagAdd(c echo.Context) error {
	out := new(cc.TagOperateResponse)
	in := new(cc.TagOperateRequest)
	out.Code = 400

	// 参数校验
	if err := c.Bind(in); err != nil {
		out.Message = "参数接收失败!"
		return c.JSON(400, out)
	}
	if in.GroupName == "" {
		out.Message = "分组名不能为空"
		return c.JSON(400, out)
	}
	if len(in.Name) > 30 || len(in.GroupName) > 30 {
		out.Message = "标签名最多10个汉字"
		if in.Name == "" {
			out.Message = "分组名最多10个汉字"
		}
		return c.JSON(400, out)
	}
	if strings.Contains(in.Name, ",") || strings.Contains(in.GroupName, ",") {
		out.Message = "分组名或标签名不允许包含英文逗号"
		return c.JSON(400, out)
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	in.OrgId = cast.ToInt32(orgId)

	// rpc请求
	in.Opt = "add"
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	res, err := client.RPC.TagOperate(client.Ctx, in)
	if err != nil {
		return c.JSON(400, err.Error())
	}

	return c.JSON(200, res)
}

// TagOperate
// @Summary 更新标签或标签分组
// @Tags 标签
// @Accept json
// @Produce json
// @Param group_name query string true "分组名"
// @Param name query string true "修改后的标签名"
// @Param old_name query string true "修改前的标签名"
// @Success 200 {object} cc.TagOperateResponse
// @Failure 400 {object} cc.TagOperateResponse
// @Router /boss/tags/operate [PUT]
func TagUpdate(c echo.Context) error {
	out := new(cc.TagOperateResponse)
	in := new(cc.TagOperateRequest)
	out.Code = 400

	// 参数校验
	if err := c.Bind(in); err != nil {
		out.Message = "参数接收失败!"
		return c.JSON(400, out)
	}
	if in.GroupName == "" || in.Name == "" || in.OldName == "" {
		out.Message = "标签名或原标签名不能为空!"
		return c.JSON(400, out)
	}
	if len(in.Name) > 30 {
		out.Message = "标签名最多10个汉字"
		return c.JSON(400, out)
	}
	if strings.Contains(in.Name, ",") {
		out.Message = "标签名不允许包含英文逗号"
		return c.JSON(400, out)
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	in.OrgId = cast.ToInt32(orgId)

	// rpc请求
	in.Opt = "update"
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	res, err := client.RPC.TagOperate(client.Ctx, in)
	if err != nil {
		return c.JSON(400, err.Error())
	}

	return c.JSON(200, res)
}

// TagOperate
// @Summary 删除标签或标签分组
// @Tags 标签
// @Accept json
// @Produce json
// @Param group_name query string true "分组名"
// @Param name query string false "标签名"
// @Success 200 {object} cc.TagOperateResponse
// @Failure 400 {object} cc.TagOperateResponse
// @Router /boss/tags/operate [DELETE]
func TagDelete(c echo.Context) error {
	out := new(cc.TagOperateResponse)
	in := new(cc.TagOperateRequest)
	out.Code = 400

	// 参数校验
	in.GroupName = c.QueryParam("group_name")
	in.Name = c.QueryParam("name")
	if in.GroupName == "" {
		out.Message = "分组名不能为空"
		return c.JSON(400, out)
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	in.OrgId = cast.ToInt32(orgId)

	// rpc请求
	in.Opt = "delete"
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	res, err := client.RPC.TagOperate(client.Ctx, in)
	if err != nil {
		return c.JSON(400, err.Error())
	}

	return c.JSON(200, res)
}
