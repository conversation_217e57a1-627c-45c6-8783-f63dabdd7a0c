package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"mime/multipart"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/google/uuid"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
)

//url ： /base/area/all
//dataJson : 数据对象转化成json字符串
func HttpPost(url string, dataJson []byte, Headers string) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")

	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}

	for k, v := range BjSignMap(url) {
		req.Header.Set(k, v)
	}

	res, err := client.Do(req)
	if err != nil {
		log.Println(err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	return body, err
}

func BjSignMap(url string) map[string]string {
	domainUrl := strings.Split(url, "//")[1]
	baseUrl := strings.Split(domainUrl, "/")[0]
	method := strings.Split(url, baseUrl)[1]
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("AppId=%s&Secret=%s&Url=%s&Timestamp=%s&Version=%s", config.GetString("bj.auth.appid"), config.GetString("bj.auth.secret"), method, Timestamp, config.GetString("bj.auth.version"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	arr := make(map[string]string)
	arr["focus-auth-appid"] = config.GetString("bj.auth.appid")
	arr["focus-auth-userid"] = "0"
	arr["focus-auth-username"] = "0"
	arr["focus-auth-version"] = config.GetString("bj.auth.version")
	arr["focus-auth-url"] = method
	arr["focus-auth-timestamp"] = Timestamp
	arr["focus-auth-sign"] = md5sign
	return arr
}

func HttpGet(url, source, ua string, param map[string]interface{}) (int, string) {
	return action(url, source, ua, http.MethodGet, "", param)
}

func HttpPostForm(url, source, ua string, param map[string]interface{}) (int, string) {
	return action(url, source, ua, http.MethodPost, "application/x-www-form-urlencoded", param)
}

func HttpPostJSON(url, source, ua string, param map[string]interface{}) (int, string) {
	return action(url, source, ua, http.MethodPost, "application/json;charset=UTF-8", param)
}

func action(uri, source, ua string, httpMethod string, contentType string, param map[string]interface{}) (int, string) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("http.action", err)
		}
	}()
	var req *http.Request
	switch httpMethod {
	case http.MethodGet:
		if param != nil {
			uri += "?" + mapToValues(param).Encode()
		}
		req, _ = http.NewRequest(httpMethod, uri, nil)
	case http.MethodPost:
		httpMethod = http.MethodPost
		var reader io.Reader

		if contentType == "application/x-www-form-urlencoded" {
			reader = strings.NewReader(mapToValues(param).Encode())
		} else if contentType == "application/json;charset=UTF-8" {
			byteData, _ := json.Marshal(param)
			reader = bytes.NewReader(byteData)
		}
		req, _ = http.NewRequest(httpMethod, uri, reader)
		req.Header.Add("Content-Type", contentType)
	default:
		return 0, "不支持的请求类型"
	}

	// for k, v := range httpHeader {
	// 	req.Header.Add(k, v)
	// }
	//ul := uuid.NewV4()
	//sn := strings.ReplaceAll(ul.String(), "-", "")
	//req.Header.Add("sn", sn)
	//req.Header.Add("source", source)
	//req.Header.Add("ua", ua)
	//req.Header.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))

	client := http.Client{Timeout: time.Second * 30, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}

	res, err := client.Do(req)
	if err != nil {
		glog.Error(err)
		return 0, err.Error()
	}

	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)
	return res.StatusCode, string(body)
}

func mapToValues(mp map[string]interface{}) url.Values {
	v := url.Values{}
	for key, val := range mp {
		switch val.(type) {
		case int:
			v.Add(key, strconv.Itoa(val.(int)))
		case int32:
			v.Add(key, strconv.Itoa(int(val.(int32))))
		case int64:
			v.Add(key, strconv.Itoa(int(val.(int64))))
		case float64:
			v.Add(key, strconv.FormatFloat(val.(float64), 'E', -1, 64))
		case float32:
			v.Add(key, strconv.FormatFloat(float64(val.(float32)), 'E', -1, 32))
		default:
			v.Add(key, val.(string))
		}
	}
	//glog.Info(v.Encode())
	return v
}

//七牛上传结果
type QiNiuUploadResult struct {
	Url string `json:"url"`
	Err string `json:"error"`
}

// UploadFile 上传文件需要的信息
// 文件路径方法 reader, err := os.Open(fileName)
// excel方式 b,err := file.WriteToBuffer();reader = bytes.NewReader(b.Bytes())
type UploadFile struct {
	Name   string
	Reader io.Reader
}

// UploadQiNiuResponse 上传到七牛响应
type UploadQiNiuResponse struct {
	FileName string
	Size     int
	Url      string
	Error    string
}

// ToQiNiu 上传文件到七牛云
func (uf *UploadFile) ToQiNiu() (url string, err error) {
	if len(uf.Name) < 1 {
		return "", errors.New("文件名称不能为空")
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", uf.Name)
	if _, err = io.Copy(part, uf.Reader); err != nil {
		return
	}
	if err = writer.Close(); err != nil {
		return
	}

	host := config.GetString("file-upload-url")
	if len(host) == 0 {
		host = "https://api.rp-pet.com"
	}
	httpResp, err := http.Post(host+"/fss/newup", writer.FormDataContentType(), body)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	resBody, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return
	}

	res := new(UploadQiNiuResponse)
	if err = json.Unmarshal(resBody, res); err != nil {
		return "", errors.New("解析响应body出错 " + err.Error())
	}
	if httpResp.StatusCode >= 400 {
		if len(res.Error) == 0 {
			res.Error = httpResp.Status
		}
		return "", errors.New("请求出错 " + res.Error)
	}

	res.Url = strings.Replace(res.Url, "http://", "https://", 1)

	return res.Url, nil
}

// UploadExcelToQiNiu 上传excel文件到七牛云
func UploadExcelToQiNiu(file *excelize.File, name string) (url string, err error) {
	b, err := file.WriteToBuffer()
	if err != nil {
		return
	}
	if len(name) < 1 {
		name = uuid.NewString() + ".xlsx"
	}
	uf := &UploadFile{
		Name:   name,
		Reader: bytes.NewReader(b.Bytes()),
	}

	return uf.ToQiNiu()
}

// 发送GET请求
// url：         请求地址
// response：    请求返回的内容
func HttpGetUrl(url string) string {

	// 超时时间：5秒
	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	var buffer [512]byte
	result := bytes.NewBuffer(nil)
	for {
		n, err := resp.Body.Read(buffer[0:])
		result.Write(buffer[0:n])
		if err != nil && err == io.EOF {
			break
		} else if err != nil {
			panic(err)
		}
	}

	return result.String()
}

// 文件下载
// url：         请求地址
// response：    请求返回的内容
func DownFile(url string) (bts []byte, err error) {
	// 超时时间
	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	var buffer [512]byte
	result := bytes.NewBuffer(nil)
	for {
		n, err := resp.Body.Read(buffer[0:])
		result.Write(buffer[0:n])
		if err != nil || err == io.EOF {
			break
		}
	}

	return result.Bytes(), err
}
