package controller

import (
	"_/dto"
	"_/proto/pc"
	"_/utils"
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// @Summary 切仓操作记录
// @Tags 切仓管理
// @Accept plain
// @Produce plain
// @Param page_size query int true "每页大小"
// @Param page query int true "当前页"
// @Param keyword query string false "搜索关键字"
// @Param channel_id query int false "渠道id(0-所有平台,1-阿闻，2-美团，3-饿了么，4-京东到家，10-阿闻竖屏自提)"
// @Param promoter query int false "发起人 0:自己;1:全部;2:其他"
// @Success 200 {object} dto.CommonPageHttpResponse{data=[]pc.SwitchWarehouseLog}
// @Failure 400 {object} dto.CommonPageHttpResponse{}
// @Router /boss/warehouse/switch-warehouse [get]
func ListSwitchWarehouseLog(c echo.Context) error {
	resp := dto.CommonPageHttpResponse{
		Code: http.StatusBadRequest,
	}
	var params dto.ListSwitchWarehouseLogRequest
	if err := c.Bind(&params); err != nil {
		resp.Message = fmt.Sprintf("绑定参数异常:%+v", err)
		return utils.ResponseJSON(c, resp)
	}
	if params.Page == 0 {
		params.Page = 1
	}
	if params.PageSize == 0 {
		params.PageSize = 20
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Errorf("ListSwitchWarehouseLog 用户未登录:%+v", err)
		resp.Message = fmt.Sprintf("用户未登录:%+v", err)
		return utils.ResponseJSON(c, resp)
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	req := &pc.ListSwitchWarehouseLogRequest{
		Page:      params.Page,
		PageSize:  params.PageSize,
		ChannelId: params.ChannelId,
		Keyword:   params.Keyword,
	}
	if params.Promoter == 0 {
		req.CreateId = userInfo.UserNo
	}
	out, err := client.RPC.ListSwitchWarehouseLog(client.Ctx, req)
	if err != nil {
		glog.Errorf("ListSwitchWarehouseLog 调用product-center接口异常:%+v", err)
		resp.Message = err.Error()
		return utils.ResponseJSON(c, resp)
	}
	if out.List == nil {
		out.List = []*pc.SwitchWarehouseLog{}
	}

	resp.Code = http.StatusOK
	resp.Data = out.List
	resp.Total = out.Total
	return utils.ResponseJSON(c, resp)
}
