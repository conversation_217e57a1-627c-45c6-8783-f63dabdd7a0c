name: 'Tests: shellcheck'

on: [pull_request, push]

permissions:
  contents: read

jobs:
  shellcheck_matrix:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        shell:
          - bash
          - sh
          - dash
          - ksh
        file:
          - nvm.sh
        include:
          - shell: bash
            file: install.sh # only supported on bash
          - shell: bash
            file: bash_completion # only needed in bash/zsh
          - shell: bash
            file: nvm-exec # only runs in bash

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@v2
        with:
          allowed-endpoints:
            ghcr.io:443
            github.com:443
            pkg-containers.githubusercontent.com:443
            formulae.brew.sh:443
      - uses: actions/checkout@v4
      - name: Set up Homebrew
        uses: Homebrew/actions/setup-homebrew@master
      - name: Install latest shellcheck
        run: brew install shellcheck
        env:
          HOMEBREW_NO_ANALYTICS: 1
      - run: which shellcheck
      - run: shellcheck --version
      - name: Run shellcheck on ${{ matrix.file }}
        run: shellcheck -s ${{ matrix.shell }} ${{ matrix.file }}

  shellcheck:
      permissions:
        contents: none
      needs: [shellcheck_matrix]
      runs-on: ubuntu-latest
      steps:
        - run: true
