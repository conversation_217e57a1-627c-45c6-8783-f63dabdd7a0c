package controller

import (
	"_/models"
	crm "_/proto/crm"
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/tealeg/xlsx"
	"net/http"
	"net/url"
	"time"
)

// @Summary 删除标签组
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.FlagGroupInfo true " "
// @Success 200 {object} crm.BaseResponse
// @Failure 400 {object} crm.BaseResponse
// @Router /boss/crm/delete_flag_group [POST]
func DeleteFlagGroup(c echo.Context) error {

	model := new(crm.FlagGroupInfo)
	var res crm.BaseResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("BOSS删除标签组参数:" + signStr)

	grpcRes, err := client.RPC.DeleteFlagGroup(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 修改标签组
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.FlagGroupInfo true " "
// @Success 200 {object} crm.BaseResponse
// @Failure 400 {object} crm.BaseResponse
// @Router /boss/crm/update_flag_group [POST]
func UpdateFlagGroup(c echo.Context) error {

	model := new(crm.FlagGroupInfo)
	var res crm.BaseResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("BOSS修改标签组参数:" + signStr)

	grpcRes, err := client.RPC.UpdateFlagGroup(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 查询标签组列表
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.FlagGroupListRequest true " "
// @Success 200 {object} crm.FlagGroupListResponse
// @Failure 400 {object} crm.FlagGroupListResponse
// @Router /boss/crm/get_flag_group_list [POST]
func GetFlagGroupList(c echo.Context) error {

	model := new(crm.FlagGroupListRequest)
	var res crm.FlagGroupListResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("BOSS查询标签组列表参数:" + signStr)

	grpcRes, err := client.RPC.GetFlagGroupList(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 创建或者修改标签和标签组
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.CreateFlagRequest true " "
// @Success 200 {object} crm.BaseResponse
// @Failure 400 {object} crm.BaseResponse
// @Router /boss/crm/create_flag [POST]
func CreateFlag(c echo.Context) error {

	glog.Info("BOSS创建或者修改标签和标签组")
	model := new(crm.CreateFlagRequest)
	var res crm.BaseResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("BOSS创建或者修改标签和标签组请求参数:" + signStr)

	grpcRes, err := client.RPC.CreateFlag(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 查询标签列表
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.FlagListRequest true " "
// @Success 200 {object} crm.FlagListResponse
// @Failure 400 {object} crm.FlagListResponse
// @Router /boss/crm/get_flag_list [POST]
func GetFlagList(c echo.Context) error {

	glog.Info("BOSS查询标签列表")
	model := new(crm.FlagListRequest)
	var res crm.FlagListResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("BOSS查询标签列表请求参数:" + signStr)

	grpcRes, err := client.RPC.GetFlagList(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 删除标签
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.FlagInfo true " "
// @Success 200 {object} crm.BaseResponse
// @Failure 400 {object} crm.BaseResponse
// @Router /boss/crm/delete_flag [POST]
func DeleteFlag(c echo.Context) error {

	glog.Info("BOSS删除标签")
	model := new(crm.FlagInfo)
	var res crm.BaseResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("BOSS删除标签请求参数:" + signStr)

	grpcRes, err := client.RPC.DeleteFlag(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

//get_flag_info

// @Summary 查询单个标签
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.FlagInfo true " "
// @Success 200 {object} crm.FlagResponse
// @Failure 400 {object} crm.FlagResponse
// @Router /boss/crm/get_flag_info [POST]
func GetFlagInfo(c echo.Context) error {

	glog.Info("BOSS获取单个企业微信标签")
	model := new(crm.FlagInfo)
	var res crm.FlagResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("BOSS获取单个企业微信标签请求参数:" + signStr)

	grpcRes, err := client.RPC.GetFlagInfo(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 根据条件给用户打标（测试用，正常是定时任务凌晨自动跑）
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.FlagInfo true " "
// @Success 200 {object} crm.BaseResponse
// @Failure 400 {object} crm.BaseResponse
// @Router /boss/crm/synchronize_to_wechat [POST]
func SynchronizeToWeChat(c echo.Context) error {

	glog.Info("BOSS同步标签到企业微信")
	model := new(crm.FlagInfo)
	var res crm.BaseResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("BOSS同步标签到企业微信请求参数:" + signStr)

	grpcRes, err := client.RPC.SynchronizeToWeChat(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 企微用户列表
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body models.FlagCustomerRequest true " "
// @Success 200 {object} models.FlagCustomerListResponse
// @Failure 400 {object} models.FlagCustomerListResponse
// @Router /boss/crm/get_customer_list [POST]
func GetCustomerList(c echo.Context) error {
	model := models.FlagCustomerRequest{}
	var res models.FlagCustomerListResponse
	res.Code = 200
	if err := c.Bind(&model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	customerRequest := &crm.CustomerRequest{
		NickName:     model.NickName,
		CustomerType: model.CustomerType,
		CustomerSex:  model.CustomerSex,
		RemarkMobile: model.RemarkMobile,
		PageIndex:    model.PageIndex,
		PageSize:     model.PageSize,
	}
	if model.IsExport == 0 {
		if customerRequest.PageIndex == 0 {
			customerRequest.PageIndex = 1
		}
		if customerRequest.PageSize == 0 {
			customerRequest.PageSize = 10
		}
	} else {
		customerRequest.PageSize = 0
		customerRequest.PageIndex = 0
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	grpcRes, err := client.RPC.GetCustomerList(client.Ctx, customerRequest)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		res.Code = grpcRes.Code
		res.Error = grpcRes.Error
		return c.JSON(400, res)
	}
	res.PageCount = grpcRes.Total
	err = json.Unmarshal([]byte(grpcRes.Data), &res.Data)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	return c.JSON(200, res)
}

// @Summary 企微用户列表
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body models.FlagCustomerRequest true " "
// @Success 200 {object} models.FlagCustomerDetailResponse
// @Failure 400 {object} models.FlagCustomerDetailResponse
// @Router /boss/crm/get_customer_detail [POST]
func GetCustomerDetail(c echo.Context) error {
	model := models.FlagCustomerRequest{}
	var res models.FlagCustomerDetailResponse
	res.Code = 200
	if err := c.Bind(&model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	customerRequest := &crm.CustomerRequest{
		Userid:         model.Userid,
		ExternalUserid: model.ExternalUserid,
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	//获取详情信息
	grpcRes, err := client.RPC.GetCustomerDetail(client.Ctx, customerRequest)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		res.Code = 400
		res.Error = grpcRes.Error
		return c.JSON(400, res)
	}
	err = json.Unmarshal([]byte(grpcRes.Data), &res.Data.FlagCustomerDetail)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	//获取客户标签信息
	grpcFlagRes, err := client.RPC.GetCustomerFlagList(client.Ctx, customerRequest)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcFlagRes.Code != 200 {
		res.Code = 400
		res.Error = grpcFlagRes.Error
		return c.JSON(400, res)
	}
	res.Data.FlagList = grpcFlagRes.FlagGroupList
	return c.JSON(200, res)
}

// @Summary 导出客户列表
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body models.FlagCustomerRequest true " "
// @Success 200 {object} models.FlagCustomerListResponse
// @Failure 400 {object} models.FlagCustomerListResponse
// @Router /boss/crm/export_customer_list [POST]
func ExportCustomerList(c echo.Context) error {
	model := models.FlagCustomerRequest{}
	list := []models.FlagCustomerExportList{}
	var res models.FlagCustomerListResponse
	res.Code = 200
	if err := c.Bind(&model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	customerRequest := &crm.CustomerRequest{
		NickName:     model.NickName,
		CustomerType: model.CustomerType,
		CustomerSex:  model.CustomerSex,
		RemarkMobile: model.RemarkMobile,
		PageIndex:    model.PageIndex,
		PageSize:     model.PageSize,
	}
	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()
	if grpcRes, err := client.RPC.ExportCustomerList(client.Ctx, customerRequest); err != nil {
		return c.JSON(400, err)
	} else {
		err = json.Unmarshal([]byte(grpcRes.Data), &list)
		if err != nil {
			res.Code = 400
			res.Error = err.Error()
			return c.JSON(400, res)
		}
	}

	// 生成一个新的文件
	file := xlsx.NewFile()
	// 添加sheet页
	sheet, _ := file.AddSheet("Sheet1")
	// 插入表头
	titleRow := sheet.AddRow()
	titleList := []string{"昵称", "用户类型", "性别", "添加好友时间", "添加好友userid", "添加好友至今消费次数", "添加好友至今消费金额",
		"用户来源", "最后消费时间", "会员卡用户id", "保障卡用户id", "近1年消费次数", "近1年消费金额", "用户积分"}
	for _, v := range titleList {
		cell := titleRow.AddCell()
		cell.Value = v
		//表头字体颜色
		cell.GetStyle().Font.Color = "00000000"
		cell.GetStyle().Font.Bold = true
		//居中显示
		cell.GetStyle().Alignment.Horizontal = "center"
		cell.GetStyle().Alignment.Vertical = "center"
	}

	// 插入内容
	for _, v := range list {
		row := sheet.AddRow()
		row.WriteStruct(&v, -1)
	}

	//将数据存入buff中
	var buff bytes.Buffer
	if err := file.Write(&buff); err != nil {
		panic(err)
	}
	//设置请求头  使用浏览器下载
	c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("企微客户信息")))
	return c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
}

// @Summary 导入用户标签
// @Tags 企业微信标签管理
// @Accept json
// @Produce json
// @Param model body crm.CreateBatchTaskRequest true " "
// @Success 200 {object} crm.CreateBatchTaskResponse
// @Failure 400 {object} crm.CreateBatchTaskResponse
// @Router /boss/crm/export_customer_list [POST]
func CreateCrmTask(c echo.Context) error {

	glog.Info("导入用户标签")
	model := new(crm.CreateBatchTaskRequest)
	var res crm.CreateBatchTaskResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	signByte, _ := json.Marshal(model)
	signStr := string(signByte)
	glog.Info("导入用户标签请求参数:" + signStr)

	grpcRes, err := client.RPC.CreateBatchTask(client.Ctx, model)
	if err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if grpcRes.Code != 200 {
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 导入用户标签列表查询
// @Tags 企业微信标签管理
// @Accept plain
// @Produce plain
// @Param page query int false "当前页，默认第1页"
// @Param pageSize query int false "每页显示数据条数，默认显示10条"
// @Param sort query string false "排序类型:createTimeDesc：按创建时间顺序倒序；"
// @Param task_status query int false "任务状态:1:调度中;2:进行中;3:已完成；"
// @Param task_content query int false "任务内容:1:导入用户标签;"
// @Param status query int false "状态:1:正常;2:冻结;"
// @Success 200 {object} crm.GetTaskListResponse
// @Failure 400 {object} crm.GetTaskListResponse
// @Router /boss/crm/get_task_list [get]
func GetCrmTaskList(c echo.Context) error {
	//接收参数
	page := c.QueryParam("page")
	pageSize := c.QueryParam("pageSize")
	sort := c.QueryParam("sort")                //排序类型:createTimeDesc：按创建时间顺序倒序；
	taskStatus := c.QueryParam("task_status")   //任务状态:1:调度中;2:进行中;3:已完成；
	taskContent := c.QueryParam("task_content") //"任务内容:1:导入用户标签;"
	status := c.QueryParam("status")            //状态:1:正常;2:冻结;

	//client := GetDcProductClient()
	//defer client.Conn.Close()
	//defer client.Cf()

	//组装request
	rpcRequest := crm.GetTaskListRequest{
		Createtime: time.Now().Format("2006-01-02"),
	}
	rpcRequest.Page = cast.ToInt32(page)
	if rpcRequest.Page == 0 {
		rpcRequest.Page = 1
	}
	rpcRequest.PageSize = cast.ToInt32(pageSize)
	if rpcRequest.PageSize == 0 {
		rpcRequest.PageSize = 10
	}
	if sort != "" {
		rpcRequest.Sort = sort
	}
	rpcRequest.TaskStatus = cast.ToInt32(taskStatus)
	rpcRequest.TaskContent = cast.ToInt32(taskContent)
	rpcRequest.Status = cast.ToInt32(status)

	//err := errors.New("")
	//var userInfo models.LoginUserInfo
	//if userInfo, err = utils.GetPayloadDirectlyToInterface(c); err != nil {
	//	glog.Error(err)
	//	return c.JSON(200, &pc.ChannelStoreProductResponse{Code: 400, Error: err.Error()})
	//}

	//rpcRequest.CreateId = userInfo.UserNo

	client := GetScrmCenterClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.GetTaskList(client.Ctx, &rpcRequest); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}
