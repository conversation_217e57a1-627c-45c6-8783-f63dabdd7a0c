package route

import (
	"_/controller"
	"github.com/labstack/echo/v4"
)

func integralStore(e *echo.Group) {
	g := e.Group("/integral-store/")
	g.GET("goods", controller.ISGoods)
	g.POST("goods", controller.ISGoodsStore)
	g.GET("goods/detail", controller.ISGoodsDetail)
	g.<PERSON>TCH("goods", controller.ISGoodsPatch)

	g.GET("order/list", controller.ISOrders)
	g.GET("order/detail", controller.ISOrderDetail)
	g.GET("order/export", controller.ISOrderExport)
	g.GET("order/export/list", controller.ISOrderExportList)
	g.POST("order/express", controller.ISExpressStore)

	g.GET("order-express/company-list", controller.ISExpressCompanyList)
	g.GET("order-express/import-template", controller.ISExpressImportTemplate)
	g.POST("order-express/import", controller.ISExpressImport)
	g.GET("order-express/import/list", controller.ISExpressImportList)

	// 查询会员等级列表
	g.GET("member/lever", controller.MemberLever)
}

func integralRecord(e *echo.Group) {
	g := e.Group("/integral-record/")
	g.GET("page-list", controller.IntegralRecordPageList)
	g.GET("integralCurrentGet", controller.IntegralCurrentGet)

}
