package controller

import (
	"_/proto/dac"
	"_/utils"
	"context"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @Summary 获取文件上传记录列表数据
// @Tags 文件上传记录
// @Accept json
// @Produce plain
// @Param id query int true "id"
// @Param create_user_id query string true "创建人ID"
// @Param valid query int true "1 只查有效，0所有"
// @Param type query int true "文件类型 1 APP微页面"
// @Param page_size query string true "每页大小"
// @Param page_index query string true "当前页"
// @Success 200 {object} dac.GetFileUploadRecordsResponse
// @Failure 400 {object} dac.GetFileUploadRecordsResponse
// @Router /boss/uploadRecord/list [get]
func GetFileUploadRecords(c echo.Context) error {
	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()
	model := dac.GetFileUploadRecordsRequest{
		Id:           cast.ToInt32(c.QueryParam("id")),
		CreateUserId: c.QueryParam("create_user_id"),
		Valid:        cast.ToInt32(c.QueryParam("valid")),
		Type:         cast.ToInt32(c.QueryParam("type")),
		PageSize:     cast.ToInt32(c.QueryParam("page_size")),
		PageIndex:    cast.ToInt32(c.QueryParam("page_index")),
	}
	out, err := client.RPC.GetFileUploadRecords(context.Background(), &model)
	if err != nil || out.Code != 200 {
		return c.JSON(400, dac.GetFileUploadRecordsResponse{Code: 400, Message: err.Error()})
	}

	return c.JSON(200, out)
}

// @Summary 新增文件上传记录
// @Tags 文件上传记录
// @Accept json
// @Produce plain
// @Param model body dac.AddFileUploadRecordRequest true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/uploadRecord/add [post]
func AddFileUploadRecord(c echo.Context) error {
	model := new(dac.AddFileUploadRecordRequest)
	var res dac.BaseResponse
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	if len(model.FileUrl) <= 0 {
		res.Code = 400
		res.Message = "路径不能为空"
		return c.JSON(400, res)
	}
	if model.Type <= 0 {
		res.Code = 400
		res.Message = "文件类型不能为空"
		return c.JSON(400, res)
	}

	if claims, err := utils.GetPayloadDirectly(c); err != nil {
		res.Code = 400
		res.Message = "获取不到用户信息"
		return c.JSON(400, res)
	} else {
		model.CreateUserId = utils.InterfaceToString(claims["userno"])
		model.CreateUserName = utils.InterfaceToString(claims["name"])
	}

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.AddFileUploadRecord(context.Background(), model)
	if err != nil || out.Code != 200 {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(200, out)
}

// @Summary 修改文件上传记录状态
// @Tags 文件上传记录
// @Accept json
// @Produce plain
// @Param model body dac.UpdateFileUploadRecordStatusRequest true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/uploadRecord/update [post]
func UpdateFileUploadRecordStatus(c echo.Context) error {
	model := new(dac.UpdateFileUploadRecordStatusRequest)
	var res dac.BaseResponse
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	if model.Id <= 0 {
		res.Code = 400
		res.Message = "id不能为空"
		return c.JSON(400, res)
	}

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.UpdateFileUploadRecordStatus(context.Background(), model)
	if err != nil || out.Code != 200 {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(200, out)
}
