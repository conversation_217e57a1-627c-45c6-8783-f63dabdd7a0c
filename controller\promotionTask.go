package controller

import (
	"_/models"
	"_/proto/mk"
	"bytes"
	"fmt"
	"github.com/spf13/cast"
	"github.com/tealeg/xlsx"
	"net/http"
	"net/url"
	"reflect"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// grpc 函数调用
type PromotionTaskGrpcFuncCall func(grpc *mk.Client) (response interface{}, err error)

// 请求处理
func PromotionTaskGrpcProcess(c echo.Context, request interface{}, grpcFun PromotionTaskGrpcFuncCall) error {
	// 400 错误代码返回
	var badResponse = new(mk.BaseResponse)
	badResponse.Code = mk.Code_parameterError

	//获取请求参数到实体对象
	err := c.Bind(request)
	if err != nil {
		glog.Error(err)
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 获取grpc 链接
	var conn = mk.GetTaskServiceClient()
	// 关闭链接
	defer conn.Close()
	if conn == nil {
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = "内部Grpc通讯错误"
		return c.JSON(http.StatusBadRequest, badResponse)
	}
	//调用Grpc方法
	response, err := grpcFun(conn)
	if err != nil {
		glog.Error(err)
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}
	if response != nil {
		// 反射查询Grpc响应
		var responseType = reflect.ValueOf(response)
		if responseType.Kind() == reflect.Ptr {
			responseType = responseType.Elem()
		}
		// 查询code 属性
		codePrototy := responseType.FieldByName("Code")
		if codePrototy.Kind() != reflect.Invalid {
			grpcCode := codePrototy.Int()
			// 解析错误代码
			if grpcCode > 400 {
				badResponse.Code = 400
				if grpcCode == int64(mk.Code_queryDbException) {
					badResponse.Error = "查询数据库异常"
				}
				if grpcCode == int64(mk.Code_saveDbException) {
					badResponse.Error = "保存数据库异常"
				}
				if grpcCode == int64(mk.Code_businessError) {
					// 是否有Message 信息
					var messageFiled = codePrototy.FieldByName("Message")
					if messageFiled.Kind() != reflect.Invalid {
						badResponse.Error = messageFiled.String()
					}
				}
				return c.JSON(http.StatusBadRequest, badResponse)
			}
		}

		return c.JSON(http.StatusOK, response)
	}
	return nil
}

// QueryPromotionTask
// @Summary 活动创建任务列表--根据登录用户信息查询其创建的活动任务列表
// @Tags v4.4
// @Accept json
// @Produce json
// @param request query mk.PromotionTaskQueryRequest true "查询参数组合"
// @Success 200 {object} mk.PromotionTaskQueryResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/QueryPromotionTask [get]
func QueryPromotionTask(c echo.Context) error {
	var request = new(mk.PromotionTaskQueryRequest)
	return PromotionTaskGrpcProcess(c, request, func(grpc *mk.Client) (response interface{}, err error) {
		request.PageIndex = cast.ToInt32(c.QueryParam("pageIndex"))
		request.PageSize = cast.ToInt32(c.QueryParam("pageSize"))
		request.Types = cast.ToInt32(c.QueryParam("types"))
		request.CreateUserId = c.QueryParam("createUserId")
		request.OrgId = cast.ToInt64(c.Request().Header.Get("Org_id"))
		return grpc.Task.QueryPromotionTask(grpc.Ctx, request)
	})
}

// ExportPromotionTask
// @Summary 活动创建导出
// @Tags v4.4
// @Accept json
// @Produce json
// @param request query mk.QueryByIdRequest true "查询参数组合"
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/ExportPromotionTask [get]
func ExportPromotionTask(c echo.Context) error {
	var request = new(mk.QueryByIdRequest)

	return PromotionTaskGrpcProcess(c, request, func(grpc *mk.Client) (response interface{}, err error) {
		// 返回响应内容
		var badResponse = &mk.BaseResponse{Code: mk.Code_success}
		taskInfo, err1 := grpc.Task.QueryPromotionTaskById(grpc.Ctx, &mk.QueryByIdRequest{Id: request.Id})
		if err1 != nil {
			badResponse.Code = mk.Code_businessError
			badResponse.Message = err1.Error()
			return badResponse, err1
		}

		var allDatas []*models.PromotionTaskDetailExport

		for i := 0; i < 512; i++ {
			var pageRequest = new(mk.PromotionTaskDetailQueryRequest)
			pageRequest.TaskId = request.Id
			pageRequest.PageIndex = int32(i) + 1
			pageRequest.PageSize = 2048
			// 调用远程信息
			taskDetailResponse, err2 := grpc.Task.QueryPromotionTaskDetail(grpc.Ctx, pageRequest)
			if err2 != nil {
				glog.Error(err2)
			}
			if taskDetailResponse != nil && taskDetailResponse.Code == mk.Code_success {
				for _, v := range taskDetailResponse.Data {
					if v.Code == 1 {
						continue
					}
					_allDatas := models.PromotionTaskDetailExport{
						ShopName:     v.ShopName,
						Message:      v.Message,
						UpdateDate:   v.UpdateDate,
						ProductSkuId: v.ProductSkuId,
						ProductName:  v.ProductName,
					}

					if taskInfo.Data.Types != 2 {
						_allDatas.ProductSkuId = ""
						_allDatas.ProductName = ""
					}
					allDatas = append(allDatas, &_allDatas)
				}
				if len(taskDetailResponse.Data) == 0 || len(taskDetailResponse.Data) < 1024 {
					break
				}
			}
		}

		if len(allDatas) > 0 {
			// 生成一个新的文件
			file := xlsx.NewFile()
			// 添加sheet页
			sheet, _ := file.AddSheet("Sheet1")
			// 插入表头
			titleRow := sheet.AddRow()
			titleList := []string{"门店", "失败原因", "处理时间", "商品代码", "商品名称"}
			for _, v := range titleList {
				cell := titleRow.AddCell()
				cell.Value = v
				//表头字体颜色
				cell.GetStyle().Font.Color = "00000000"
				cell.GetStyle().Font.Bold = true
				//居中显示
				cell.GetStyle().Alignment.Horizontal = "center"
				cell.GetStyle().Alignment.Vertical = "center"
			}

			// 插入内容
			for _, v := range allDatas {
				row := sheet.AddRow()
				row.WriteStruct(v, -1)
			}

			var buff bytes.Buffer
			if err := file.Write(&buff); err != nil {
				badResponse.Code = 400
				badResponse.Message = "导出文件失败"
			} else {
				fileName := "异步任务异常列表_" + time.Now().Format("20060102150405")
				c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s.xlsx", url.PathEscape(fileName)))
				return nil, c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
			}
		} else {
			badResponse.Code = mk.Code_businessError
			badResponse.Message = "没有数据可以导出"
		}

		return badResponse, nil
	})
}
