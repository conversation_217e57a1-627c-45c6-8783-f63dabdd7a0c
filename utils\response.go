package utils

import (
	"net/http"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"github.com/labstack/echo/v4"
)

type commonResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	// Error   string `json:"error"`
	//列表总记录数
	TotalCount int `json:"data_count"`
}

type ResponseGrpc struct {
	commonResponse
	Details interface{} `json:"data"`
}

func (r *ResponseGrpc) Successful(ctx echo.Context) error {
	var js []byte
	if r.Code == 0 {
		r.Code = http.StatusOK
	}
	var json = jsoniter.ConfigCompatibleWithStandardLibrary
	if r.Details == nil {
		r.Details = ""
	}
	js, _ = json.Marshal(r)
	return ctx.String(http.StatusOK, string(js))
}

func (r *ResponseGrpc) Failed(ctx echo.Context) error {
	if r.Code == 0 {
		r.Code = http.StatusBadRequest
	}
	if strings.Contains(r.Message, "=") {
		a := strings.Split(r.Message, "=")
		r.Message = strings.TrimSpace(a[len(a)-1])
	}
	var json = jsoniter.ConfigCompatibleWithStandardLibrary
	js, _ := json.Marshal(r)
	return ctx.String(http.StatusOK, string(js))
}
