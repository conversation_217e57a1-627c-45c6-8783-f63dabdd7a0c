#!/bin/sh

for i in $(seq 1 10)
  do
  rm -f "../../../alias/test-stable-$i"
  rm -rf "../../../v0.0.$i"
  rm -f "../../../alias/test-unstable-$i"
  rm -rf "../../../v0.1.$i"
  rm -rf "../../../alias/test-iojs-$i"
  rm -rf "../../../versions/io.js/v0.2.$i"
done

rm -f "../../../alias/stable"
rm -f "../../../alias/unstable"
rm -f "../../../alias/node"
rm -f "../../../alias/iojs"
rm -f "../../../alias/default"
rm -f "../../../alias/test-blank-lines"
rm -f "../../../alias/test-multi-lines"
