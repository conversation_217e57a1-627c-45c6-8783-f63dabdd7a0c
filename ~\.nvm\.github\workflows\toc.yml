name: update readme TOC

on: [push]

permissions:
  contents: read

jobs:
  _:
    permissions:
      contents: write
    name: "update readme TOC"

    runs-on: ubuntu-latest

    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@v2
      with:
        allowed-endpoints:
          github.com:443
          registry.npmjs.org:443
          api.github.com:443
    - uses: actions/checkout@v4
      with:
        # https://github.com/actions/checkout/issues/217#issue-599945005
        # pulls all commits (needed for lerna / semantic release to correctly version)
        fetch-depth: "0"

    # pulls all tags (needed for lerna / semantic release to correctly version)
    - run: git fetch --depth=1 origin +refs/tags/*:refs/tags/*
    - uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
    - run: npm install
    - run: npm run doctoc
    - name: commit changes
      uses: ljharb/actions-js-build/commit@v3+amendpush
      with:
        amend: true
        force: true
