package controller

import (
	"_/proto/oc"
	"_/utils"
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
	"google.golang.org/grpc/status"
)

// @summary 会员卡实体卡订单列表 vip-3.1
// @Tags 付费会员卡实体卡
// @Accept json
// @Produce json
// @Param model query oc.GetPhysicalVipCardOrderListRequest true " "
// @Success 200 {object} oc.GetPhysicalVipCardOrderListResponse
// @Failure 400 {object} oc.GetPhysicalVipCardOrderListResponse
// @Router /boss/vip/physical/order/list [GET]
func GetPhysicalVipCardOrderList(c echo.Context) error {
	req := &oc.GetPhysicalVipCardOrderListRequest{
		PageIndex:        cast.ToInt32(c.QueryParam("page_index")),
		PageSize:         cast.ToInt32(c.QueryParam("page_size")),
		OrderSn:          c.QueryParam("order_sn"),
		MemberMobile:     c.QueryParam("member_mobile"),
		PaymentTimeStart: c.QueryParam("payment_time_start"),
		PaymentTimeEnd:   c.QueryParam("payment_time_end"),
		OrderState:       cast.ToInt32(c.QueryParam("order_state")),
	}
	logPrefix := fmt.Sprintf("GetPhysicalVipCardOrderList====boss====入参：%s", kit.JsonEncode(req))

	glog.Info(logPrefix)
	client := oc.GetOrderServiceClient()
	out, err := client.VC.GetPhysicalVipCardOrderList(client.Ctx, req)
	if err != nil {
		glog.Error(logPrefix, "client.VC.GetPhysicalVipCardOrderList错误：", err.Error())
		return c.JSON(400, &oc.GetPhysicalVipCardOrderListResponse{Code: http.StatusBadRequest, Message: status.Convert(err).Message()})
	}
	return c.JSON(http.StatusOK, out)
}

// @summary 会员卡实体卡订单列表 vip-3.1
// @Tags 付费会员卡实体卡
// @Accept json
// @Produce json
// @Param model query oc.GetPhysicalVipCardOrderDetailRequest true " "
// @Success 200 {object} oc.GetPhysicalVipCardOrderDetailResponse
// @Failure 400 {object} oc.GetPhysicalVipCardOrderDetailResponse
// @Router /boss/vip/physical/order/detail [GET]
func GetPhysicalVipCardOrderDetail(c echo.Context) error {
	req := &oc.GetPhysicalVipCardOrderDetailRequest{
		OrderSn: c.QueryParam("order_sn"),
	}
	logPrefix := fmt.Sprintf("GetPhysicalVipCardOrderDetail====boss====入参：%s", kit.JsonEncode(req))
	glog.Info(logPrefix)
	client := oc.GetOrderServiceClient()
	out, err := client.VC.GetPhysicalVipCardOrderDetail(client.Ctx, req)
	if err != nil {
		glog.Error(logPrefix, "client.VC.GetPhysicalVipCardOrderDetail错误：", err.Error())
		return c.JSON(400, &oc.GetPhysicalVipCardOrderDetailResponse{Code: http.StatusBadRequest, Message: status.Convert(err).Message()})
	}
	return c.JSON(http.StatusOK, out)
}

// @Summary 健康会员卡实体卡订单导出 vip-3.1
// @Tags  付费会员卡实体卡
// @Param model query oc.GetPhysicalVipCardOrderListRequest true " "
// @Success 200 {object} oc.GetPhysicalVipCardOrderExportResponse
// @Failure 400 {object} oc.GetPhysicalVipCardOrderExportResponse
// @Router /boss/vip/physical/order/export [get]
func GetPhysicalVipCardOrderExport(c echo.Context) error {
	req := &oc.GetPhysicalVipCardOrderListRequest{
		PageIndex:        cast.ToInt32(c.QueryParam("page_index")),
		PageSize:         cast.ToInt32(c.QueryParam("page_size")),
		OrderSn:          c.QueryParam("order_sn"),
		MemberMobile:     c.QueryParam("member_mobile"),
		PaymentTimeStart: c.QueryParam("payment_time_start"),
		PaymentTimeEnd:   c.QueryParam("payment_time_end"),
		OrderState:       cast.ToInt32(c.QueryParam("order_state")),
	}
	out := oc.GetPhysicalVipCardOrderExportResponse{}
	logPrefix := fmt.Sprintf("健康会员卡实体卡订单导出GetPhysicalVipCardOrderExport====入参：%s", kit.JsonEncode(req))
	glog.Info(logPrefix)
	client := oc.GetOrderServiceClient()

	ctx := utils.AppendToOutgoingContextLoginUserInfo(client.Ctx, c)

	if _, err := client.VC.GetPhysicalVipCardOrderExport(ctx, req); err != nil {
		glog.Error(logPrefix, "请求client.VC.GetPhysicalVipCardOrderExport错误：", status.Convert(err).Message())
		out.Message = "订单导出失败" + err.Error()
		return c.JSON(400, out)
	}

	return c.JSON(200, out)
}

// @Summary  健康会员卡实体卡订单导出结果列表 vip-3.1
// @Tags 付费会员卡实体卡
// @Param model query oc.PhysicalVipCardOrderExportListRequest true " "
// @Success 200 {object} oc.PhysicalVipCardOrderExportListResponse
// @Failure 400 {object} oc.PhysicalVipCardOrderExportListResponse
// @Router /boss/vip/physical/order/export/list [get]
func PhysicalVipCardOrderExportList(c echo.Context) error {
	req := &oc.PhysicalVipCardOrderExportListRequest{
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
	}

	client := oc.GetOrderServiceClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(client.Ctx, c)

	if out, err := client.VC.PhysicalVipCardOrderExportList(ctx, req); err != nil {
		return c.JSON(400, &oc.PhysicalVipCardOrderExportListResponse{Code: 400, Message: status.Convert(err).Message()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 健康会员卡实体卡订单 发货单号导入模板（文件） vip-3.1
// @Tags 付费会员卡实体卡
// @Accept json
// @Produce octet-stream
// @Failure 400 {object} oc.PVCExpressImportTemplateResponse
// @Router /boss/vip/physical/order-express/import-template [get]
func PVCExpressImportTemplate(c echo.Context) error {
	client := oc.GetOrderServiceClient()
	if out, err := client.VC.PVCExpressImportTemplate(client.Ctx, &oc.PVCExpressImportTemplateRequest{}); err != nil {
		glog.Error("PVCExpressImportTemplate=====client.VC.PVCExpressImportTemplate错误11111：", err.Error())
		return c.JSON(400, &oc.PVCExpressImportTemplateResponse{Code: 400, Message: status.Convert(err).Message()})
	} else {

		c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename=导入模板.xlsx")
		//return c.Blob(200, echo.MIMEOctetStream, out.Template)
		return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(out.Template))
	}
}

// @Summary 健康会员卡实体卡订单 发货单号导入 vip-3.1
// @Tags 付费会员卡实体卡
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "上传的文件 必须是.xlsx文件"
// @Success 200 {object} oc.PVCExpressImportResponse
// @Failure 400 {object} oc.PVCExpressImportResponse
// @Router /boss/vip/physical/order-express/import [POST]
func PVCExpressImport(c echo.Context) error {
	req := new(oc.PVCExpressImportRequest)
	file, _, err := c.Request().FormFile("file")
	if err != nil {
		return c.JSON(400, &oc.PVCExpressImportResponse{Code: 400, Message: "获取文件出错 " + err.Error()})
	}
	defer file.Close()

	buf := bytes.NewBuffer(nil)
	if _, err := io.Copy(buf, file); err != nil {
		return c.JSON(400, &oc.PVCExpressImportResponse{Code: 400, Message: "复制文件出错 " + err.Error()})
	}
	req.File = buf.Bytes()

	client := oc.GetOrderServiceClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)

	if out, err := client.VC.PVCExpressImport(ctx, req); err != nil {

		return c.JSON(400, &oc.PVCExpressImportResponse{Code: 400, Message: status.Convert(err).Message()})
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 健康会员卡实体卡订单 发货、物流编辑
// @Tags 付费会员卡实体卡
// @Accept json
// @Produce json
// @Param model body oc.PVCExpressEditRequest true " "
// @Success 200 {object} oc.PVCExpressEditResponse
// @Failure 400 {object} oc.PVCExpressEditResponse
// @Router /boss/vip/physical/order/express [POST]
func PVCExpressEdit(c echo.Context) error {
	req := new(oc.PVCExpressEditRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.PVCExpressEditResponse{Code: 400, Message: err.Error()})
	}
	userNo := c.Request().Header.Get("userNo")
	userRealName := c.Request().Header.Get("userRealName")
	logPrefix := fmt.Sprintf("PVCExpressEdit====操作人：%s,%s，入参：%s", userNo, userRealName, kit.JsonEncode(req))
	glog.Info(logPrefix)
	client := oc.GetOrderServiceClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	if out, err := client.VC.PVCExpressEdit(ctx, req); err != nil {
		glog.Error(logPrefix, "client.VC.PVCExpressEdit失败：", err.Error())
		return c.JSON(400, &oc.PVCExpressEditResponse{Code: 400, Message: status.Convert(err).Message()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 健康会员卡实体卡订单 会员卡号编辑
// @Tags 付费会员卡实体卡
// @Accept json
// @Produce json
// @Param model body oc.PVCEditRequest true " "
// @Success 200 {object} oc.PVCEditResponse
// @Failure 400 {object} oc.PVCEditResponse
// @Router /boss/vip/physical/order/vcard/edit [POST]
func PVCEdit(c echo.Context) error {
	req := new(oc.PVCEditRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.PVCEditResponse{Code: 400, Message: err.Error()})
	}
	userNo := c.Request().Header.Get("userNo")
	userRealName := c.Request().Header.Get("userRealName")
	logPrefix := fmt.Sprintf("PVCEdit====操作人：%s,%s，入参：%s", userNo, userRealName, kit.JsonEncode(req))
	glog.Info(logPrefix)
	client := oc.GetOrderServiceClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
	if out, err := client.VC.PVCEdit(ctx, req); err != nil {
		glog.Info(logPrefix, "client.VC.PVCEdit失败：", err.Error())
		return c.JSON(400, &oc.PVCEditResponse{Code: 400, Message: status.Convert(err).Message()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 健康会员卡实体卡订单  批量发货导入历史 vip-3.1
// @Tags 付费会员卡实体卡
// @Accept json
// @Produce json
// @Param model query oc.PVCExpressImportListRequest true " "
// @Success 200 {object} oc.PVCExpressImportListResponse
// @Failure 400 {object} oc.PVCExpressImportListResponse
// @Router /boss/vip/physical/order-express/import/list [GET]
func PVCExpressImportList(c echo.Context) error {
	req := &oc.PVCExpressImportListRequest{
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
	}

	client := oc.GetOrderServiceClient()
	ctx := utils.AppendToOutgoingContextLoginUserInfo(client.Ctx, c)

	if out, err := client.VC.PVCExpressImportList(ctx, req); err != nil {
		return c.JSON(400, &oc.PVCExpressImportListResponse{Code: 400, Message: status.Convert(err).Message()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
