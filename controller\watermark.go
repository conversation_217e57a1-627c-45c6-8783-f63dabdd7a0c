package controller

import (
	"_/params"
	"_/proto/ac"
	"bytes"
	"crypto/md5"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/tricobbler/rp-kit/cast"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

// @Summary 获取水印列表
// @Tags 水印活动
// @Param WatermarkList body ac.WatermarkListReq true " "
// @Success 200 {object} ac.WatermarkListRes
// @Router /boss/watermark/list [get]
func WatermarkList(c echo.Context) error {
	client := ac.GetActivityCenterClient()

	var model ac.WatermarkListReq
	model.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	model.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	model.Name = c.Query<PERSON>aram("name")
	model.Status = cast.ToInt32(c.Query<PERSON>aram("status"))

	if model.PageSize <= 0 {
		model.PageSize = 10
	}
	if model.PageIndex <= 0 {
		model.PageIndex = 1
	}
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.OrgId = cast.ToInt32(orgId)
	if out, err := client.RPC.WatermarkList(client.Ctx, &model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 获取水印详情
// @Tags 水印活动
// @Param GetWatermark body ac.GetWatermarkReq true " "
// @Success 200 {object} ac.GetWatermarkRes
// @Router /boss/watermark/detail [get]
func GetWatermark(c echo.Context) error {
	client := ac.GetActivityCenterClient()

	var model ac.GetWatermarkReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	}

	if model.Id <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	if out, err := client.RPC.GetWatermark(client.Ctx, &model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 添加或编辑水印
// @Tags 水印活动
// @Param SaveWatermark body ac.SaveWatermarkReq true " "
// @Success 200 {object} ac.SaveWatermarkRes
// @Router /boss/watermark/save [post]
func SaveWatermark(c echo.Context) error {
	client := ac.GetActivityCenterClient()

	var model ac.SaveWatermarkReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	}

	if model.Name == "" || model.StartTime == 0 || model.EndTime == 0 || model.WatermarkImg == "" {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	if model.StartTime >= model.EndTime {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "活动时间错误"})
	}
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.OrgId = cast.ToInt32(orgId)
	if out, err := client.RPC.SaveWatermark(client.Ctx, &model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 获取水印关联的商品列表
// @Tags 水印活动
// @Param GoodsList body ac.GoodsListReq true " "
// @Success 200 {object} ac.GoodsListRes
// @Router /boss/watermark/goods/list [get]
func WatermarkGoodsList(c echo.Context) error {
	client := ac.GetActivityCenterClient()
	model := ac.GoodsListReq{
		WatermarkName: c.QueryParam("watermark_name"),
		WatermarkId:   cast.ToInt64(c.QueryParam("watermark_id")),
		Sku:           cast.ToInt32(c.QueryParam("sku")),
		GoodsName:     c.QueryParam("goods_name"),
		PageSize:      cast.ToInt32(c.QueryParam("page_size")),
		PageIndex:     cast.ToInt32(c.QueryParam("page_index")),
	}

	if model.WatermarkId <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	if model.PageSize <= 0 {
		model.PageSize = 10
	}
	if model.PageIndex <= 0 {
		model.PageIndex = 1
	}

	if out, err := client.RPC.WatermarkGoodsList(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除水印商品
// @Tags 水印活动
// @Param GoodsDelete body ac.GoodsDeleteReq true " "
// @Success 200 {object} ac.BaseResponse
// @Router /boss/watermark/goods/delete [POST]
func WatermarkGoodsDelete(c echo.Context) error {
	client := ac.GetActivityCenterClient()

	var model ac.GoodsDeleteReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	}

	if model.WatermarkId <= 0 || len(model.Id) <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	if out, err := client.RPC.WatermarkGoodsDelete(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 添加水印商品
// @Tags 水印活动
// @Param GoodsAdd body ac.GoodsAddReq true " "
// @Success 200 {object} ac.BaseResponse
// @Router /boss/watermark/goods/add [POST]
func WatermarkGoodsAdd(c echo.Context) error {
	client := ac.GetActivityCenterClient()

	var model ac.GoodsAddReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	}

	if model.WatermarkId <= 0 || model.GoodsId <= 0 || model.Price <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.OrgId = cast.ToInt32(orgId)

	if out, err := client.RPC.WatermarkGoodsAdd(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 修改水印商品价格
// @Tags 水印活动
// @Param GoodsAdd body ac.GoodsPriceReq true " "
// @Success 200 {object} ac.BaseResponse
// @Router /boss/watermark/goods/price [POST]
func WatermarkGoodsPrice(c echo.Context) error {
	client := ac.GetActivityCenterClient()

	var model ac.GoodsPriceReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	}

	if model.WatermarkId <= 0 || model.Id <= 0 || model.Price <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	if model.Price < 0.01 || model.Price > 9999999 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "活动价格范围0.01~9999999"})
	}

	if out, err := client.RPC.WatermarkGoodsPrice(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 添加水印商品时弹窗中的商品列表
// @Tags 水印活动
// @Param SearchGoods body ac.SearchGoodsReq true " "
// @Success 200 {object} ac.SearchGoodsRes
// @Router /boss/watermark/goods/search [get]
func SearchGoods(c echo.Context) error {
	client := ac.GetActivityCenterClient()

	var model ac.SearchGoodsReq
	model.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	model.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	model.SkuId = cast.ToInt64(c.QueryParam("sku_id"))
	model.SpuId = cast.ToInt64(c.QueryParam("spu_id"))
	model.Name = c.QueryParam("name")
	model.WatermarkId = cast.ToInt64(c.QueryParam("watermark_id"))

	if model.WatermarkId <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	if model.PageSize <= 0 {
		model.PageSize = 10
	}
	if model.PageIndex <= 0 {
		model.PageIndex = 1
	}

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.OrgId = cast.ToInt32(orgId)

	if out, err := client.RPC.SearchGoods(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿里云水印上传
// @Tags 水印活动
// @Param file query string  true "form-data文件"
// @Success 200 {object} params.UploadFileResponse
// @Failure 400 {object} params.UploadFileResponse
// @Router /boss/watermark/upload [post]
func UploadFile(c echo.Context) error {
	var (
		out params.UploadFileResponse
	)
	out.Code = http.StatusBadRequest
	file, err := c.FormFile("file")
	if err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	src, err := file.Open()
	if err != nil {
		out.Message = "打开文件失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	defer src.Close()

	fileByte, err := ioutil.ReadAll(src) //获取上传文件字节流
	if err != nil {
		out.Message = "读取文件失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	url, err := Upload(file.Filename, fileByte)
	if err != nil {
		out.Message = "上传文件失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	out.Code = http.StatusOK
	out.Message = "获取成功"
	out.Data = &params.UploadFileData{
		Url: url,
	}
	return c.JSON(http.StatusOK, out)
}

// 阿里云上传文件
func Upload(fileName string, fileByte []byte) (url string, err error) {

	//oss 的相关配置信息
	bucketName := config.GetString("aliyun.oss.bucketName")
	endpoint := config.GetString("aliyun.oss.endpoint")
	accessKeyId := config.GetString("aliyun.oss.accessKeyId")
	accessKeySecret := config.GetString("aliyun.oss.accessKeySecret")
	domain := config.GetString("aliyun.oss.domain")

	//创建OSSClient实例
	client, err := oss.New(endpoint, accessKeyId, accessKeySecret)
	if err != nil {
		return url, err
	}

	// 获取存储空间
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return url, err
	}

	//上传阿里云路径
	fileNameSlice := strings.Split(fileName, ".")
	ext := fileNameSlice[len(fileNameSlice)-1]
	folderName := time.Now().Format("2006-01-02")
	fileName = fmt.Sprintf("%d", time.Now().Unix()) + fileName
	filenameByte := md5.Sum([]byte(fileName))
	fileName = fmt.Sprintf("%x", filenameByte)
	yunFileTmpPath := "uploads/" + folderName + "/" + fileName + "." + ext //uploads/2020-06-17/c05fa50bd80dd9d9e99edbc90de0227f.png

	// 上传Byte数组
	err = bucket.PutObject(yunFileTmpPath, bytes.NewReader([]byte(fileByte)))
	if err != nil {
		return url, err
	}

	return domain + "/" + yunFileTmpPath, nil
}

// @Summary 终止水印
// @Tags 水印活动
// @Param StopWatermark body ac.StopWatermarkReq true " "
// @Success 200 {object} ac.BaseResponse
// @Router /boss/watermark/stop [get]
func StopWatermark(c echo.Context) error {
	client := ac.GetActivityCenterClient()
	model := &ac.StopWatermarkReq{
		WatermarkId: cast.ToInt32(c.QueryParam("watermark_id")),
	}

	if model.WatermarkId <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	if out, err := client.RPC.StopWatermark(client.Ctx, model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 批量导入商品
// @Tags 水印活动
// @Param GoodsDelete body ac.GoodsImportReq true " "
// @Success 200 {object} ac.GoodsImportRes
// @Router /boss/watermark/goods/import [POST]
func WatermarkGoodsImport(c echo.Context) error {
	client := ac.GetActivityCenterClient()

	var model ac.GoodsImportReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	}

	if model.WatermarkId <= 0 || len(model.FilePath) <= 0 {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: "参数错误"})
	}

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.OrgId = cast.ToInt32(orgId)

	if out, err := client.RPC.WatermarkGoodsImport(client.Ctx, &model); err != nil {
		return c.JSON(400, &ac.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
