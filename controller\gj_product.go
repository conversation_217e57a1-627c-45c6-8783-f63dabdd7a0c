package controller

import (
	"_/dto"
	"_/proto/pc"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"unicode/utf8"

	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// @Summary v6.5.8 编辑查询管家商品库商品
// @Tags 管家商品库
// @Accept plain
// @Produce plain
// @Param id query int true "商品ID"
// @Success 200 {object} dto.GjQueryProductResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/gj-product/edit-query [get]
func EditQueryGjProductOne(c echo.Context) error {
	id, _ := strconv.Atoi(c.QueryParam("id"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var por dto.GjQueryProductResponse

	//查询商品主库信息
	if res, err := client.RPC.QueryGjProductOnly(client.Ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}); err != nil {
		glog.Error(err)
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
	} else {
		por.Product = res.Details[0]
	}

	// 查询商品渠道信息
	if res, err := client.RPC.QueryGjProductChannel(client.Ctx, &pc.IdRequest{Id: fmt.Sprintf("%d", id)}); err != nil {
		glog.Errorf("EditQueryProductOne 查询商品渠道信息异常，err:%+v", err)
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
	} else {
		list := make([]dto.GjProductChannel, 0, len(res.Details))
		for _, item := range res.Details {
			channel := dto.GjProductChannel{
				ChannelId:  item.ChannelId,
				CategoryId: item.CategoryId,
			}
			for _, v := range item.Attr {
				channel.Attr = append(channel.Attr, dto.ProductChannelAttr{
					AttrId:      v.AttrId,
					AttrValueId: v.AttrValueId,
					AttrName:    v.AttrName,
					AttrValue:   v.AttrValue,
				})
			}
			list = append(list, channel)
		}
		por.ChannelList = list
	}

	//查询商品属性
	if res, err := client.RPC.QueryGjProductAttr(client.Ctx, &pc.IdRequest{Id: strconv.Itoa(id)}); err != nil {
		glog.Error(err)
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
	} else {
		por.ProductAttr = res.Details
	}

	//查询商品SKU
	var sku []*pc.Sku
	if res, err := client.RPC.QueryGjSku(client.Ctx, &wrappers.Int32Value{Value: int32(id)}); err != nil {
		glog.Error(err)
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
	} else {
		sku = res.Details
	}

	//查询SKU值
	var skuValue []*pc.SkuValue
	if res, err := client.RPC.QueryGjSkuValue(client.Ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}); err != nil {
		glog.Error(err)
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
	} else {
		skuValue = res.Details
	}
	//查询第三方货号
	var skuThird []*pc.SkuThird
	if res, err := client.RPC.QueryGjSkuThird(client.Ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}); err != nil {
		glog.Error(err)
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
	} else {
		skuThird = res.Details
	}

	//组合商品明细
	var skuGroup []*pc.SkuGroup
	if por.Product.ProductType == 3 {
		if res, err := client.RPC.QuerySkuGroup(client.Ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}); err != nil {
			glog.Error(err)
			return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
		} else {
			skuGroup = res.Details
		}
	}

	//规格、规格值
	spec := make(map[int32]string)
	specValue := make(map[int32]string)
	var specId strings.Builder
	var specValueId strings.Builder
	for i, v := range skuValue {
		specId.WriteString(strconv.Itoa(int(v.SpecId)))
		specValueId.WriteString(strconv.Itoa(int(v.SpecValueId)))
		if i != len(skuValue)-1 {
			specId.WriteString(",")
			specValueId.WriteString(",")
		}
	}

	if res, err := client.RPC.QuerySpecSingle(client.Ctx, &pc.IdRequest{Id: specId.String()}); err != nil {
		glog.Error(err)
	} else {
		for _, v := range res.Details {
			spec[v.Id] = v.Name
		}
	}

	if res, err := client.RPC.QuerySpecValue(client.Ctx, &pc.IdRequest{Id: specValueId.String()}); err != nil {
		glog.Error(err)
	} else {
		for _, v := range res.Details {
			specValue[v.Id] = v.Value
		}
	}

	for _, s := range sku {
		skuInfo := &pc.SkuInfo{
			RetailPrice:   s.RetailPrice,
			SkuId:         s.Id,
			ProductId:     s.ProductId,
			MarketPrice:   s.MarketPrice,
			BarCode:       s.BarCode,
			WeightForUnit: s.WeightForUnit,
			PreposePrice:  s.PreposePrice,
			StorePrice:    s.StorePrice,
		}
		//第三方货号
		for _, t := range skuThird {
			if t.SkuId == s.Id {
				skuInfo.SkuThird = append(skuInfo.SkuThird, t)
			}
		}

		//sku value
		for _, v := range skuValue {
			if v.SkuId == s.Id {
				skuv := *v
				skuv.SpecName = spec[skuv.SpecId]
				skuv.SpecValueValue = specValue[skuv.SpecValueId]
				skuInfo.Skuv = append(skuInfo.Skuv, &skuv)
			}
		}

		//组合商品的组合明细
		if por.Product.ProductType == 3 {
			skuInfo.SkuGroup = skuGroup
		}
		por.SkuInfo = append(por.SkuInfo, skuInfo)
	}
	por.Code = http.StatusOK
	return c.JSON(http.StatusOK, por)
}

// @Summary v6.0 编辑管家商品
// @Tags 管家商品库
// @Accept json
// @Produce plain
// @Param product body dto.GjSaveProductRequest true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/gj-product/edit [post]
func EditGjProduct(c echo.Context) error {
	var product dto.GjSaveProductRequest
	if err := c.Bind(&product); err != nil {
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
	}

	if len(product.SkuInfo) == 0 {
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: "SKU规格信息为空"})
	}
	if len([]rune(product.Product.ShortName)) > 15 {
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: "短标题最多15个汉字"})
	}
	/*if len(product.Product.ShortName) < 1 {
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: "短标题为必填项"})
	}*/

	// 商品卖点限制长度
	product.Product.SellingPoint = strings.TrimSpace(product.Product.SellingPoint)
	if utf8.RuneCountInString(product.Product.SellingPoint) > 10 {
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: "商品卖点最多10个汉字"})
	}

	// 验证渠道信息
	if len(product.ChannelList) == 0 {
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: "渠道信息为空"})
	}
	for _, item := range product.ChannelList {
		if item.CategoryId == 0 {
			return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: fmt.Sprintf("%s分类不能为空", channelNameMap[item.ChannelId])})
		}
	}

	skuSpaces := RemoveThirdSkuSpaces(product.SkuInfo)
	product.SkuInfo = skuSpaces

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.EditGjProduct(client.Ctx, &product.ProductRequest)
	if err != nil {
		return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
	}

	// 如果http状态码不是200则不需要执行后面逻辑
	if out.Code != http.StatusOK {
		return c.JSON(int(out.Code), out)
	}

	// 更新产品渠道信息
	channelList := make([]*pc.ProductChannel, 0, len(product.ChannelList))
	for _, item := range product.ChannelList {
		if item.ChannelId == 0 {
			continue
		}
		channelList = append(channelList, buildGjProductChannel(product.Product.Id, item))
	}

	if len(channelList) > 0 {
		_, err := client.RPC.SaveGjProductChannel(client.Ctx, &pc.SaveGjProductChannelRequest{
			List: channelList,
		})
		if err != nil {
			return c.JSON(http.StatusBadRequest, pc.BaseResponse{Code: http.StatusBadRequest, Message: err.Error()})
		}
	}
	return c.JSON(http.StatusOK, out)
}

func buildGjProductChannel(productId int32, channel dto.GjProductChannel) *pc.ProductChannel {
	resp := &pc.ProductChannel{
		ProductId:  productId,
		ChannelId:  channel.ChannelId,
		CategoryId: channel.CategoryId,
	}
	for _, v := range channel.Attr {
		resp.Attr = append(resp.Attr, &pc.SimpleChannelAttr{
			AttrId:      v.AttrId,
			AttrValueId: v.AttrValueId,
			AttrName:    v.AttrName,
			AttrValue:   v.AttrValue,
		})
	}
	return resp
}
