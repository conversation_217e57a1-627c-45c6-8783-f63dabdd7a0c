// Copyright 2020.03.19 The Go Authors.chenshengfu All rights reserved.
// 登陆流程
// 用户打开系统首页(前端index)，boss后台根据jwt检查是否登陆
//1:未登陆：直接返回跳转uuap登陆验证的url给到前端，前端跳转到uuap登陆页面
//2:进行登陆：uuap通过验证，跳转到前端页面，前端接收uuap返回的jwt
//前端再次发起请求带上jwt告诉后台已验证登陆，后台解析jwt信息，并缓存登录信息到redis，重新组装一个新的jwt给前端
//3:前端每次请求带上新组装的jwt，如果未带上jwt信息或jwt信息已失效，直接跳转到uuap登陆页
//登陆模块接口如下：
//i:前端带jwt告知后台已验证登录；ii:前端通知后台已退出注销；iii:后台解析每个前端请求的jwt判断是否登陆

package controller

import (
	"_/models"
	"_/utils"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	jw "github.com/limitedlee/microservice/common/jwt"
	"github.com/maybgit/glog"
	"github.com/pkg/errors"
)

type LoginApi struct {
}

// @Tags 用户
// @Summary 用户登录
// @Description 前端带jwt告知后台已验证登录，后台解析jwt信息，redis缓存登录信息
// @Param loginticket body models.LoginTicket true " "
// @Success 200 {object} models.LoginResponse
// @Failure 400 {object} models.LoginResponse
// @Router /boss/login [post]
func Login(c echo.Context) error {
	var err error
	var model models.LoginTicket
	if err := c.Bind(&model); err != nil {
		return c.JSON(http.StatusBadRequest, &models.LoginResponse{Code: 400, Message: "未获取到登录票据"})
	}

	//testStr:="eyJhbGciOiJIUzUxMiJ9.ZXlKemRXSWlPaUkwTnpVeElpd2lhWE5HY205dFRtVjNURzluYVc0aU9pSjBjblZsSWl3aVlYVjBhR1Z1ZEdsallYUnBiMjVFWVhSbElqb2lNakF5TUMwd015MHlNVlF4TWpveU1Ub3pOUzQyTVRJck1EZzZNREJiUVhOcFlWd3ZVMmhoYm1kb1lXbGRJaXdpZFhObGNrNXZJam9pVlY5SVJFVk5TRXhGSWl3aWMzVmpZMlZ6YzJaMWJFRjFkR2hsYm5ScFkyRjBhVzl1U0dGdVpHeGxjbk1pT2lKVmMyVnlibUZ0WlZCaGMzTjNiM0prVkhsd1pVRjFkR2hsYm5ScFkyRjBhVzl1U0dGdVpHeGxjaUlzSW1semN5STZJbWgwZEhCek9sd3ZYQzkxZFdGd0xYUmxjM1F1Y25BdFptbGxiR1F1WTI5dFhDOWpZWE1pTENKdGIySnBiR1VpT2lJck9EWXhNelF4T0RnMU5UY3hNaUlzSW5SNWNHVWlPaUp3WVhOemQyUWlMQ0pqY21Wa1pXNTBhV0ZzVkhsd1pTSTZJbFZ6WlhKdVlXMWxVR0Z6YzNkdmNtUlVlWEJsUTNKbFpHVnVkR2xoYkNJc0ltRjFaQ0k2SW1oMGRIQTZYQzljTDJOdmJuTnZiR1V0ZEdWemRDNXljQzFtYVdWc1pDNWpiMjBpTENKeVpXRnNUbUZ0WlNJNkl1Ky92ZSsvdmUrL3ZlKy92ZSsvdmUrL3ZlKy92ZSsvdmUrL3ZTSXNJbUYxZEdobGJuUnBZMkYwYVc5dVRXVjBhRzlrSWpvaVZYTmxjbTVoYldWUVlYTnpkMjl5WkZSNWNHVkJkWFJvWlc1MGFXTmhkR2x2YmtoaGJtUnNaWElpTENKc2IyNW5WR1Z5YlVGMWRHaGxiblJwWTJGMGFXOXVVbVZ4ZFdWemRGUnZhMlZ1VlhObFpDSTZJblJ5ZFdVaUxDSnBaQ0k2SWpRM05URWlMQ0psZUhBaU9qRTFPRFV6TmpreU9UVXNJbWxoZENJNk1UVTRORGMyTkRRNU5Td2lhblJwSWpvaVUxUXRNelEwTkRVdE0wMXJTMmt3YkZwRFpVOXFNRkJRVHpoNVdFZGhaRUZDZUVwakxXRjJZWFJoY25WMVlYQXROamsxWmpSaU56ZzRMV3AwTkdKM0lpd2laVzFoYVd3aU9pSTBNVEE1TXpRNU1USkFjWEV1WTI5dElpd2lkWE5sY201aGJXVWlPaUpqYUdWdWMyWWlmUT09.8tXu8-urUqSxT7yqxNH6slqvgZl9w1zBTMHEmVQ08vqDqXI2ledO1rALrhX3lW2SfUfHvnY3hZwur8GyVbXHOQ"
	//1:解析前端提供的uuap的jwt
	//info := models.LoginInfo{}
	logstr := strings.Builder{}
	logstr.WriteString(fmt.Sprintf("登陆ticket:%s", model.Ticket))

	//验证北京的jwt

	//以下4行临时屏蔽
	// _, err = utils.ValidateToken(model.Ticket, utils.BjPublicKey)
	// if err != nil {
	// 	return c.String(http.StatusBadRequest, fmt.Sprintf("bj valid token required.%v", err))
	// }

	strlist := strings.Split(model.Ticket, ".") //三段，中间那段

	//val, err := utils.Base64Decrypt(strlist[1])
	/*	if err != nil {
		return c.JSON(http.StatusBadRequest, &models.LoginResponse{Code: 400, Message: "首次解密登录票据失败"})
	}*/
	//jsonData, err := base64.StdEncoding.DecodeString(val)
	//if err != nil {
	//	return c.JSON(http.StatusBadRequest, &models.LoginResponse{Code: 400, Message: "二次解密登录票据失败"})
	//}
	//err = json.Unmarshal(jsonData, &info)

	info := models.LoginUserInfo{}
	val, _ := utils.Base64RawDecrypt(strlist[1])
	json.Unmarshal([]byte(val), &info)
	if len(info.Mobile) > 0 {
		//处理手机号
		info.BossMobile = strings.Replace(info.Mobile, "+86", "", 1)
	}

	//2:重新组装jwt用户身份验证
	jwtString, err := utils.CreateJwtToken(info.BossMobile, info.UserNo, info.UserName)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, &models.LoginResponse{Code: 500, Message: "生成新的jwt失败"})
	}
	//3:缓存登录信息到token存储redis
	client := GetRedisConn()
	err = client.Do("SET", fmt.Sprintf("Boss:token:%s", info.UserNo), jwtString, "EX", 2*86400).Err()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, &models.LoginResponse{Code: 500, Message: "token缓存redis失败"})
	}

	//4:将权限数据存储到redis
	systemCode := config.GetString("BJAuth.SystemCode")
	authStr, autherr := GetUserAuthCollection(systemCode, info.UserNo, c.Request().Header.Get("structOuterCode"))
	var baseRes = models.UserAuthResponse{}

	err = json.Unmarshal([]byte(authStr), &baseRes)

	if autherr != nil || err != nil {
		return c.JSON(http.StatusInternalServerError, models.LoginResponse{Code: 500, Message: "获取用户权限失败"})
	}
	err = client.Do("SET", fmt.Sprintf("Boss:auth:%s", info.UserNo), authStr, "EX", 2*86400).Err()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.LoginResponse{Code: 500, Message: "用户权限缓存redis失败"})
	}

	//测试跳转
	//return c.Redirect(http.StatusMovedPermanently, "www.baidu.com")

	authjson, _ := json.Marshal(baseRes.AuthData.Operation)
	authString := string(authjson)

	return c.JSON(http.StatusOK, &models.LoginResponse{Code: 200, Token: jwtString, Auths: authString, Message: ""})

}

//授权中间件(验证是否登陆)
func AuthFilter(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		//路由拦截 - 登录身份、资源权限判断等

		//glog.Info(fmt.Sprintf("Api路由拦截path:%s;请求来源ip:%s;配置白名单:%s;真实IP:%s", c.Path(), c.RealIP(), config.GetString("WhiteIPs")), c.Request().RemoteAddr)

		//1：过滤不需要授权的请求
		noAuthPaths := []string{
			"/boss/login",
			"/boss/test",
			"/boss/product/product/list",
			"/boss/product/product/sku",
			"/boss/product/product/product-sku",
			"/boss/product/product/getRelationList",
			"/boss/product/product/addGoodsRelation",
			"/boss/product/product/deleteGoodsRelation",
			"/boss/product/product/deleteGoodsRelationSku",
			"/boss/tags/product",
			"/boss/tags/pettips",
			"/boss/tags/select-values",
			"/boss/product/bbc/change-notify",
			"/boss/Intelligence/warehouseData",
			"/boss/market/activity/voucher/add",
			"/boss/market/activity/coupon/list",
			"/boss/scrm/export_customer_list",
			"/boss/crm/synchronize_to_wechat",
			"/boss/petmillions/marchent/download",
			"/boss/ordercenter/order/OrderRefundReject",
			"/boss/ordercenter/order/OrderRefundAgree",
			"/boss/product/add-product/oms",
			"/boss/datacenter/near-shops",
			"/boss/user/member/level",
			"/boss/Promotion/operate-list",
			"/boss/product/diagnose-dic",
		}

		for _, v := range noAuthPaths {
			if strings.Contains(c.Path(), v) {
				return next(c)
			}
		}

		//if(strings.Contains(c.Path(),"/boss/login")||strings.Contains(c.Path(),"/boss/test")){
		//	return  next(c)
		//}

		//2:验证是否登陆,比对redistoken和前端过来的token是否过期
		jwtToken := c.Request().Header.Get("Authorization")

		if len(jwtToken) <= 0 {
			return c.String(http.StatusUnauthorized, "valid token required.")
		}

		index := strings.Index(jwtToken, " ")
		count := strings.Count(jwtToken, "")
		token := jwtToken[index+1 : count-1]

		_, err := utils.ValidateToken(token, jw.PublicKey)
		if err != nil {
			return c.String(http.StatusUnauthorized, fmt.Sprintf("valid token required.%v", err))
		}

		//判断token 是否失效
		Redis := GetRedisConn()
		claims, err := utils.ParseAndGetPayload(token)
		//获取redis进行匹配
		RedisToken, err := Redis.Get(fmt.Sprintf("Boss:token:%s", claims["userno"])).Result()
		if err != nil { //redis 获取失败
			glog.Error("AuthFilter read redis err", err)
			return c.String(http.StatusUnauthorized, "redis获取token失败,token失效")
		}
		if RedisToken != token { //redis 不一致
			return c.String(http.StatusUnauthorized, "token失效")
		}

		//3：验证是否有权限
		//AuthStr, err := Redis.Get(fmt.Sprintf("Boss:auth:%s", claims["mobile"])).Result()
		//判断是否权限集

		return next(c)
	}
}

//调用北京获取用户权限接口，将结果权限集缓存redis
func GetUserAuthCollection(systemcode, memberid, structOuterCode string) (string, error) {
	url := fmt.Sprintf("%s%s", config.GetString("bj-user-auth-url"), "/api/out/priv/list")

	dataParam := createCommonBjAcpParam()
	dataParam["systemCode"] = systemcode
	dataParam["userCode"] = memberid
	dataParam["structOuterCode"] = structOuterCode

	//2：调用北京获取权限接口
	code, data := utils.HttpPostForm(url, "", "", dataParam)

	_, err := json.Marshal(dataParam)
	if err != nil {
		return "MapToJson转换出错", err
	}
	if code != 200 {
		return "", errors.New("获取权限集失败")
	}

	return data, nil
}

//北京acp接口公用参数
func createCommonBjAcpParam() map[string]interface{} {
	dataArr := make(map[string]interface{})

	apiid := config.GetString("BJAuth.AppId")
	apiSecret := config.GetString("BJAuth.ApiSecret")
	apiStr := utils.GenSonyflake() //自己生成，唯一的十六位随机字符串
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("apiSecret=%s&apiStr=%s&apiId=%s&timestamp=%s&apiSecret=%s", apiSecret, apiStr, apiid, timestamp, apiSecret)
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))

	dataArr["apiId"] = apiid
	dataArr["apiStr"] = apiStr
	dataArr["timestamp"] = timestamp
	dataArr["sign"] = md5sign

	return dataArr
}
