package controller

import (
	"_/dto"
	"_/proto/base"
	"_/proto/cc"
	"_/utils"
	"encoding/json"
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/tricobbler/echo-tool/validate"
	"net/http"
)

// @Summary 获取用户反馈详情
// @Tags 用户反馈
// @Accept plain
// @Accept json
// @Produce json
// @Param id query int true "反馈记录id"
// @Success 200 {object} dto.GetFeedbackResponse
// @Failure 400 {object} dto.GetFeedbackResponse
// @Router /boss/feedback/detail [GET]
func GetFeedback(c echo.Context) error {
	out := &dto.GetFeedbackResponse{}
	model := new(dto.GetFeedbackRequest)
	if err := c.Bind(model); err != nil {
		out.Message = "参数错误"
		return c.JSON(http.StatusBadRequest, out)
	}
	if model.Id == 0 {
		out.Message = "参数缺失"
		return c.JSON(200, out)
	}
	rpcRequestParam := new(cc.GetFeedbackRequest)
	rpcRequestParam.Id = model.Id

	client := cc.GetCustomerCenterClient()
	defer client.Close()

	rpcRes, err := client.Feedback.GetFeedback(client.Ctx, rpcRequestParam)
	if err != nil || rpcRes.Code != http.StatusOK {
		if err != nil {
			out.Message = "请求失败，请稍后再试" + err.Error()
		} else {
			out.Message = "请求失败，请稍后再试" + rpcRes.Message
		}
	}
	if rpcRes != nil && rpcRes.Data != nil {
		out.Data = new(dto.Feedback)
		if err = utils.MapTo(rpcRes.Data, out.Data); err != nil {
			glog.Error("GetFeedback MapTo fail:", err.Error())
			out.Message = "获取失败,数据转换出错"
		}
		if rpcRes.Data.ClientInfo != "" {
			clientInfo := new(base.ClientInfo)
			err = json.Unmarshal([]byte(rpcRes.Data.ClientInfo), clientInfo)
			if err != nil {
				glog.Error("GetFeedback Unmarshal ClientInfo fail:", err.Error())
			}
			out.Data.ClientInfo = clientInfo
		}
	}
	out.Message = "获取成功"
	return c.JSON(http.StatusOK, out)
}

// @Summary 获取用户反馈列表
// @Tags 用户反馈
// @Accept plain
// @Accept json
// @Produce json
// @Param pageIndex query int false "当前多少页 从1开始 不传默认为1"
// @Param pageSize query int false "每页多少条数据 不传默认为15"
// @Param type query int false "反馈类型 0：建议  1咨询 2投诉 不传则默认为0"
// @Param phone query string false "用户电话"
// @Param content query string false "反馈内容"
// @Param createTime query string false "反馈创建时间区间"
// @Success 200 {object} dto.GetFeedbackListResponse
// @Failure 400 {object} dto.GetFeedbackListResponse
// @Router /boss/feedback/list [GET]
func GetFeedbackList(c echo.Context) error {
	out := &dto.GetFeedbackListResponse{}
	model := new(dto.GetFeedbackListRequest)
	if err := c.Bind(model); err != nil {
		out.Message = "参数错误"
		return c.JSON(200, out)
	}
	rpcRequestParam := new(cc.GetFeedbackListRequest)
	if err := utils.MapTo(model, rpcRequestParam); err != nil {
		glog.Error("GetFeedbackList MapTo fail:", err.Error())
		out.Message = "获取失败，参数数据转换出错：" + err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	//默认查询最近三个月的数据
	client := cc.GetCustomerCenterClient()
	defer client.Close()
	rpcRes, err := client.Feedback.GetFeedbackList(client.Ctx, rpcRequestParam)
	if err != nil || rpcRes.Code != http.StatusOK {
		if err != nil {
			out.Message = "请求失败，请稍后再试" + err.Error()
		} else {
			out.Message = "请求失败，请稍后再试" + rpcRes.Message
		}
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes != nil && len(rpcRes.Data) > 0 {
		out.Total = rpcRes.Total
		out.Data = make([]*dto.Feedback, len(rpcRes.Data))
		for i, v := range rpcRes.Data {
			item := new(dto.Feedback)
			if err = utils.MapTo(v, item); err != nil {
				glog.Error("GetFeedbackList MapTo fail:", err.Error())
				out.Message = "获取失败，数据转换出错" + err.Error()
				return c.JSON(http.StatusBadRequest, out)
			}
			if v.ClientInfo != "" {
				clientInfo := new(base.ClientInfo)
				err = json.Unmarshal([]byte(v.ClientInfo), clientInfo)
				if err != nil {
					glog.Error("GetFeedbackList Unmarshal ClientInfo fail:", err.Error())
				}
				item.ClientInfo = clientInfo
			}
			out.Data[i] = item
		}
	}
	out.Message = "获取成功"
	return c.JSON(http.StatusOK, out)
}

// @Summary 添加用户反馈回复
// @Tags 用户反馈
// @Produce json
// @Param param body dto.AddFeedbackCommentRequest true " "
// @Success 200 {object} dto.BaseResponse
// @Failure 400 {object} dto.BaseResponse
// @Router /boss/feedback/comment-add [POST]
func AddFeedbackComment(c echo.Context) error {
	out := &dto.BaseResponse{}
	model := new(dto.AddFeedbackCommentRequest)
	if err := c.Bind(model); err != nil {
		out.Message = "参数错误"
		return c.JSON(200, out)
	}
	if err := c.Validate(model); err != nil {
		errValidate := validate.Translate(err.(validator.ValidationErrors))
		out.Message = errValidate.One()
		return c.JSON(http.StatusBadRequest, out)
	}
	rpcRequestParam := new(cc.AddFeedbackCommentRequest)
	if err := utils.MapTo(model, rpcRequestParam); err != nil {
		glog.Error("AddFeedbackComment MapTo fail:", err.Error())
		out.Message = "回复失败，请稍后再试,参数转换出错" + err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	rpcRequestParam.Content = model.Content
	rpcRequestParam.FeedbackId = model.FeedbackId
	rpcRequestParam.IsOfficial = 1
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	rpcRes, err := client.Feedback.AddFeedbackComment(client.Ctx, rpcRequestParam)
	if err != nil || rpcRes.Code != http.StatusOK {
		if err != nil {
			out.Message = "请求失败，请稍后再试" + err.Error()
		} else {
			out.Message = "请求失败，请稍后再试" + rpcRes.Message
		}
		return c.JSON(http.StatusBadRequest, out)
	}
	//todo 发送用户通知
	out.Message = "回复成功"
	return c.JSON(http.StatusOK, out)
}

// @Summary 用户反馈回复列表
// @Tags 用户反馈
// @Accept plain
// @Accept json
// @Produce json
//@Param pageIndex query int false "当前多少页 从1开始 不传默认为1"
// @Param pageSize query int false "每页多少条数据 不传默认为15"
// @Success 200 {object} cc.GetFeedbackCommentListResponse
// @Failure 400 {object} cc.GetFeedbackCommentListResponse
// @Router /boss/feedback/comment-list [GET]
func GetFeedbackCommentList(c echo.Context) error {
	out := &dto.GetFeedbackCommentListResponse{}
	model := new(dto.GetFeedbackCommentListRequest)
	if err := c.Bind(model); err != nil {
		out.Message = "参数错误"
		return c.JSON(200, out)
	}

	rpcRequestParam := new(cc.GetFeedbackCommentListRequest)
	rpcRequestParam.PageIndex = model.PageIndex
	rpcRequestParam.PageSize = model.PageSize
	rpcRequestParam.FeedbackId = model.FeedbackId

	//默认查询最近三个月的数据
	client := cc.GetCustomerCenterClient()
	defer client.Close()
	rpcRes, err := client.Feedback.GetFeedbackCommentList(client.Ctx, rpcRequestParam)
	if err != nil || rpcRes.Code != http.StatusOK {
		if err != nil {
			out.Message = "请求失败，请稍后再试" + err.Error()
		} else {
			out.Message = "请求失败，请稍后再试" + rpcRes.Message
		}
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes != nil && len(rpcRes.Data) > 0 {
		out.Total = rpcRes.Total
		out.Data = make([]*dto.FeedbackComment, len(rpcRes.Data))
		for i, v := range rpcRes.Data {
			item := new(dto.FeedbackComment)
			item.CreateTime = v.CreateTime
			item.Content = v.Content
			item.UserName = v.UserName
			//没有user_id 即为官方回复
			if v.IsOfficial == 1 {
				item.UserName = "阿闻"
			}
			out.Data[i] = item
		}
	}
	out.Message = "获取成功"
	return c.JSON(http.StatusOK, out)
}
