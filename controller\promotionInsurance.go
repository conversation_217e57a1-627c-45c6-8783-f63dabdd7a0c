package controller

import (
	"_/proto/mk"
	"_/utils"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @Summary 选择商品
// @Tags 赠险
// @Accept json
// @Produce json
// @Param model query mk.InsuranceChoiceSkuReq true " "
// @Success 200 {object} mk.InsuranceChoiceSkuRes
// @Failure 400 {object} mk.InsuranceChoiceSkuRes
// @Router /boss/Promotion/Insurance/ChoiceSku [GET]
func InsuranceChoiceSku(c echo.Context) error {
	req := &mk.InsuranceChoiceSkuReq{
		PageIndex:   cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:    cast.ToInt32(c.QueryParam("pageSize")),
		BeginDate:   c.Query<PERSON>aram("beginDate"),
		EndDate:     c.QueryParam("endDate"),
		PromotionId: cast.ToInt32(c.Query<PERSON>aram("promotionId")),
		SkuId:       cast.ToInt32(c.QueryParam("skuId")),
		ProductId:   cast.ToInt32(c.QueryParam("productId")),
		Name:        c.QueryParam("name"),
	}

	client := mk.GetMarketingCenterClient()
	defer client.Close()

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	if out, err := client.Insurance.ChoiceSku(client.Ctx, req); err != nil {
		return c.JSON(400, &mk.InsuranceChoiceSkuRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动列表
// @Tags 赠险
// @Accept json
// @Produce json
// @Param model query mk.InsuranceListReq true " "
// @Success 200 {object} mk.InsuranceListRes
// @Failure 400 {object} mk.InsuranceListRes
// @Router /boss/Promotion/Insurance/List [GET]
func InsuranceList(c echo.Context) error {
	req := &mk.InsuranceListReq{
		PageIndex: cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:  cast.ToInt32(c.QueryParam("pageSize")),
	}

	client := mk.GetMarketingCenterClient()
	defer client.Close()

	if out, err := client.Insurance.List(client.Ctx, req); err != nil {
		return c.JSON(400, &mk.InsuranceListRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动详情
// @Tags 赠险
// @Accept json
// @Produce json
// @Param model query mk.InsuranceDetailReq true " "
// @Success 200 {object} mk.InsuranceDetailRes
// @Failure 400 {object} mk.InsuranceDetailRes
// @Router /boss/Promotion/Insurance/Detail [GET]
func InsuranceDetail(c echo.Context) error {
	req := &mk.InsuranceDetailReq{
		Id: cast.ToInt32(c.QueryParam("id")),
	}

	client := mk.GetMarketingCenterClient()
	defer client.Close()

	if out, err := client.Insurance.Detail(client.Ctx, req); err != nil {
		return c.JSON(400, &mk.InsuranceDetailRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动 新增/编辑
// @Tags 赠险
// @Accept json
// @Produce json
// @Param model body mk.InsuranceReq true " "
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/Insurance/Store [POST]
func InsuranceStore(c echo.Context) error {
	req := new(mk.InsuranceReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketingCenterClient()
	defer client.Close()

	if user, err := utils.GetPayloadDirectlyToInterface(c); err == nil {
		req.UserId = user.UserNo
		req.UserName = user.UserName
	}
	if out, err := client.Insurance.Store(client.Ctx, req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动 撤销
// @Tags 赠险
// @Accept json
// @Produce json
// @Param model body mk.InsuranceCancelReq true " "
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/Insurance/Cancel [POST]
func InsuranceCancel(c echo.Context) error {
	req := new(mk.InsuranceCancelReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketingCenterClient()
	defer client.Close()

	if user, err := utils.GetPayloadDirectlyToInterface(c); err == nil {
		req.UserId = user.UserNo
		req.UserName = user.UserName
	}
	if out, err := client.Insurance.Cancel(client.Ctx, req); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}

}
