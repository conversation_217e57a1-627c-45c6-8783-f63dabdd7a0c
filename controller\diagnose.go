package controller

import (
	"_/models"
	"_/params"
	"_/proto/dgc"
	"_/proto/pm"
	"_/utils"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/tealeg/xlsx"
	"github.com/tricobbler/echo-tool/validate"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
	"google.golang.org/grpc/status"
	"net/http"
	"net/url"
	"strconv"
	"strings"
)

var AcpUserUrl = "/api/out/mobile/userInfo"
var DoctorStatusSetting = map[int32]string{
	0: "正常",
	1: "禁用",
}

// @Summary 问诊列表
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.DiagnoseListRequest true " "
// @Success 200 {object} params.DiagnoseListResponse
// @Failure 400 {object} params.DiagnoseListResponse
// @Router /boss/diagnose/list [POST]
func DiagnoseList(c echo.Context) error {
	var (
		p      params.DiagnoseListRequest
		rpcRqt dgc.DiagnoseListRequest
		rpcRps *dgc.DiagnoseListResponse
		err    error
		rps    params.DiagnoseListResponse
	)
	rps.Data = make([]*params.DiagnoseInfo, 0)

	if err = c.Bind(&p); err != nil {
		glog.Error("boss-DiagnoseList,c.Bind-错误=", kit.JsonEncode(err))
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}
	logStr := "boss-DiagnoseList,入参=" + kit.JsonEncode(p)
	glog.Info(logStr)
	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",c.Validate-错误=", kit.JsonEncode(validateErrList.All()))
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		glog.Error(logStr, "utils.MapTo-错误=", kit.JsonEncode(err))
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	glog.Info(logStr, ",rpcRqt-数据记录=", kit.JsonEncode(rpcRqt))

	client := dgc.GetDiagnoseServiceClient()
	if rpcRps, err = client.DCT.DiagnoseList(client.Ctx, &rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, ",grpc-错误=", kit.JsonEncode(s.Message()))
			rps.Msg = s.Message()
			return c.JSON(400, rps)
		}

	}

	glog.Info(logStr, " 数据记录-rpcRps=", kit.JsonEncode(rpcRps))
	if rpcRps == nil {
		return c.JSON(200, rps)
	}

	rps.Total = rpcRps.Total
	if len(rpcRps.Data) > 0 {
		if err = utils.MapTo(rpcRps.Data, &rps.Data); err != nil {
			glog.Error(logStr, "utils.MapTo-错误=", kit.JsonEncode(err))
			return c.JSON(400, err.Error())
		}
	}
	glog.Info(logStr, "，函数返回=", kit.JsonEncode(rps))
	return c.JSON(200, rps)
}

// @Summary 问诊列表
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.DiagnoseListRequest true " "
// @Success 200 {object} params.DiagnoseListResponse
// @Failure 400 {object} params.DiagnoseListResponse
// @Router /boss/diagnose/export [POST]
func ExportDiagnoseList(c echo.Context) error {
	var (
		p      params.DiagnoseListRequest
		rpcRqt dgc.DiagnoseListRequest
		rpcRps *dgc.DiagnoseListResponse
		err    error
		rps    params.DiagnoseListResponse
	)
	rps.Data = make([]*params.DiagnoseInfo, 0)

	if err = c.Bind(&p); err != nil {
		glog.Error("boss-DiagnoseList,c.Bind-错误=", kit.JsonEncode(err))
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}
	logStr := "boss-DiagnoseList,入参=" + kit.JsonEncode(p)
	glog.Info(logStr)
	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",c.Validate-错误=", kit.JsonEncode(validateErrList.All()))
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		glog.Error(logStr, "utils.MapTo-错误=", kit.JsonEncode(err))
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	glog.Info(logStr, ",rpcRqt-数据记录=", kit.JsonEncode(rpcRqt))
	rpcRqt.PageSize = 0
	rpcRqt.PageIndex = 0
	client := dgc.GetDiagnoseServiceClient()
	if rpcRps, err = client.DCT.DiagnoseList(client.Ctx, &rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, ",grpc-错误=", kit.JsonEncode(s.Message()))
			rps.Msg = s.Message()
			return c.JSON(400, rps)
		}
	}
	if len(rpcRps.Data) == 0 {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: "无可下载的内容"})
	}
	exportDiagnoseInfos := []models.ExportDiagnoseInfo{}
	utils.MapTo(rpcRps.Data, exportDiagnoseInfos)

	// 生成一个新的文件
	file := xlsx.NewFile()
	// 添加sheet页
	sheet, _ := file.AddSheet("Sheet1")
	// 插入表头
	titleRow := sheet.AddRow()
	titleList := []string{"订单ID", "问诊时间", "医生姓名", "医生手机号", "医生职级", "医生所在医院", "客户姓名", "客户手机号", "问诊项目", "问诊形式", "回复间隔", "订单状态"}
	for _, v := range titleList {
		cell := titleRow.AddCell()
		cell.Value = v
		//表头字体颜色
		cell.GetStyle().Font.Color = "00000000"
		cell.GetStyle().Font.Bold = true
		//居中显示
		cell.GetStyle().Alignment.Horizontal = "center"
		cell.GetStyle().Alignment.Vertical = "center"
	}
	// 插入内容
	for _, v := range exportDiagnoseInfos {
		row := sheet.AddRow()
		row.WriteStruct(&v, -1)
	}

	//将数据存入buff中
	var buff bytes.Buffer
	if err := file.Write(&buff); err != nil {
		panic(err)
	}
	//设置请求头  使用浏览器下载
	c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("问诊列表")))
	return c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
}

// @Summary 问诊详情
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.DiagnoseDetailsRequest true " "
// @Success 200 {object} params.DiagnoseDetailsResponse
// @Failure 400 {object} params.DiagnoseDetailsResponse
// @Router /boss/diagnose/details [GET]
func DiagnoseDetails(c echo.Context) error {
	//声明变量
	var (
		p           = new(params.DiagnoseDetailsRequest)
		out         = new(params.DiagnoseDetailsResponse)
		rpcRequest  = new(dgc.DiagnoseDetailsRequest)
		rpcResponse = new(dgc.DiagnoseDetailsResponse)
		err         error
	)

	//获得数据
	p.Id = cast.ToInt32(c.QueryParam("id"))
	if p.Id <= 0 {
		out.Msg = "参数错误"
		return c.JSON(400, out)
	}

	//组装rpc请求数据
	rpcRequest.Id = p.Id
	//调用rpc服务
	client := dgc.GetDiagnoseServiceClient()
	if rpcResponse, err = client.DCT.DiagnoseDetails(client.Ctx, rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}
	if err := utils.MapTo(rpcResponse.DiagnoseDetails, &out.Data); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	return c.JSON(200, out)
}

// @Summary 医生接单管理
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.ScrmDoctorListRequest true " "
// @Success 200 {object} params.ScrmDoctorListResponse
// @Failure 400 {object} params.ScrmDoctorListResponse
// @Router /boss/diagnose/doctor/list [POST]
func ScrmDoctorList(c echo.Context) error {
	var (
		p      params.ScrmDoctorListRequest
		rpcRqt dgc.ScrmDoctorListRequest
		rpcRps *dgc.ScrmDoctorListResponse
		rps    params.ScrmDoctorListResponse
		err    error
	)
	rps.Data = make([]params.ScrmDoctorSettingInfo, 0)
	if err = c.Bind(&p); err != nil {
		glog.Error("boss-ScrmDoctorList,c.Bind-错误=", kit.JsonEncode(err))
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}
	logStr := "boss-ScrmDoctorList,入参=" + kit.JsonEncode(p)
	glog.Info(logStr)
	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		glog.Error(logStr, ",c.Validate-错误=", kit.JsonEncode(validateErrList.All()))
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		glog.Error(logStr, "utils.MapTo-错误=", kit.JsonEncode(err))
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	glog.Info(logStr, ",rpcRqt-数据记录=", kit.JsonEncode(rpcRqt))

	client := dgc.GetDiagnoseServiceClient()
	if rpcRps, err = client.DCT.ScrmDoctorList(client.Ctx, &rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logStr, ",grpc-错误=", kit.JsonEncode(s.Message()))
			rps.Msg = s.Message()
			return c.JSON(400, rps)
		}
	}

	glog.Info(logStr, " 数据记录-rpcRps=", kit.JsonEncode(rpcRps))
	if rpcRps == nil {
		return c.JSON(200, rps)
	}

	rps.Total = rpcRps.Total
	if len(rpcRps.Data) > 0 {
		for _, row := range rpcRps.Data {
			tmp := params.ScrmDoctorSettingInfo{}
			tmp.DoctorCode = row.DoctorCode
			tmp.DoctorName = row.DoctorName
			tmp.DoctorPhone = row.DoctorPhone
			tmp.DoctorLevel = row.DoctorLevel
			tmp.HospitalName = row.HospitalName
			tmp.UpdateTime = row.UpdateTime
			tmp.QuickImageTextPrice = row.QuickImageTextPrice
			tmp.ImageTextPrice = row.ImageTextPrice
			tmp.VideoPrice = row.VideoPrice
			tmp.PhonePrice = row.PhonePrice
			tmp.IsForbidden = row.IsForbidden
			rps.Data = append(rps.Data, tmp)
		}
	}
	glog.Info(logStr, "，函数返回=", kit.JsonEncode(rps))
	return c.JSON(200, rps)
}

// DiagnoseSystemInfo
// @Summary 获取 系统医生接单设置
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Success 200 {object} params.DiagnoseSystemInfoResponse
// @Failure 400 {object} params.DiagnoseSystemInfoResponse
// @Router /boss/diagnose/system/info [GET]
func DiagnoseSystemInfo(c echo.Context) error {
	//声明变量
	var (
		out         = new(params.DiagnoseSystemInfoResponse)
		rpcRequest  = new(dgc.EmptyRequest)
		rpcResponse = new(dgc.DiagnoseSystemInfoResponse)
		err         error
	)

	//调用rpc服务
	client := dgc.GetDiagnoseServiceClient()
	if rpcResponse, err = client.DCT.DiagnoseSystemInfo(client.Ctx, rpcRequest); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	if err := utils.MapTo(rpcResponse, &out.Data); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	return c.JSON(200, out)
}

// DiagnoseInternetDoctorAdd
// @Summary 新增互联网医生
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.InternetDoctorAddRequest true " "
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/diagnose/doctor/internet/add [POST]
func DiagnoseInternetDoctorAdd(c echo.Context) error {
	var (
		p      params.InternetDoctorAddRequest
		rpcRqt dgc.InternetDoctorAddRequest
		err    error
		rps    params.BaseResponseNew
	)

	if err = c.Bind(&p); err != nil {
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}

	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}
	//判断手机号是否在北京ACP存在
	url := fmt.Sprintf("%s%s", config.GetString("bj-user-auth-url"), AcpUserUrl)
	dataParam := createCommonBjAcpParam()
	dataParam["mobile"] = p.Mobile

	//2：调用北京接口
	code, data := utils.HttpPostForm(url, "", "", dataParam)
	_, err = json.Marshal(dataParam)
	if err != nil {
		rps.Msg = "MapToJson转换出错"
		return c.JSON(400, rps)
	}
	if code != 200 {
		rps.Msg = "获取用户信息失败"
		return c.JSON(400, rps)
	}

	userInfo := params.AcpUserInfo{}
	err = json.Unmarshal([]byte(data), &userInfo)
	if err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}

	if userInfo.Status != 200 { //用户不存在
		rps.Msg = userInfo.Message
		return c.JSON(400, rps)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}

	//调用grpc
	client := dgc.GetDiagnoseServiceClient()
	if _, err := client.DCT.DiagnoseInternetDoctorAdd(client.Ctx, &rpcRqt); err != nil {
		if s, ok := status.FromError(err); ok {
			rps.Msg = s.Message()
			return c.JSON(400, rps)
		}
	}
	return c.JSON(200, rps)
}

// DiagnoseInternetDoctorList
// @Summary 互联网医生列表
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.InternetDoctorListRequest true " "
// @Success 200 {object} params.InternetDoctorListResponse
// @Failure 400 {object} params.InternetDoctorListResponse
// @Router /boss/diagnose/doctor/internet/list [POST]
func DiagnoseInternetDoctorList(c echo.Context) error {
	//声明变量
	var (
		p           = new(params.InternetDoctorListRequest)
		out         = params.InternetDoctorListResponse{}
		rpcRequest  dgc.InternetDoctorListRequest
		rpcResponse = new(dgc.InternetDoctorListResponse)
		err         error
	)

	//获得数据
	if err = c.Bind(p); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	// 请求参数数据验证
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}
	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRequest); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	//调用rpc服务
	client := dgc.GetDiagnoseServiceClient()
	if rpcResponse, err = client.DCT.DiagnoseInternetDoctorList(client.Ctx, &rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}

	if err := utils.MapTo(&rpcResponse, &out); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	return c.JSON(200, out)
}

// DiagnoseInternetDoctorDelete
// @Summary 启和、禁用互联网医生
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.InternetDoctorForbiddenRequest true " "
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/diagnose/doctor/internet/forbidden [POST]
func InternetDoctorForbidden(c echo.Context) error {
	var (
		p      params.InternetDoctorForbiddenRequest
		rpcRqt dgc.InternetDoctorForbiddenRequest
		err    error
		rps    params.BaseResponseNew
	)

	if err = c.Bind(&p); err != nil {
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}

	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}

	//调用grpc
	client := dgc.GetDiagnoseServiceClient()
	if _, err := client.DCT.InternetDoctorForbidden(client.Ctx, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	return c.JSON(200, rps)
}

// @Summary 医生接单设置
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.DiagnoseSetRequest true " "
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/diagnose/config/set [POST]
func DiagnoseSet(c echo.Context) error {
	var (
		p      params.DiagnoseSetRequest
		rpcRqt dgc.DiagnoseSetRequest
		err    error
		rps    params.BaseResponseNew
	)

	if err = c.Bind(&p); err != nil {
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}

	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}

	//调用grpc
	client := dgc.GetDiagnoseServiceClient()
	if _, err := client.DCT.DiagnoseSet(client.Ctx, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	return c.JSON(200, rps)
}

// @Summary 医生接单设置-禁用、启用
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.DoctorIsForbiddenRequest true " "
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/diagnose/doctor/isforbidden [POST]
func DoctorIsForbidden(c echo.Context) error {
	var (
		p      params.DoctorIsForbiddenRequest
		rpcRqt dgc.DoctorIsForbiddenRequest
		err    error
		rps    params.BaseResponseNew
	)

	if err = c.Bind(&p); err != nil {
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}

	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}

	//调用grpc
	client := dgc.GetDiagnoseServiceClient()
	if _, err := client.DCT.DoctorIsForbidden(client.Ctx, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	return c.JSON(200, rps)
}

// @Summary 获取互联网医生信息
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param InternetDoctorInfoRequest body params.InternetDoctorInfoRequest true " "
// @Success 200 {object} params.InternetDoctorInfoResponse
// @Failure 400 {object} params.InternetDoctorInfoResponse
// @Router /diagnose/doctor/internet/info [GET]
func InternetDoctorInfo(c echo.Context) error {
	//声明变量
	var (
		p           = new(params.InternetDoctorInfoRequest)
		out         = new(params.InternetDoctorInfoResponse)
		rpcRequest  dgc.InternetDoctorInfoRequest
		rpcResponse = new(dgc.InternetDoctorInfoResponse)
		err         error
	)

	//获得数据
	if err = c.Bind(p); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	// 请求参数数据验证
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}
	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRequest); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	//调用rpc服务
	client := dgc.GetDiagnoseServiceClient()
	if rpcResponse, err = client.DCT.InternetDoctorInfo(client.Ctx, &rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}

	if err := utils.MapTo(rpcResponse, &out.Data); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	return c.JSON(200, out)
}

// @Summary 修改互联网医生在线状态
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.DoctorOnlineChangeRequest true " "
// @Success 200 {object} params.BaseResponseNew
// @Failure 400 {object} params.BaseResponseNew
// @Router /boss/diagnose/doctor/online/change [POST]
func DoctorOnlineChange(c echo.Context) error {
	var (
		p      params.DoctorOnlineChangeRequest
		rpcRqt dgc.DoctorOnlineChangeRequest
		err    error
		rps    params.BaseResponseNew
	)

	if err = c.Bind(&p); err != nil {
		rps.Msg = "请求参数格式有误"
		return c.JSON(400, rps)
	}

	if err = c.Validate(&p); err != nil {
		validateErrList := validate.Translate(err)
		rps.Msg = validateErrList.One()
		return c.JSON(400, rps)
	}

	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}

	//调用grpc
	client := dgc.GetDiagnoseServiceClient()
	if _, err := client.DCT.DoctorOnlineChange(client.Ctx, &rpcRqt); err != nil {
		rps.Msg = err.Error()
		return c.JSON(400, rps)
	}
	return c.JSON(200, rps)
}

// @Summary 接单后台-医生在线状态和信息
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param InternetDoctorInfoRequest body params.DoctorStatusRequest true " "
// @Success 200 {object} params.DoctorStatusResponse
// @Failure 400 {object} params.DoctorStatusResponse
// @Router /boss/diagnose/doctor/status [POST]
func DoctorStatus(c echo.Context) error {
	var (
		p           = new(params.DoctorStatusRequest)
		out         = new(params.DoctorStatusResponse)
		rpcRequest  dgc.DoctorStatusRequest
		rpcResponse = new(dgc.DoctorStatusResponse)
		err         error
	)

	//获得数据
	if err = c.Bind(p); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	// 请求参数数据验证
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}
	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRequest); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	//调用rpc服务
	client := dgc.GetDiagnoseServiceClient()
	if rpcResponse, err = client.DCT.DoctorStatus(client.Ctx, &rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}

	if err := utils.MapTo(rpcResponse, &out.Data); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	return c.JSON(200, out)
}

// @Summary 接单后台-搜索医院
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param HospitalListRequest body params.HospitalListRequest true " "
// @Success 200 {object} params.HospitalListResponse
// @Failure 400 {object} params.HospitalListResponse
// @Router /boss/diagnose/hospital/list [POST]
func HospitalList(c echo.Context) error {
	var (
		p           = new(params.HospitalListRequest)
		out         = params.HospitalListResponse{}
		rpcRequest  dgc.HospitalListRequest
		rpcResponse = new(dgc.HospitalListResponse)
		err         error
	)

	//获得数据
	if err = c.Bind(p); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	// 请求参数数据验证
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}
	//组装rpc请求参数
	if err = utils.MapTo(&p, &rpcRequest); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	//调用rpc服务
	client := dgc.GetDiagnoseServiceClient()
	if rpcResponse, err = client.DCT.HospitalList(client.Ctx, &rpcRequest); err != nil {
		if s, ok := status.FromError(err); ok {
			out.Msg = s.Message()
			return c.JSON(400, out)
		}
	}

	if err := utils.MapTo(&rpcResponse, &out); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	return c.JSON(200, out)
}

// @Summary 互联网医生管理-根据手机号匹配医生信息
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param InternetDoctorInfoRequest body params.AcpUserInfoRequest true " "
// @Success 200 {object} params.AcpUserInfoResponse
// @Failure 400 {object} params.AcpUserInfoResponse
// @Router /boss/diagnose/acpuser/info [POST]
func AcpUserInfo(c echo.Context) error {
	var (
		p   = new(params.AcpUserInfoRequest)
		out = params.AcpUserInfoResponse{}
		err error
	)

	//获得数据
	if err = c.Bind(p); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	// 请求参数数据验证
	if err = c.Validate(p); err != nil {
		validateErrList := validate.Translate(err)
		out.Msg = validateErrList.One()
		return c.JSON(400, out)
	}

	url := fmt.Sprintf("%s%s", config.GetString("bj-user-auth-url"), AcpUserUrl)
	dataParam := createCommonBjAcpParam()
	dataParam["mobile"] = p.Mobile

	//2：调用北京接口
	code, data := utils.HttpPostForm(url, "", "", dataParam)

	_, err = json.Marshal(dataParam)
	if err != nil {
		out.Msg = "MapToJson转换出错"
		return c.JSON(400, out)
	}
	if code != 200 {
		out.Msg = "获取用户信息失败"
		return c.JSON(400, out)
	}

	userInfo := params.AcpUserInfo{}
	err = json.Unmarshal([]byte(data), &userInfo)
	if err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	if userInfo.Status != 200 {
		out.Msg = userInfo.Message
		return c.JSON(400, out)
	}

	out.Data.Mobile = userInfo.Data.UserInfo.UserMobile
	out.Data.UserName = userInfo.Data.UserInfo.UserRealName
	return c.JSON(200, out)
}

// @Summary 医生接单管理-导出EXCEL
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param DiagnoseListRequest body params.ScrmDoctorListExportRequest true " "
// @Success 200 {object} dgc.ScrmDoctorListResponse
// @Failure 400 {object} dgc.ScrmDoctorListResponse
// @Router /boss/diagnose/doctor/list [POST]
func ScrmDoctorListExport(c echo.Context) error {
	var model dgc.ScrmDoctorListExportRequest
	dataList := []params.ScrmDoctorExportData{}

	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := dgc.GetDiagnoseServiceClient()
	if out, err := client.DCT.ScrmDoctorListExport(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		if len(out.Data) == 0 {
			return c.JSON(400, &pm.BaseRes{Code: 400, Message: "无可下载的内容"})
		}
		//组装数据
		for _, row := range out.Data {
			tmp := params.ScrmDoctorExportData{}
			tmp.DoctorCode = row.DoctorCode
			tmp.DoctorName = row.DoctorName
			tmp.DoctorPhone = row.DoctorPhone
			tmp.DoctorLevel = row.DoctorLevel
			tmp.HospitalName = row.HospitalName
			tmp.EppCode = row.EppCode
			tmp.HospitalCity = row.HospitalCity
			tmp.Region = row.Region
			tmp.UpdateTime = row.UpdateTime
			tmp.PhonePrice = kit.FenToYuan(row.PhonePrice)
			tmp.QuickImageTextPrice = kit.FenToYuan(row.QuickImageTextPrice)
			tmp.ImageTextPrice = kit.FenToYuan(row.ImageTextPrice)
			tmp.VideoPrice = kit.FenToYuan(row.VideoPrice)
			tmp.IsForbidden = DoctorStatusSetting[row.IsForbidden]
			dataList = append(dataList, tmp)
		}

		// 生成一个新的文件
		file := xlsx.NewFile()
		// 添加sheet页
		sheet, _ := file.AddSheet("Sheet1")
		// 插入表头
		titleRow := sheet.AddRow()
		titleList := []string{"医生ID", "医生姓名", "手机号", "医生职级", "大区", "城市", "财务编码", "任职医院", "快速问诊-图文", "找医生-图文", "找医生-电话", "找医生-视频", "医生状态", "上次操作时间"}
		for _, v := range titleList {
			cell := titleRow.AddCell()
			cell.Value = v
			//表头字体颜色
			cell.GetStyle().Font.Color = "00000000"
			cell.GetStyle().Font.Bold = true
			//居中显示
			cell.GetStyle().Alignment.Horizontal = "center"
			cell.GetStyle().Alignment.Vertical = "center"
		}

		// 插入内容
		for _, v := range dataList {
			row := sheet.AddRow()
			row.WriteStruct(&v, -1)
		}

		//将数据存入buff中
		var buff bytes.Buffer
		if err := file.Write(&buff); err != nil {
			panic(err)
		}
		//设置请求头  使用浏览器下载
		c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("医生接单管理")))
		return c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
	}

}

// DiagnoseGetLatestOrder
// @Summary 获取用户与医生最后的订单信息
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param order_sn query string fasle "订单号"
// @Param doctor_id query string fasle "医生id"
// @Param user_id query string fasle "用户id"
// @Success 200 {object} params.GetLatestOrderResponse
// @Success 400 {object} params.BaseResponse
// @Router /boss/diagnose/order/get-latest-order [GET]
func DiagnoseGetLatestOrder(c echo.Context) error {
	var err error
	getLatestOrderResponse := params.GetLatestOrderResponse{}
	//新增参数
	orderSn := c.QueryParam("order_sn")
	doctorId := c.QueryParam("doctor_id")
	userId := c.QueryParam("user_id")
	doctorId = strings.Replace(strings.Replace(doctorId, "sit_awen_", "", -1), "uat_awen_", "", -1)

	//入参打印
	logStr := "/boss/diagnose/order/get-latest-order 请求参数：" + kit.JsonEncode(&dgc.GetLatestOrderRequest{
		DoctorId: doctorId,
		UserId:   userId,
		OrderSn:  orderSn,
	})
	glog.Info(logStr)

	//创建服务
	client := dgc.GetDiagnoseServiceClient()
	LatestOrder, err := client.ODR.GetLatestOrder(context.Background(), &dgc.GetLatestOrderRequest{
		DoctorId: doctorId,
		UserId:   userId,
		OrderSn:  orderSn,
	})

	glog.Info(logStr, "返回结果：", LatestOrder, err)

	if err != nil {
		getLatestOrderResponse.Msg = err.Error()
		return c.JSON(400, getLatestOrderResponse)
	}
	getLatestOrderResponseData := params.LatestOrderResponse{}
	_ = utils.MapTo(LatestOrder, &getLatestOrderResponseData)
	getLatestOrderResponse.Data = getLatestOrderResponseData
	return c.JSON(200, getLatestOrderResponse)
}

// UserRegistration
// @Summary 环信用户注册
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param content body params.UserRegistrationRequest true " "
// @Success 200 {object} params.BaseResponse
// @Router /boss/diagnose/im/user-registration [POST]
func UserRegistration(c echo.Context) error {
	var err error
	var jsonData params.UserRegistrationRequest

	//参数绑定
	err = c.Bind(&jsonData)
	if err != nil {
		return c.JSON(400, params.ImResponse{Msg: err.Error()})
	}

	//参数判断
	if jsonData.UserId == "" {
		return c.JSON(400, params.ImResponse{Msg: "userid不能为空"})
	}

	//入参打印
	logStr := "/boss/diagnose/im/user-registration 请求参数：" + kit.JsonEncode(&dgc.UserRegistrationRequest{
		UserId: jsonData.UserId,
	})
	glog.Info(logStr)

	//rpc请求数据
	//创建服务
	client := dgc.GetDiagnoseServiceClient()
	_, err = client.Im.UserRegistration(context.Background(), &dgc.UserRegistrationRequest{
		UserId: jsonData.UserId,
	})

	glog.Info(logStr, err)

	//错误判断
	if err != nil {
		return c.JSON(400, params.ImResponse{Msg: err.Error()})
	}
	return c.JSON(200, &params.ImResponse{Msg: "注册成功"})
}

// GetMessage
// @Summary 消息记录获取
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param doctor_id query string true "医生id"
// @Param user_id query string true "用户id"
// @Param order_sn query string true "订单编号"
// @Param end_time query string false "结束时间"
// @Param page_index query string true "页码"
// @Param page_size query string true "页数"
// @Success 200 {object} params.GetMessageResponse
// @Success 400 {object} params.ImResponse
// @Router /boss/diagnose/im/get-message [GET]
func GetMessage(c echo.Context) error {
	var err error

	//数据转换
	var pageIndex, pageSize, endTime int
	pageIndex, err = strconv.Atoi(c.QueryParam("page_index"))
	if err != nil {
		return c.JSON(400, err.Error())
	}
	pageSize, err = strconv.Atoi(c.QueryParam("page_size"))
	if err != nil {
		return c.JSON(400, err.Error())
	}
	if c.QueryParam("end_time") != "" {
		endTime, err = strconv.Atoi(c.QueryParam("end_time"))
		if err != nil {
			return c.JSON(400, err.Error())
		}
	}

	//入参打印
	logStr := "/boss/diagnose/im/get-message 请求参数：" + kit.JsonEncode(&dgc.GetMessageRequest{
		DoctorId:  c.QueryParam("doctor_id"),
		UserId:    c.QueryParam("user_id"),
		OrderSn:   c.QueryParam("order_sn"),
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		EndTime:   int64(endTime),
	})
	glog.Info(logStr)

	//rpc请求数据
	client := dgc.GetDiagnoseServiceClient()
	data, err := client.Im.GetMessage(context.Background(), &dgc.GetMessageRequest{
		DoctorId:  c.QueryParam("doctor_id"),
		UserId:    c.QueryParam("user_id"),
		OrderSn:   c.QueryParam("order_sn"),
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		EndTime:   int64(endTime),
	})
	glog.Info(logStr, "返回结果：", kit.JsonEncode(data), err)

	if err != nil {
		return c.JSON(400, err.Error())
	}

	//数组处理
	if len(data.List) <= 0 {
		data.List = make([]*dgc.GetMessageResponse_GetMessageList, 0)
	}

	return c.JSON(200, params.GetMessageResponse{
		Msg:   "获取成功",
		Total: data.Total,
		Data:  data.List,
	})
}

// DiagnoseAddFinishMessage
// @Summary 追加回复次数
// @Tags 在线问诊-管理后台
// @Accept json
// @Produce json
// @Param content body params.DiagnoseAddFinishMessage true " "
// @Success 200 {object} params.BaseResponseNew
// @Success 400 {object} params.BaseResponseNew
// @Router /boss/diagnose/doctor/add-finish-message [POST]
func DiagnoseAddFinishMessage(c echo.Context) error {
	var err error
	var jsonData params.DiagnoseAddFinishMessage

	//参数绑定
	err = c.Bind(&jsonData)
	if err != nil {
		return c.JSON(400, params.BaseResponseNew{Msg: err.Error()})
	}

	if jsonData.OrderSn == "" {
		return c.JSON(400, params.BaseResponseNew{Msg: "order_sn不能为空"})
	}
	if jsonData.Number == 0 {
		return c.JSON(400, params.BaseResponseNew{Msg: "number 不能为0"})
	}

	//入参打印
	logStr := "/boss/diagnose/doctor/add-finish-message 请求参数：" + kit.JsonEncode(&dgc.DiagnoseAddFinishMessageRequest{
		OrderSn: jsonData.OrderSn,
		Number:  int32(jsonData.Number),
	})
	glog.Info(logStr)

	//rpc请求数据
	client := dgc.GetDiagnoseServiceClient()
	_, err = client.ODR.DiagnoseAddFinishMessage(context.Background(), &dgc.DiagnoseAddFinishMessageRequest{
		OrderSn: jsonData.OrderSn,
		Number:  int32(jsonData.Number),
	})
	glog.Info(logStr, ";返回结果：", err)

	if err != nil {
		return c.JSON(400, err.Error())
	}

	return c.JSON(200, params.BaseResponseNew{
		Msg: "更新成功",
	})
}
