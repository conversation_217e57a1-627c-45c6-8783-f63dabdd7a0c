package controller

import (
	pb "_/proto/ic"
	"context"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"google.golang.org/grpc"
)

type InventoryCenterApi struct {
}

//inventorycenter的链接
func Getinventorylient() (*grpc.ClientConn, context.Context, pb.InventoryServiceClient, context.CancelFunc) {
	if conn, err := grpc.Dial(config.GetString("grpc.inventory-center"), grpc.WithInsecure()); err != nil {
		return nil, nil, nil, nil
	} else {
		client := pb.NewInventoryServiceClient(conn)
		ctx, cf := context.WithTimeout(context.Background(), time.Second*30)
		return conn, ctx, client, cf
	}
}

/*
// @Summary 查询商品库存是否充足
// @Tags 库存中心
// @Accept json
// @Produce json
// @Param brand body pc.Brand true " "
// @Success 200 {object} pb.StockResponse
// @Failure 400 {object} pb.StockResponse
// @Router /inventorycenter/stock/getstockbysku [get]
func GetStockByProductId(c echo.Context) error {
	conn, ctx, client, cf := Getinventorylient()
	defer conn.Close()
	defer cf()
	var out pb.StockResponse
	var model pb.StockRequest
	if err := c.Bind(&model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	r, e := client.GetStockByProductId(ctx, &model)
	if e != nil {
		out.Code = 400
		out.Message = "查询报错:" + e.Error()
	} else {
		out.Code = 200
		out.WarehouseGoodsList = r.WarehouseGoodsList
	}
	return c.JSON(int(out.Code), out)
}

// @Summary 冻结商品库存
// @Tags 库存中心
// @Accept json
// @Produce json
// @Param brand body pc.Brand true " "
// @Success 200 {object} pb.FreezeResponse
// @Failure 400 {object} pb.FreezeResponse
// @Router /inventorycenter/stock/freezestock [post]
func FreezeStock(c echo.Context) error {
	conn, ctx, client, cf := Getinventorylient()
	defer conn.Close()
	defer cf()
	var out pb.FreezeResponse
	var model pb.FreezeRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pb.FreezeResponse{Code: 400, Message: err.Error()})
	}
	_, e := client.FreezeStock(ctx, &model)
	if e != nil {
		out.Code = 400
		out.Message = "冻结报错:" + e.Error()
	} else {
		out.Code = 200
	}
	return c.JSON(int(out.Code), out)
}
*/

// @Summary 导入库存
// @Tags 库存中心
// @Accept json
// @Produce json
// @Param in body ic.ThirdInfoRequest true " "
// @Success 200 {object} ic.ThirdInfoResponse
// @Failure 400 {object} ic.ThirdInfoResponse
// @Router /boss/inventorycenter/stock/import [post]
//TODO：proto.ThirdInfoRequest会引发swag文档生成不了，原因未明，暂时注释
//add by csf 类型包引用错误，自己不改，我帮忙改，修正swag文档生成不了，原因未明的问题
func ImportThirdStock(c echo.Context) error {
	conn, ctx, client, cf := Getinventorylient()
	defer conn.Close()
	defer cf()
	var in pb.ThirdInfoRequest
	if err := c.Bind(&in); err != nil {
		return c.JSON(400, &pb.ThirdInfoResponse{Code: 400, Message: err.Error()})
	}
	if out, err := client.ImportThirdStock(ctx, &in); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(200, out)
	}
}
