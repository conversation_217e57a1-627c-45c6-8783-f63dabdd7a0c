name: 'Tests on Windows: `nvm install`'

on: [pull_request, push]

permissions:
  contents: read

env:
  NVM_INSTALL_GITHUB_REPO: ${{ github.repository }}
  NVM_INSTALL_VERSION: ${{ github.sha }}

jobs:
  msys_fail_install:
    # Default installation does not work due to npm_config_prefix set to C:\npm\prefix
    permissions:
      contents: none
    name: 'MSYS fail prefix nvm install'
    runs-on: windows-latest
    steps:
      - name: Retrieve nvm
        shell: bash
        run: |
          curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | METHOD=script bash
          . "$HOME/.nvm/nvm.sh"
          ! nvm install --lts

  msys_matrix:
    permissions:
      contents: none
    name: 'MSYS nvm install'
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        npm-node-version:
          - '--lts'
          - '--default 12'
          - '--no-progress 10'
    steps:
      - name: Retrieve nvm
        shell: bash
        run: |
          unset npm_config_prefix
          if [ "${{ matrix.npm-node-version }}" = "--lts" ]; then
            curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | bash
          else
            curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | METHOD=script bash
          fi
          . "$HOME/.nvm/nvm.sh"
          nvm install ${{ matrix.npm-node-version }}

  cygwin_matrix:
    continue-on-error: true
    permissions:
      contents: none
    name: 'Cygwin nvm install'
    runs-on: windows-latest
    steps:
      - name: Install Cygwin
        shell: bash
        run: |
          export SITE='https://mirror.clarkson.edu/cygwin/' # see https://archlinux.org/mirrors/clarkson.edu/1603/ for uptime status
          export SITE='https://mirrors.kernel.org/sourceware/cygwin/'
          export LOCALDIR="$(pwd)"
          export ROOTDIR="$USERPROFILE\\cygwin"
          export PACKAGES='bash,git,curl'

          curl -fsSLo setup-x86_64.exe 'https://cygwin.com/setup-x86_64.exe'
          ./setup-x86_64.exe --disable-buggy-antivirus -q -s "$SITE" -l "$LOCALDIR" -R "$ROOTDIR" -P "$PACKAGES"

          cat >~/setup.sh <<EOM
            unset npm_config_prefix
            export NVM_INSTALL_GITHUB_REPO="$NVM_INSTALL_GITHUB_REPO"
            export NVM_INSTALL_VERSION="$NVM_INSTALL_VERSION"
            export HOME="$(cygpath -u "$USERPROFILE")"

            echo "HOME is $HOME"
            curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | bash
            ls -l $HOME/.nvm
            . "$HOME/.nvm/nvm.sh"
            nvm install --lts

            nvm deactivate
            rm -rf "$HOME/.nvm/nvm.sh"

            curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | METHOD=script bash
            . "$HOME/.nvm/nvm.sh"
            nvm install 9
          EOM
      - name: Retrieve nvm
        shell: cmd
        run: |
          cd %USERPROFILE%\cygwin\bin
          bash.exe "%USERPROFILE%\setup.sh"

  wsl_matrix:
    continue-on-error: true
    name: 'WSL nvm install'
    defaults:
      run:
          shell: wsl-bash {0}
    runs-on: windows-latest
    env:
      WSLENV: NVM_INSTALL_GITHUB_REPO:NVM_INSTALL_VERSION:/p
    strategy:
      fail-fast: false
      matrix:
        wsl-distrib:
          - Debian
        # - Alpine # fails
          - Ubuntu-20.04
          - Ubuntu-18.04
        npm-node-version:
          - '--lts'
          - '21'
          - '18'
          - '16'
          - '14'
          - '12'
          - '10'
        exclude:
          - wsl-distrib: Ubuntu-18.04
            npm-node-version: '--lts'
          - wsl-distrib: Ubuntu-18.04
            npm-node-version: '21'
          - wsl-distrib: Ubuntu-18.04
            npm-node-version: '18'
        method:
          - ''
          - 'script'
    steps:
      - uses: Vampire/setup-wsl@v3
        with:
          distribution: ${{ matrix.wsl-distrib }}
          additional-packages: bash git curl ca-certificates wget
      - name: Retrieve nvm on WSL
        run: |
          if [ -z "${{ matrix.method }}" ]; then
            curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | bash
          else
            curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | METHOD="${{matrix.method}}" bash
          fi
          . "$HOME/.nvm/nvm.sh"
          nvm install ${{ matrix.npm-node-version }}
          node -v

  wsl_matrix_unofficial:
    continue-on-error: true
    name: 'WSL nvm install'
    defaults:
      run:
          shell: wsl-bash {0}
    runs-on: windows-latest
    env:
      WSLENV: NVM_INSTALL_GITHUB_REPO:NVM_INSTALL_VERSION:/p
      NVM_NODEJS_ORG_MIRROR: https://unofficial-builds.nodejs.org/download/release
    strategy:
      fail-fast: false
      matrix:
        wsl-distrib:
          - Alpine
        npm-node-version:
          - '--lts'
          - '21'
          - '18'
          - '16'
          - '14'
          - '12'
          - '11'
          - '10'
        method:
          - ''
          - 'script'
    steps:
      - uses: Vampire/setup-wsl@v3
        with:
          distribution: ${{ matrix.wsl-distrib }}
          additional-packages: bash git curl ca-certificates wget
      - name: Retrieve nvm on WSL
        run: |
          if [ -z "${{ matrix.method }}" ]; then
            curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | bash
          else
            curl -fsSLo- "https://raw.githubusercontent.com/${NVM_INSTALL_GITHUB_REPO}/${NVM_INSTALL_VERSION}/install.sh" | METHOD="${{matrix.method}}" bash
          fi
          . "$HOME/.nvm/nvm.sh"
          NVM_NODEJS_ORG_MIRROR=${{ env.NVM_NODEJS_ORG_MIRROR }} nvm install ${{ matrix.npm-node-version }}

  nvm_windows:
    name: 'tests, on windows'
    permissions:
      contents: none
    needs: [wsl_matrix, wsl_matrix_unofficial, cygwin_matrix, msys_matrix, msys_fail_install]
    runs-on: ubuntu-latest
    steps:
      - run: true
