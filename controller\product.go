package controller

import (
	"_/dto"
	"_/enum"
	"_/models"
	"_/proto/et"
	"_/proto/pc"
	"_/proto/sh"
	"_/utils"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/labstack/echo/v4"

	kit "github.com/tricobbler/rp-kit"

	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

type Product struct{}

// @Summary 新增品牌
// @Tags 商家品牌
// @Accept json
// @Produce json
// @Param brand body pc.Brand true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/brand/new [post]
func NewBrand(c echo.Context) error {
	var model pc.Brand
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewBrand(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 品牌列表
// @Tags 商家品牌
// @Accept plain
// @Produce json
// @Param id query string false "品牌ID"
// @Param name query string false "品牌名称"
// @Param initial query string false "品牌首字母"
// @Param category_id query string false "品牌分类ID"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.BrandResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/brand/list [get]
func QueryBrand(c echo.Context) error {
	id, _ := strconv.Atoi(c.QueryParam("id"))
	name := c.QueryParam("name")
	initial := c.QueryParam("initial")
	category_id, _ := strconv.Atoi(c.QueryParam("category_id"))
	pageIndex, _ := strconv.Atoi(c.QueryParam("page_index"))
	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryBrand(client.Ctx, &pc.BrandRequest{
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		Where: &pc.Brand{
			Id:              int32(id),
			Name:            name,
			Initial:         initial,
			BrandCategoryId: int32(category_id),
		},
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除品牌
// @Tags 商家品牌
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/brand/del [get]
func DelBrand(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelBrand(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 通过skuId获取商品标签
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param sku_id query string true "skuId"
// @Success 200 {object} pc.ProductTagsResponse
// @Failure 400 {object} pc.ProductTagsResponse
// @Router /boss/product/product/get-product-tags [get]
func GetProductTags(c echo.Context) error {
	skuId, _ := strconv.Atoi(c.QueryParam("sku_id"))
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.GetProductTags(client.Ctx, &pc.ProductTagsRequest{
		SkuId: int32(skuId),
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary v6.3.5  新增/编辑平台商品
// @Tags v6.3.5 商品库
// @Accept json
// @Produce plain
// @Param product body pc.ProductRequest true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/product/new [post]
func NewProduct(c echo.Context) error {
	var product pc.ProductRequest
	if err := c.Bind(&product); err != nil {
		return c.JSON(400, pc.BaseResponse{Code: 400, Message: err.Error()})
	}
	if len(product.SkuInfo) == 0 {
		return c.JSON(400, pc.BaseResponse{Code: 400, Message: "SKU规格信息为空"})
	}
	/*if len(product.Product.ShortName) < 1 {
		return c.JSON(400, pc.BaseResponse{Code: 400, Message: "短标题为必填项"})
	}*/
	if len([]rune(product.Product.ShortName)) > 15 {
		return c.JSON(400, pc.BaseResponse{Code: 400, Message: "短标题最多15个汉字"})
	}

	// 药品
	if product.Product.IsPrescribedDrug == 1 && product.Product.ProductType == 1 {
		if product.Product.DosingDays < 1 {
			return c.JSON(400, pc.BaseResponse{Code: 400, Message: "处方药投药天数不能为空"})
		}
		if len(product.Product.Disease) == 0 {
			return c.JSON(400, pc.BaseResponse{Code: 400, Message: "处方药对应病症不能为空"})
		}
		if product.Product.DrugDosage == nil {
			return c.JSON(400, pc.BaseResponse{Code: 400, Message: "处方药用量信息不存在"})
		}
	}

	for _, v := range product.SkuInfo {
		if v.MarketPrice == 0 && product.Product.ProductType != 3 {
			return c.JSON(400, pc.BaseResponse{Code: 400, Message: "市场价不能为0"})
		}
		if v.BarCode == "" && product.Product.ProductType == 1 {
			return c.JSON(400, pc.BaseResponse{Code: 400, Message: "条码信息不能为空或为0"})
		}
		if product.Product.ProductType == 1 {
			tempInt, err := strconv.ParseFloat(v.BarCode, 64)
			if err != nil {
				return c.JSON(400, pc.BaseResponse{Code: 400, Message: "条码只能为纯数字"})
			}
			if tempInt == 0 {
				return c.JSON(400, pc.BaseResponse{Code: 400, Message: "条码信息不能为空或为0"})
			}
		}
	}

	//编辑新增商品的时候去除两边的空格
	NewSkuInfo := RemoveThirdSkuSpaces(product.SkuInfo)
	product.SkuInfo = NewSkuInfo

	glog.Info("去除第三方货号空格： ", kit.JsonEncode(product))

	client := GetDcProductClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	var err error
	var out *pc.BaseResponse
	if product.Product.Id == 0 {
		product.Product.SourceType = 2
		out, err = client.RPC.NewProduct(client.Ctx, &product)
	} else {
		out, err = client.RPC.EditProduct(client.Ctx, &product)
	}

	if err != nil {
		return c.JSON(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// 去除货号的空格
func RemoveThirdSkuSpaces(skuInfo []*pc.SkuInfo) []*pc.SkuInfo {

	for i := range skuInfo {
		for k := range skuInfo[i].SkuThird {
			thirdSkuId := skuInfo[i].SkuThird[k].ThirdSkuId
			thirdSpuId := skuInfo[i].SkuThird[k].ThirdSpuId
			skuInfo[i].SkuThird[k].ThirdSkuId = strings.TrimSpace(thirdSkuId)
			skuInfo[i].SkuThird[k].ThirdSpuId = strings.TrimSpace(thirdSpuId)
		}
	}

	return skuInfo
}

// @Summary 删除商品
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param id query int true "ID"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/product/del [get]
func DelProduct(c echo.Context) error {
	s := strings.Split(c.QueryParam("id"), ",")
	var arr pc.ArrayIntValue
	for _, v := range s {
		id, _ := strconv.Atoi(v)
		arr.Value = append(arr.Value, int32(id))
	}

	client := GetDcProductClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelProduct(client.Ctx, &arr); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 编辑商品已使用状态
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param id query int true "商品ID"
// @Param channel_id query string false "渠道ID"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/product/edit-isuse [get]
/*func EditProductIsUse(c echo.Context) error {
	channel_id := c.QueryParam("channel_id")
	//渠道id默认值
	if len(channel_id) == 0 {
		channel_id = "1"
	}

	product := &pc.ProductRequest{
		Product: &pc.Product{
			Id:        cast.ToInt32(c.QueryParam("id")),
			ChannelId: channel_id,
		},
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()


	if res, err := client.RPC.EditProductIsUse(client.Ctx, product); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(200, res)
	}
}*/

// @Summary v6.5.8 编辑查询商品
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param id query int true "商品ID"
// @Success 200 {object} pc.ProductOneResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/product/edit-query [get]
func EditQueryProductOne(c echo.Context) error {
	id, _ := strconv.Atoi(c.QueryParam("id"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var por pc.ProductOneResponse

	//查询商品主库信息
	if res, err := client.RPC.QueryProductOnly(client.Ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		if len(res.Details) == 0 {
			return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "未查询到商品信息"})
		}
		por.Product = res.Details[0]
	}

	//查询商品属性
	if res, err := client.RPC.QueryProductAttr(client.Ctx, &pc.IdRequest{Id: strconv.Itoa(id)}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		por.ProductAttr = res.Details
	}

	//查询商品SKU
	var sku []*pc.Sku
	if res, err := client.RPC.QuerySku(client.Ctx, &wrappers.Int32Value{Value: int32(id)}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		sku = res.Details
	}

	//查询SKU值
	var skuValue []*pc.SkuValue
	if res, err := client.RPC.QuerySkuValue(client.Ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		skuValue = res.Details
	}
	//查询第三方货号
	var skuThird []*pc.SkuThird
	if res, err := client.RPC.QuerySkuThird(client.Ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		skuThird = res.Details
	}

	//组合商品明细
	var skuGroup []*pc.SkuGroup
	if por.Product.ProductType == 3 {
		if res, err := client.RPC.QuerySkuGroup(client.Ctx, &pc.OneofIdRequest{Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: []int32{int32(id)}}}}); err != nil {
			glog.Error(err)
			return c.JSON(400, err)
		} else {
			skuGroup = res.Details
		}
	}

	//规格、规格值
	spec := make(map[int32]string)
	specValue := make(map[int32]string)
	var specId strings.Builder
	var specValueId strings.Builder
	for i, v := range skuValue {
		specId.WriteString(strconv.Itoa(int(v.SpecId)))
		specValueId.WriteString(strconv.Itoa(int(v.SpecValueId)))
		if i != len(skuValue)-1 {
			specId.WriteString(",")
			specValueId.WriteString(",")
		}
	}

	if res, err := client.RPC.QuerySpecSingle(client.Ctx, &pc.IdRequest{Id: specId.String()}); err != nil {
		glog.Error(err)
	} else {
		for _, v := range res.Details {
			spec[v.Id] = v.Name
		}
	}

	if res, err := client.RPC.QuerySpecValue(client.Ctx, &pc.IdRequest{Id: specValueId.String()}); err != nil {
		glog.Error(err)
	} else {
		for _, v := range res.Details {
			specValue[v.Id] = v.Value
		}
	}

	for _, s := range sku {
		skuInfo := &pc.SkuInfo{
			RetailPrice:   s.RetailPrice,
			SkuId:         s.Id,
			ProductId:     s.ProductId,
			MarketPrice:   s.MarketPrice,
			BarCode:       s.BarCode,
			WeightForUnit: s.WeightForUnit,
		}
		//第三方货号
		for _, t := range skuThird {
			if t.SkuId == s.Id {
				skuInfo.SkuThird = append(skuInfo.SkuThird, t)
			}
		}

		//sku value
		for _, v := range skuValue {
			if v.SkuId == s.Id {
				skuv := *v
				skuv.SpecName = spec[skuv.SpecId]
				skuv.SpecValueValue = specValue[skuv.SpecValueId]
				skuInfo.Skuv = append(skuInfo.Skuv, &skuv)
			}
		}

		//组合商品的组合明细
		if por.Product.ProductType == 3 {
			for _, g := range skuGroup {
				skuInfo.SkuGroup = append(skuInfo.SkuGroup, g)
			}
		}
		por.SkuInfo = append(por.SkuInfo, skuInfo)
	}
	por.Code = 200
	return c.JSON(200, por)
}

// @Summary 查询商品属性
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param id query string true "商品ID"
// @Success 200 {object} pc.ProductAttrResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/attr/query [get]
func QueryProAttr(c echo.Context) error {
	id := c.QueryParam("id")

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryProductAttr(client.Ctx, &pc.IdRequest{Id: id}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 批量查询商品及SKU（商品ID/SKUID选其一）
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param product_id query string false "商品ID，多个以逗号分隔"
// @Param sku_id query string false "SKU ID，多个以逗号分隔"
// @Success 200 {object} pc.ProductResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/product/product-sku [get]
func QueryProductSku(c echo.Context) error {
	in := &pc.OneofIdRequest{}
	var productId, skuId []int32

	if id := c.QueryParam("product_id"); id != "" {
		for _, v := range strings.Split(id, ",") {
			i, _ := strconv.Atoi(v)
			productId = append(productId, int32(i))
		}

	}
	if id := c.QueryParam("sku_id"); id != "" {
		for _, v := range strings.Split(id, ",") {
			i, _ := strconv.Atoi(v)
			skuId = append(skuId, int32(i))
		}
	}

	if len(productId) > 0 {
		in.Id = &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: productId}}
	} else if len(skuId) > 0 {
		in.Id = &pc.OneofIdRequest_SkuId{SkuId: &pc.ArrayIntValue{Value: skuId}}
	} else {
		return c.JSON(400, &pc.BaseResponse{Message: "商品ID与SKUID为空", Code: 400})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if res, err := client.RPC.QueryProductSku(client.Ctx, in); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(200, res)
	}

}

/*
// @Summary 分页查询渠道商品列表
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param where query string false "商品筛选条件"
// @Param where_type query string false "商品筛选类型，为空则为综合筛选（name=商品名称，bar_code=商品条码，code=商品编码，id=商品ID）"
// @Param category_id query string false "商品分类id"
// @Param brand_id query string false "品牌id"
// @Param is_group query int false "是否为组合商品"
// @Param product_type query int false "商品类别（0-所有，1-实物商品，2-虚拟商品）"
// @Param channel_id query int true "渠道id"
// @Param page_index query int false "分页索引"
// @Param page_size query int false "分页大小"
// @Param up_down_state query int false "上下架状态（-1全部，1-上架，0-下架）"
// @Param finance_code query string false "财务编码"
// @Success 200 {object} pc.ProductResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/channel-product/list [get]
func QueryChannelProduct(c echo.Context) error {
	channelID := cast.ToInt32(c.QueryParam("channel_id"))
	client := GetDcProductClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	upDownState := c.QueryParam("up_down_state")
	if upDownState == "" {
		upDownState = "-1"
	}
	var params pc.QueryProductRequest
	params.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	params.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	params.Where = c.QueryParam("where")
	params.WhereType = c.QueryParam("where_type")
	params.CategoryId = cast.ToInt32(c.QueryParam("category_id"))
	params.BrandId = cast.ToInt32(c.QueryParam("brand_id"))
	params.IsGroup = cast.ToInt32(c.QueryParam("is_group"))
	params.ProductType = cast.ToInt32(c.QueryParam("product_type"))
	params.IsDel = cast.ToInt32(c.QueryParam("is_del"))
	params.ChannelId = channelID
	params.UpDownState = cast.ToInt32(upDownState)
	params.FinanceCode = c.QueryParam("finance_code")
	glog.Info("QueryChannelProduct请求参数=", params)
	fmt.Println("QueryChannelProduct请求参数=", params)
	if out, err := client.RPC.QueryChannelProduct(client.Ctx, &pc.QueryProductRequest{
		PageIndex:   cast.ToInt32(c.QueryParam("page_index")),
		PageSize:    cast.ToInt32(c.QueryParam("page_size")),
		Where:       c.QueryParam("where"),
		WhereType:   c.QueryParam("where_type"),
		CategoryId:  cast.ToInt32(c.QueryParam("category_id")),
		BrandId:     cast.ToInt32(c.QueryParam("brand_id")),
		IsGroup:     cast.ToInt32(c.QueryParam("is_group")),
		ProductType: cast.ToInt32(c.QueryParam("product_type")),
		IsDel:       cast.ToInt32(c.QueryParam("is_del")),
		ChannelId:   channelID,
		UpDownState: cast.ToInt32(upDownState),
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		in := pc.ChannelProductSnapshotRequest{ChannelId: channelID}
		if len(out.Details) > 0 {
			for _, v := range out.Details {
				in.ProductId = append(in.ProductId, v.Id)
			}

			//取快照
			if params.FinanceCode != "" {
				in.FinanceCode = params.FinanceCode
				if outSnapshot, err := client.RPC.QueryChannelProductSnapshot(client.Ctx, &in); err != nil {
					glog.Error(err)
					return c.JSON(400, err)
				} else {
					for _, v := range out.Details {
						for _, v2 := range outSnapshot.Details {
							if v.Id == v2.ProductId {
								var snapIn pc.ChannelProductRequest
								if err := json.Unmarshal([]byte(v2.JsonData), &snapIn); err != nil {
									glog.Error(err)
								}
								*v = *snapIn.Product
							}
						}
					}
				}
			}

		}
		return c.JSON(int(out.Code), out)
	}
}*/

// @Summary 分页查询渠道商品列表导出
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param where query string false "商品筛选条件"
// @Param where_type query string false "商品筛选类型，为空则为综合筛选（name=商品名称，bar_code=商品条码，code=商品编码，id=商品ID）"
// @Param category_id query string false "商品分类id"
// @Param brand_id query string false "品牌id"
// @Param is_group query int false "是否为组合商品"
// @Param product_type query int false "商品类别（0-所有，1-实物商品，2-虚拟商品）"
// @Param channel_id query int true "渠道id"
// @Param page_index query int false "分页索引"
// @Param page_size query int false "分页大小"
// @Param up_down_state query int false "上下架状态（-1全部，1-上架，0-下架）"
// @Success 200 {object} pc.ProductResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/channel-product/export [get]
/*func QueryChannelProductExport(c echo.Context) error {
	channelID := cast.ToInt32(c.QueryParam("channel_id"))
	client := GetDcProductClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	//根据门店名称，获取财务编码
	var arrFinanceCode []string

	dacClient := GetDataCenterClient(c)
	defer dacClient.Conn.Close()
	defer dacClient.Cf()

	var err error
	var outStoreInfo *dac.StoreInfoResponse

	//获取登录用户权限范围内的关联到渠道的门店
	if outStoreInfo, err = dacClient.RPC.QueryStoreInfoUserAuthority(dacClient.Ctx, &dac.StoreInfoRequest{Name: "", ChannelId: int32(channelID)}); err != nil || outStoreInfo.Code != 200 {
		glog.Error(err)
		if err != nil {
			return c.JSON(400, err)
		} else {
			return c.JSON(200, outStoreInfo)
		}
	} else {
		if len(outStoreInfo.Details) == 0 {
			return c.JSON(200, models.ChannelStoreProductResponse{Code: 200})
		}
		for _, v := range outStoreInfo.Details {
			arrFinanceCode = append(arrFinanceCode, v.FinanceCode)
		}
	}

	upDownState := c.QueryParam("up_down_state")
	if upDownState == "" {
		upDownState = "-1"
	}
	var params pc.QueryProductExportRequest
	params.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	params.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	params.PageSize = 999999 //修改总页数
	params.Where = c.QueryParam("where")
	params.WhereType = c.QueryParam("where_type")
	params.CategoryId = cast.ToInt32(c.QueryParam("category_id"))
	params.BrandId = cast.ToInt32(c.QueryParam("brand_id"))
	params.IsGroup = cast.ToInt32(c.QueryParam("is_group"))
	params.ProductType = cast.ToInt32(c.QueryParam("product_type"))
	params.IsDel = cast.ToInt32(c.QueryParam("is_del"))
	params.ChannelId = channelID
	params.UpDownState = cast.ToInt32(upDownState)
	params.FinanceCode = arrFinanceCode
	glog.Info("QueryChannelProduct请求参数=", params)
	fmt.Println("QueryChannelProduct请求参数=", params)
	if out, err := client.RPC.QueryChannelProductExport(client.Ctx, &params); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		if len(out.Details) > 0 {
			////解析快照商品信息
			//for _, v := range out.Details {
			//	var snapIn pc.ChannelProductRequest
			//	if err := json.Unmarshal([]byte(v.JsonData), &snapIn); err != nil {
			//		glog.Error(err)
			//	}
			//	//*v = *snapIn.Product
			//
			//	pro.Product = snapIn.Product
			//	pro.SkuInfo = snapIn.SkuInfo
			//	pro.ProductAttr = snapIn.ProductAttr
			//
			//	v.Id = snapIn.Product.Id
			//}

			//导出
			var channelName string
			switch channelID {
			case 1:
				channelName = "阿闻"
				break
			case 2:
				channelName = "美团"
				break
			case 3:
				channelName = "饿了么"
				break
			}

			f := excelize.NewFile()
			header := []string{"门店ID", "门店名称", "所属大区", "省", "城市", "商品名称", "商品类别", "商品类目", "店内商品分类", "渠道", "状态"}
			for i := 0; i < len(header); i++ {
				f.SetCellValue("Sheet1", string(65+i)+"1", header[i])
			}
			for i := 0; i < len(out.Details); i++ {
				v := out.Details[i]
				//解析快照商品信息
				var snapIn pc.ChannelProductRequest
				if err := json.Unmarshal([]byte(v.JsonData), &snapIn); err != nil {
					glog.Error(err)
				}
				storeName := "" //渠道门店名称
				bigRegion := ""
				province := ""
				city := ""
				for _, v2 := range outStoreInfo.Details {
					if v.FinanceCode == v2.FinanceCode {
						storeName = v2.Name
						bigRegion = v2.Bigregion
						province = v2.Province
						city = v2.City
						break
					}
				}
				//if storeName == ""{
				//	continue //门店为空的数据过滤掉
				//}
				productType := "实物商品"
				if v.ProductType == 2 {
					productType = "虚拟商品"
				}
				channelCategoryName := ""
				if snapIn.Product != nil {
					channelCategoryName = snapIn.Product.ChannelCategoryName
				}

				f.SetCellValue("Sheet1", "A"+strconv.Itoa(i+2), v.FinanceCode) //门店id
				f.SetCellValue("Sheet1", "B"+strconv.Itoa(i+2), storeName)
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(i+2), bigRegion)   //所属大区
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(i+2), province)    //省
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(i+2), city)        //城市
				//f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), v.Id)          //平台商品id
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), v.Name)      //渠道商品名
				f.SetCellValue("Sheet1", "G"+strconv.Itoa(i+2), productType) //商品类别
				f.SetCellValue("Sheet1", "H"+strconv.Itoa(i+2), v.CategoryName)
				f.SetCellValue("Sheet1", "I"+strconv.Itoa(i+2), channelCategoryName)
				f.SetCellValue("Sheet1", "J"+strconv.Itoa(i+2), channelName)
				if v.UpDownState == 0 {
					f.SetCellValue("Sheet1", "K"+strconv.Itoa(i+2), "下架")
				} else {
					f.SetCellValue("Sheet1", "K"+strconv.Itoa(i+2), "上架")
				}
			}
			f.Save()
			var buff bytes.Buffer
			if err = f.Write(&buff); err != nil {
				return c.JSON(200, &models.ChannelStoreProductResponse{Code: 400, Message: "导出文件失败"})
			}
			fileName := "store-product -" + time.Now().Format("20060102150405") + ".xlsx"
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
			return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
		}

		return c.JSON(int(out.Code), out)
	}
}*/

// @Summary v6.5.1 分页查询商品列表
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param model query pc.QueryProductRequest true " "
// @Success 200 {object} pc.ProductResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/product/list [get]
func QueryProduct(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryProduct(client.Ctx, &pc.QueryProductRequest{
		PageIndex:        cast.ToInt32(c.QueryParam("page_index")),
		PageSize:         cast.ToInt32(c.QueryParam("page_size")),
		Where:            c.QueryParam("where"),
		WhereType:        c.QueryParam("where_type"),
		CategoryId:       cast.ToInt32(c.QueryParam("category_id")),
		BrandId:          cast.ToInt32(c.QueryParam("brand_id")),
		IsGroup:          cast.ToInt32(c.QueryParam("is_group")),
		ProductType:      cast.ToInt32(c.QueryParam("product_type")),
		IsDel:            cast.ToInt32(c.QueryParam("is_del")),
		IsGj:             cast.ToInt32(c.QueryParam("is_gj")),
		IsDrugs:          cast.ToInt32(c.QueryParam("is_drugs")),
		UseRange:         cast.ToInt32(c.QueryParam("use_range")),
		IsExclude:        cast.ToInt32(c.QueryParam("is_exclude")),
		SelectType:       cast.ToInt32(c.QueryParam("select_type")),
		SourceType:       cast.ToInt32(c.QueryParam("source_type")),
		IsPrescribedDrug: cast.ToInt32(c.QueryParam("is_prescribed_drug")),
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary v6.0 分页查询sku产品信息
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param where query string false "商品筛选条件"
// @Param where_type query string false "商品筛选类型，为空则为综合筛选（name=商品名称，bar_code=商品条码，code=商品编码，id=商品ID,sku_id=SKUID）"
// @Param page_index query int false "分页索引"
// @Param page_size query int false "分页大小"
// @Param is_exclude query int false "是否排除过期虚拟商品"  (0-否 ，1-是)
// @Success 200 {object} pc.ProductResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/product-sku/list [get]
func ListProductSku(c echo.Context) error {
	where := c.QueryParam("where")
	where_type := c.QueryParam("where_type")
	pageIndex, _ := strconv.Atoi(c.QueryParam("page_index"))
	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))
	is_group, _ := strconv.Atoi(c.QueryParam("is_group"))
	is_exclude, _ := strconv.Atoi(c.QueryParam("is_exclude"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.ListProductSku(client.Ctx, &pc.ListProductSkuRequest{
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		Where:     where,
		WhereType: where_type,
		IsGroup:   int32(is_group),
		IsExclude: int32(is_exclude),
	})
	if err != nil {
		glog.Errorf("ListProductSku 分页查询sku产品信息异常,err:%+v", err)
		return c.JSON(http.StatusBadRequest, err)
	}
	return c.JSON(int(out.Code), out)
}

// @Summary 根据商品ID查询SKU详情信息
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param id query string true "商品id"
// @Success 200 {object} pc.SkuResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/product/sku [get]
func QuerySku(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	id, _ := strconv.Atoi(c.QueryParam("id"))

	if out, err := client.RPC.QuerySku(client.Ctx, &wrappers.Int32Value{Value: int32(id)}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 新增/编辑分类
// @Tags 商品分类
// @Accept json
// @Produce plain
// @Param category body pc.Category true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/category/new [post]
func NewCategory(c echo.Context) error {
	//fmt.Printf("%v",c.FormValue("categoryarrid"))
	var model pc.Category
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewCategory(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除分类
// @Tags 商品分类
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/category/del [get]
func DelCategory(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelCategory(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分类列表
// @Tags 商品分类
// @Accept plain
// @Produce json
// @Param data_type query string false "数据类别(data_type=category_arr归属数据 data_type=category_sub子数据)"
// @Param name query string false "分类名称"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.CategoryResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/category/list [get]
func QueryCategory(c echo.Context) error {
	name := c.QueryParam("name")
	parent_id := c.QueryParam("parent_id")
	pageIndex, _ := strconv.Atoi(c.QueryParam("page_index"))
	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))
	dataType := c.QueryParam("data_type")

	if parent_id == "" {
		parent_id = "-1"
	}
	parentId, _ := strconv.Atoi(parent_id)

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryCategory(client.Ctx, &pc.CategoryRequest{
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		DataType:  dataType,
		Where: &pc.Category{
			Name:     name,
			ParentId: int32(parentId),
		},
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 分类菜单列表
// @Tags 商品分类
// @Accept plain
// @Produce json
// @Success 200 {object} pc.CategoryNavResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/category/menu [get]
func GetCategoryList(c echo.Context) error {
	//category_id := c.QueryParam("categoryid")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.GetCategoryListTree(client.Ctx, &pc.IdRequest{
		Id: "",
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 新增/编辑ERP
// @Tags ERP
// @Accept json
// @Produce plain
// @Param erp body pc.Erp true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/erp/new [post]
func NewErp(c echo.Context) error {
	var model pc.Erp
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewErp(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除ERP
// @Tags ERP
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/erp/del [get]
func DelErp(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelErp(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 暂停/启用ERP
// @Tags ERP
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Param status query string true "启用/停用状态(enabled/stop)"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/erp/active [get]
func ActiveErp(c echo.Context) error {
	id := c.QueryParam("id")
	status := c.QueryParam("status")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.ActiveErp(client.Ctx, &pc.ActiveErpRequest{
		Id:     id,
		Status: status,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary ERP列表
// @Tags ERP
// @Accept plain
// @Produce json
// @Param name query string false "分类名称"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.ErpResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/erp/list [get]
func QueryErp(c echo.Context) error {
	name := c.QueryParam("name")
	pageIndex, _ := strconv.Atoi(c.QueryParam("page_index"))
	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryErp(client.Ctx, &pc.ErpRequest{
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		Where: &pc.Erp{
			Name: name,
		},
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 新增/编辑规格
// @Tags 商家规格
// @Accept json
// @Produce json
// @Param spec body pc.Spec true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/spec/new [post]
func NewSpec(c echo.Context) error {
	var model pc.Spec
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	name := model.Name
	if len(strings.TrimSpace(name)) == 0 {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "字符不能为空或者空字符"})
	}
	runName := []rune(name)
	if len(runName) > 20 {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "字符串长度不能大于20"})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewSpec(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 规格列表
// @Tags 商家规格
// @Accept plain
// @Produce json
// @Param id query string false "规格ID"
// @Param name query string false "规格名称"
// @Param category_id query string false "快捷定位ID"
// @Param category_name query string false "快捷定位名称"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.SpecResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/spec/list [get]
func QuerySpec(c echo.Context) error {
	id, _ := strconv.Atoi(c.QueryParam("id"))
	name := c.QueryParam("name")
	pageIndex, _ := strconv.Atoi(c.QueryParam("page_index"))
	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QuerySpec(client.Ctx, &pc.SpecRequest{
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		Where: &pc.Spec{
			Id:   int32(id),
			Name: name,
		},
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除规格
// @Tags 商家规格
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/spec/del [get]
func DelSpec(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelSpec(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 新增/编辑规格值
// @Tags 商家规格
// @Accept json
// @Produce json
// @Param specValue body pc.SpecValue true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/specvalue/new [post]
func NewSpecValue(c echo.Context) error {
	var model pc.SpecValue
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewSpecValue(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 规格值列表
// @Tags 商家规格
// @Accept plain
// @Produce json
// @Param id query string false "规格值ID"
// @Param spec_id query string false "规格ID"
// @Param name query string false "规格名称"
// @Param value query string false "规格值"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.SpecValueResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/specvalue/list [get]
func QuerySpecValue(c echo.Context) error {
	id := c.QueryParam("id")

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QuerySpecValue(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return nil
}

// @Summary 删除规格值
// @Tags 商家规格
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/specvalue/del [get]
func DelSpecValue(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelSpecValue(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return nil
}

// @Summary 新增/编辑属性
// @Tags 商家属性
// @Accept json
// @Produce json
// @Param attr body pc.Attr true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/attr/new [post]
func NewAttr(c echo.Context) error {
	var model pc.Attr
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewAttr(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 属性列表
// @Tags 商家属性
// @Accept plain
// @Produce json
// @Param id query string false "属性ID"
// @Param name query string false "属性名称"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.AttrResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/attr/list [get]
func QueryAttr(c echo.Context) error {
	id := c.QueryParam("id")

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryAttr(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除属性
// @Tags 商家属性
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/attr/del [get]
func DelAttr(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelAttr(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 新增/编辑属性值
// @Tags 商家属性
// @Accept json
// @Produce json
// @Param attrValue body pc.AttrValue true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/attrvalue/new [post]
func NewAttrValue(c echo.Context) error {
	var model pc.AttrValue
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewAttrValue(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 属性值列表
// @Tags 商家属性
// @Accept plain
// @Produce json
// @Param id query string false "属性值ID"
// @Param name query string false "属性名称"
// @Param value query string false "属性值"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.AttrValueResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/attrvalue/list [get]
func QueryAttrValue(c echo.Context) error {
	id := c.QueryParam("id")

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryAttrValue(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return nil
}

// @Summary 删除属性值
// @Tags 商家属性
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/attrvalue/del [get]
func DelAttrValue(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelAttrValue(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return nil
}

// @Summary 新增/编辑类型
// @Tags 商品类型
// @Accept json
// @Produce json
// @Param type body pc.Type true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/type/new [post]
func NewType(c echo.Context) error {
	var model pc.Type
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewType(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return nil
}

// @Summary 类型列表
// @Tags 商品类型
// @Accept plain
// @Produce json
// @Param id query string false "类型Id"
// @Param name query string false "类型名称"
// @Param category_id query string false "快捷定位分类Id"
// @Param category_name query string false "快捷定位分类名称"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.TypeResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/type/list [get]
func QueryType(c echo.Context) error {
	id, _ := strconv.Atoi(c.QueryParam("id"))
	name := c.QueryParam("name")
	category_id, _ := strconv.Atoi(c.QueryParam("category_id"))
	pageIndex, _ := strconv.Atoi(c.QueryParam("page_index"))
	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryType(client.Ctx, &pc.TypeRequest{
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		Where: &pc.Type{
			Id:         int32(id),
			Name:       name,
			CategoryId: int32(category_id),
		},
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return nil
}

// @Summary 删除类型
// @Tags 商品类型
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/type/del [get]
func DelType(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelType(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return nil
}

// @Summary 新增/编辑分类导航
// @Tags 分类导航
// @Accept json
// @Produce plain
// @Param erp body pc.CategoryNav true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/category-nav/new [post]
func NewCategoryNav(c echo.Context) error {
	var model pc.CategoryNav
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewCategoryNav(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 获取分类导航
// @Tags 分类导航
// @Accept plain
// @Produce json
// @Param category_id query string true "分类ID"
// @Success 200 {object} pc.CategoryNavResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/category-nav/list [get]
func QueryCategoryNav(c echo.Context) error {
	category_id, _ := strconv.Atoi(c.QueryParam("categoryid"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryCategoryNav(client.Ctx, &pc.CategoryNavRequest{
		Where: &pc.CategoryNav{
			CategoryId: int32(category_id),
		},
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 新增/编辑商品标签
// @Tags 商品标签
// @Accept json
// @Produce plain
// @Param category body pc.Tag true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/tag/new [post]
func NewTag(c echo.Context) error {
	var model pc.Tag
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.NewTag(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除商品标签
// @Tags 商品标签
// @Accept plain
// @Produce plain
// @Param id query string true "ID，多个以英文逗号分隔"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/tag/del [get]
func DelTag(c echo.Context) error {
	id := c.QueryParam("id")
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.DelTag(client.Ctx, &pc.IdRequest{
		Id: id,
	}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 商品标签列表
// @Tags 商品标签
// @Accept plain
// @Produce json
// @Param typename query string false "名称(typename=name) 索引ID(typename=id) 所属分类(typename=category_id)"
// @Param name query string false "搜索内容"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} pc.TagResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/tag/list [get]
func QueryTag(c echo.Context) error {
	typeName := c.QueryParam("typename")
	name := c.QueryParam("name")
	pageIndex, _ := strconv.Atoi(c.QueryParam("page_index"))
	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.QueryTag(client.Ctx, &pc.TagRequest{
		TypeName:  typeName,
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		Where: &pc.Tag{
			Name: name,
		},
	}); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 图片空间相册列表
// @Tags 图片、视频空间
// @Param MediaCLassList body pc.MediaClassListRequest true " "
// @Success 200 {object} pc.MediaClassListResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/media-class/list [post]
func MediaCLassList(c echo.Context) error {
	/*KeywordType, _ := strconv.Atoi(c.FormValue("keyword_type"))
	Keyword := c.FormValue("keyword")
	ApicType, _ := strconv.Atoi(c.FormValue("apic_type"))
	PageIndex, _ := strconv.Atoi(c.FormValue("page_index"))
	PageSize, _ := strconv.Atoi(c.FormValue("pages_size"))*/
	var mediaClass pc.MediaClassListRequest
	if err := c.Bind(&mediaClass); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.MediaClassList(client.Ctx, &mediaClass); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 图片、视频详细列表
// @Tags 图片、视频空间
// @Param MediaItemList body pc.MediaItemListRequest true " "
// @Success 200 {object} pc.MediaItemListResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/media-item/list [post]
func MediaItemList(c echo.Context) error {
	var mediaClass pc.MediaItemListRequest
	if err := c.Bind(&mediaClass); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	//fmt.Println(mediaClass)
	/*MediaType, _ := strconv.Atoi(c.FormValue("media_type"))
	AclassId := c.FormValue("aclass_id")
	PageIndex, _ := strconv.Atoi(c.FormValue("page_index"))
	PageSize, _ := strconv.Atoi(c.FormValue("pages_size"))
	fmt.Println(AclassId)*/
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.MediaItemList(client.Ctx, &mediaClass); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 图片、视频上传
// @Tags 图片、视频空间
// @Description 目前商品中心只需要apic_type file文件参数  其他参数忽略
// @Param MediaUpload body pc.MediaUploadRequest true " "
// @Success 200 {object} pc.MediaUploadResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/media-item/upload [post]
func MediaUpload(c echo.Context) error {
	var mediaClass pc.MediaUploadRequest
	if err := c.Bind(&mediaClass); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}
	/*AclassId, _ := strconv.Atoi(c.FormValue("aclass_id"))
	ApicType, _ := strconv.Atoi(c.FormValue("apic_type"))
	StoreId, _ := strconv.Atoi(c.FormValue("store_id"))
	StoreName := c.FormValue("store_name")
	mediaClass := pc.MediaUploadRequest{
		AclassId:  int32(AclassId),
		ApicType:  int32(ApicType),
		StoreId:   int32(StoreId),
		StoreName: StoreName,
	}*/

	r := c.Request()
	file, header, err := r.FormFile("file") // 获得客户端传来的 文件 file

	if err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	if mediaClass.ApicType == 0 {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "上传类型错误！"})
	}

	bodyBuffer := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuffer)
	fileWriter, _ := bodyWriter.CreateFormFile("file", header.Filename)
	io.Copy(fileWriter, file)                       //将 客户端文件 复制给 用于传输的 fileWriter
	contentType := bodyWriter.FormDataContentType() //contentType
	bodyWriter.Close()
	uploadUrl := config.GetString("file-upload-url")
	resp, _ := http.Post(fmt.Sprintf("%s/fss/up", uploadUrl), contentType, bodyBuffer)
	defer resp.Body.Close()
	resp_body, _ := ioutil.ReadAll(resp.Body)

	var mapResult map[string]interface{}
	if resp.Status == `200 OK` {
		err := json.Unmarshal([]byte(bytes.NewBuffer(resp_body).String()), &mapResult)
		if err != nil {
			return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
		}

		if mapResult["error"] != nil {
			return c.JSON(400, &pc.BaseResponse{Code: 400, Message: mapResult["error"].(string)})
		}
	} else {

		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "上传失败！"})
	}

	mediaClass.ApicPath = mapResult["url"].(string)
	mediaClass.ApicName = mapResult["filename"].(string)
	mediaClass.ApicSize = int32(mapResult["size"].(float64))
	mediaClass.ApicSpec = fmt.Sprintf("%.0fx%.0f", mapResult["width"].(float64), mapResult["height"].(float64))
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if mediaClass.ApicType != 1 && mediaClass.ApicType != 2 {
		mediaClass.AclassId = 1
	}

	if out, err := client.RPC.MediaUpload(client.Ctx, &mediaClass); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 图片、视频上传的信息保存
// @Tags 图片、视频空间
// @Description 针对目前限制媒体文件最大5M,大文件在前端通过fss/up接口上传，相关信息通过此接口保存
// @Param MediaUpload body pc.MediaUploadRequest true " "
// @Success 200 {object} pc.MediaUploadResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/media-item/save [post]
func MediaInfoSave(c echo.Context) error {
	var mediaClass pc.MediaUploadRequest
	if err := c.Bind(&mediaClass); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}
	if mediaClass.ApicType == 0 {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "上传类型错误！"})
	}
	if len(mediaClass.ApicPath) < 1 {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "路径地址必填！"})
	}
	if len(mediaClass.ApicName) < 1 {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "文件名必填！"})
	}
	if mediaClass.ApicSize < 1 {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "文件大小必须大于0"})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if mediaClass.ApicType != 1 && mediaClass.ApicType != 2 {
		mediaClass.AclassId = 1
	}

	if out, err := client.RPC.MediaUpload(client.Ctx, &mediaClass); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 图片、视频删除
// @Tags 图片、视频空间
// @Param MediaUpload body pc.IdRequest true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/media-item/del [post]
func MediaItemDel(c echo.Context) error {
	var ids pc.IdRequest
	if err := c.Bind(&ids); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if ids.Id == "" {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "id不能为空！"})
	}

	if out, err := client.RPC.MediaItemDel(client.Ctx, &ids); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 图片、视频相册删除
// @Tags 图片、视频空间
// @Param MediaUpload body pc.IdRequest true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/media-class/del [post]
func MediaClassDel(c echo.Context) error {
	var ids pc.IdRequest
	if err := c.Bind(&ids); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if ids.Id == "" {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: "id不能为空！"})
	}

	if out, err := client.RPC.MediaClassDel(client.Ctx, &ids); err != nil {
		glog.Error(err)
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 渠道商品批量新建
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param product_id formData string true "商品ID，多个用逗号分隔"
// @Param channel_id formData int true "渠道ID"
// @Param finance_code_list formData string false "上架门店财务编码，多个财务编码用英文逗号分隔"
// @Param is_all_finance formData int false "是否上架全部门店，1是0否，与上架门店财务编码二者填一个"
// @Param finance_code formData string false "当前门店财务编码"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/channel-product/batch-new [post]
func ChannelProductBatchNew(c echo.Context) error {
	baseResponse := &pc.BaseResponse{
		Code: 400,
	}

	channelId := cast.ToInt32(c.FormValue("channel_id"))
	productId := c.FormValue("product_id")
	financeCodeList := c.FormValue("finance_code_list")
	financeCode := c.FormValue("finance_code")
	isAllFinance := cast.ToInt32(c.FormValue("is_all_finance"))
	category := cast.ToInt(c.FormValue("category"))
	//参数合法性验证
	if (isAllFinance == 0 && len(financeCodeList) == 0) || channelId == 0 {
		baseResponse.Message = "参数不能为空"
		return c.JSON(200, baseResponse)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		baseResponse.Message = err.Error()
		return c.JSON(200, baseResponse)
	}
	userNo := userInfo.UserNo
	if len(userNo) == 0 {
		baseResponse.Message = "用户不存在"
		return c.JSON(200, baseResponse)
	}

	if userInfo.FinancialCode != "" {
		financeCodeList = userInfo.FinancialCode
		financeCode = userInfo.FinancialCode
		isAllFinance = 0
	}

	client := GetDcProductClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	/*商品快照插入 Start*/
	in := pc.BatchCreateToAWRequest{
		ChannelId:   channelId,
		ProductId:   productId,
		FinanceCode: financeCodeList,
		IsAll:       isAllFinance,
		UserNo:      userNo,
		Category:    int32(category),
	}
	params_json, _ := json.Marshal(in)
	/*同步至第三方【美团、饿了么】 Start*/
	switch channelId {
	case 1:
		userInfoArry, _ := json.Marshal(userInfo)
		//fmt.Println(string(userInfoArry))
		request_header := string(userInfoArry)

		//组装request
		rpcRequest := pc.CreateBatchTaskRequest{
			TaskContent:      3,
			OperationFileUrl: string(params_json),
			CreateId:         userNo,
			ChannelId:        channelId,
			RequestHeader:    request_header,
			CreateName:       userInfo.UserName,
			CreateMobile:     userInfo.Mobile,
			CreateIp:         c.RealIP(),
			IpLocation:       GetIpAddress(c.RealIP()),
		}
		rpcRequest.ExtendedData = enum.ChannelMapText[channelId] + enum.TaskContentMapText[rpcRequest.TaskContent]

		if out, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
			return c.JSON(400, err)
		} else {
			//fmt.Println("批量创建返回：", out)
			return c.JSON(int(out.Code), out)
		}
	case 2:
		//组装request
		userInfoArry, _ := json.Marshal(userInfo)
		request_header := string(userInfoArry)
		rpcRequest := pc.CreateBatchTaskRequest{
			TaskContent:      13,
			OperationFileUrl: string(params_json),
			CreateId:         userNo,
			ChannelId:        channelId,
			RequestHeader:    request_header,
			CreateName:       userInfo.UserName,
			CreateMobile:     userInfo.Mobile,
			CreateIp:         c.RealIP(),
			IpLocation:       GetIpAddress(c.RealIP()),
		}
		rpcRequest.ExtendedData = enum.ChannelMapText[channelId] + enum.TaskContentMapText[rpcRequest.TaskContent]
		if out, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
			return c.JSON(400, err)
		} else {
			//fmt.Println("批量创建返回：", out)
			return c.JSON(int(out.Code), out)
		}

	case 3:
		//饿了么
		/*商品快照更新【多门店帐号才更新】 Start*/
		/*in := pc.BatchUpdateToAWRequest{
			ChannelId:   channelId,
			ProductId:   productId,
			FinanceCode: financeCodeList,
			IsAll:       isAllFinance,
			UserNo:      userNo,
		}*/

		/*商品快照插入 End*/
		//if _, err := client.RPC.BatchUpdateToAW(client.Ctx, &in); err != nil {
		//glog.Error(err)
		//baseResponse.Message = err.Error()
		//return c.JSON(200, baseResponse)
		//} else {
		if userInfo.FinancialCode != "" {
			baseResponse.Message = "单门店账号无法批量新建"
			return c.JSON(200, baseResponse)
		}
		if result, err := client.RPC.BatchToElm(client.Ctx, &pc.BatchToMTRequest{
			ProductId:       productId,
			ChannelId:       channelId,
			FinanceCodeList: financeCodeList,
			FinanceCode:     financeCode,
			UserNo:          userNo,
			IsAll:           isAllFinance,
			OperateType:     1,
		}); err != nil {
			glog.Error(err)
			baseResponse.Message = err.Error()
			return c.JSON(200, baseResponse)
		} else if result.Code != 200 {
			glog.Error(err)
			baseResponse.Message = result.Message
			return c.JSON(200, baseResponse)
		}
		//}

	}
	/*同步至第三方【美团、饿了么】 End*/

	baseResponse.Code = 200
	return c.JSON(200, baseResponse)
}

// @Summary 渠道商品批量更新
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param product_id formData string true "商品ID，多个用逗号分隔"
// @Param channel_id formData int true "渠道ID"
// @Param finance_code_list formData string false "上架门店财务编码，多个财务编码用英文逗号分隔"
// @Param is_all_finance formData int false "是否上架全部门店，1是0否，与上架门店财务编码二者填一个"
// @Param update_field formData string true "更新的字段，多个用逗号分隔"
// @Param finance_code formData string false "当前门店财务编码"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/channel-product/batch-update [post]
func ChannelProductBatchUpdate(c echo.Context) error {
	baseResponse := &pc.BaseResponse{
		Code: 400,
	}

	channelId := cast.ToInt32(c.FormValue("channel_id"))
	productId := c.FormValue("product_id")
	financeCodeList := c.FormValue("finance_code_list")
	financeCode := c.FormValue("finance_code")
	isAllFinance := cast.ToInt32(c.FormValue("is_all_finance"))
	updateField := c.FormValue("update_field")
	//参数合法性验证
	if (isAllFinance == 0 && len(financeCodeList) == 0) || channelId == 0 || updateField == "" {
		baseResponse.Message = "参数不能为空"
		return c.JSON(200, baseResponse)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		baseResponse.Message = err.Error()
		return c.JSON(200, baseResponse)
	}
	userNo := userInfo.UserNo
	if len(userNo) == 0 {
		baseResponse.Message = "用户不存在"
		return c.JSON(200, baseResponse)
	}

	if userInfo.FinancialCode != "" {
		financeCodeList = userInfo.FinancialCode
		financeCode = userInfo.FinancialCode
		isAllFinance = 0
	}

	client := GetDcProductClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	/*商品快照更新【多门店帐号才更新】 Start*/
	in := pc.BatchUpdateToAWRequest{
		ChannelId:   channelId,
		ProductId:   productId,
		FinanceCode: financeCodeList,
		IsAll:       isAllFinance,
		UserNo:      userNo,
	}

	if financeCode == "" {
		updateFieldList := strings.Split(updateField, ",")
		for _, v := range updateFieldList {
			switch v {
			case "name":
				in.UpdateGoodsName = 1
			case "selling_point":
				in.UpdateSellingPoint = 1
			case "pic":
				in.UpdatePic = 1
			case "video":
				in.UpdateVideo = 1
			case "brand_id":
				in.UpdateBrandId = 1
			case "attr_id":
				in.UpdateAttr = 1
			case "third_sku_id":
				in.UpdateCode = 1
			case "market_price":
				in.UpdateMarketPrice = 1
			case "bar_code":
				in.UpdateRetailPrice = 1
			case "retail_price":
				in.UpdateBarCode = 1
			case "content_pc":
				in.UpdatePicDetails = 1
			case "weight_for_unit":
				in.UpdateHeight = 1
			}
		}

		if _, err := client.RPC.BatchUpdateToAW(client.Ctx, &in); err != nil {
			glog.Error(err)
			baseResponse.Message = err.Error()
			return c.JSON(200, baseResponse)
		}
	}
	/*商品快照更新 End*/

	/*同步至第三方【美团、饿了么】 Start*/
	switch channelId {
	case 2:
		//美团
		inMT := pc.BatchToMTRequest{
			ProductId:          productId,
			FinanceCodeList:    financeCodeList,
			FinanceCode:        financeCode,
			IsAll:              isAllFinance,
			ChannelId:          channelId,
			UserNo:             userNo,
			OperateType:        2,
			UpdateGoodsName:    in.UpdateGoodsName,
			UpdateSellingPoint: in.UpdateSellingPoint,
			UpdatePic:          in.UpdatePic,
			UpdateVideo:        in.UpdateVideo,
			UpdateHeight:       in.UpdateHeight,
			UpdateCode:         in.UpdateCode,
			UpdateMarketPrice:  in.UpdateMarketPrice,
			UpdateBarCode:      in.UpdateBarCode,
			UpdatePicDetails:   in.UpdatePicDetails,
		}
		if result, err := client.RPC.BatchToMT(client.Ctx, &inMT); err != nil {
			glog.Error(err)
			baseResponse.Message = err.Error()
			return c.JSON(200, baseResponse)
		} else if result.Code != 200 {
			glog.Error(err)
			baseResponse.Message = result.Message
			return c.JSON(200, baseResponse)
		}
	case 3:
		//饿了么
		if userInfo.FinancialCode != "" {
			baseResponse.Message = "单门店账号无法批量更新"
			return c.JSON(200, baseResponse)
		}
		inElm := pc.BatchToMTRequest{
			ChannelId:       in.ChannelId,
			ProductId:       productId,
			FinanceCodeList: financeCodeList,
			FinanceCode:     financeCode,
			IsAll:           isAllFinance,
			UserNo:          userNo,
			OperateType:     2,
			UpdateField:     updateField,
		}
		if result, err := client.RPC.BatchToElm(client.Ctx, &inElm); err != nil {
			glog.Error(err)
			baseResponse.Message = err.Error()
			return c.JSON(200, baseResponse)
		} else if result.Code != 200 {
			glog.Error(err)
			baseResponse.Message = result.Message
			return c.JSON(200, baseResponse)
		}
	}
	/*同步至第三方【美团、饿了么】 End*/

	baseResponse.Code = 200
	return c.JSON(200, baseResponse)
}

// @Summary 渠道商品批量上架  针对已经下架的商品进行处理再次上架
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param product_id formData string true "商品ID，多个用逗号分隔"
// @Param channel_id formData int true "渠道ID"
// @Param finance_code_list formData string false "上架门店财务编码，多个财务编码用英文逗号分隔"
// @Param is_all_finance formData int false "是否上架全部门店，1是0否，与上架门店财务编码二者填一个"
// @Param category formData int false "仓库类型 3-门店仓，4-前置仓"
// @Param finance_code formData string false "当前门店财务编码"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/channel-product/batch-up [post]
func ChannelProductBatchUp(c echo.Context) error {
	baseResponse := &pc.BaseResponse{
		Code:    400,
		Message: "失败",
	}
	userNo := ""
	var userInfoStr string
	var userInfo = &models.LoginUserInfo{}
	if userInfo, err := utils.GetPayloadDirectlyToInterface(c); err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	} else {
		userInfoArry, _ := json.Marshal(userInfo)
		userInfoStr = string(userInfoArry)
		userNo = userInfo.UserNo
		//fmt.Println(string(userInfoArry))
	}

	channelId := cast.ToInt32(c.FormValue("channel_id"))
	productId := c.FormValue("product_id")
	financeCodeList := c.FormValue("finance_code_list")
	isAllFinance := cast.ToInt32(c.FormValue("is_all_finance"))
	financeCode := c.FormValue("finance_code")
	category := cast.ToInt32(c.FormValue("category"))
	//参数合法性验证
	if channelId == 0 {
		baseResponse.Message = "渠道参数不能为空"
		return c.JSON(200, baseResponse)
	}
	var ProductIds []string
	if productId != "" {
		ProductIds = strings.Split(productId, ",")
	}

	if userInfo.FinancialCode != "" {
		financeCodeList = userInfo.FinancialCode
		financeCode = userInfo.FinancialCode
		isAllFinance = 0
	}

	client := GetDcProductClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	/*商品快照插入 Start*/
	in := pc.ChannelProductUnsaleRequest{
		ChannelId:       channelId,
		ProductIds:      ProductIds,
		FinanceCodeList: financeCodeList,
		Category:        category,
		UserNo:          userNo,
	}

	params_json, _ := json.Marshal(in)

	//组装request
	rpcRequest := pc.CreateBatchTaskRequest{
		TaskContent:      4,
		OperationFileUrl: string(params_json),
		CreateId:         userNo,
		ChannelId:        channelId,
		RequestHeader:    userInfoStr,
		CreateName:       userInfo.UserName,
		CreateMobile:     userInfo.Mobile,
		CreateIp:         c.RealIP(),
		IpLocation:       GetIpAddress(c.RealIP()),
	}
	rpcRequest.ExtendedData = enum.TaskContentMapText[rpcRequest.TaskContent]
	/*上架【阿闻、美团、饿了么】 Start*/
	switch channelId {
	case 1:
		rpcRequest.ExtendedData = enum.ChannelMapText[channelId] + rpcRequest.ExtendedData
		if out, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
			return c.JSON(400, err)
		} else {
			return c.JSON(int(out.Code), out)
		}
	case 2:
		//美团批量上架
		rpcRequest.ExtendedData = enum.ChannelMapText[channelId] + rpcRequest.ExtendedData
		if _, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
			return c.JSON(400, err)
		} else {
			storeMasterId, err := GetAppChannelByFinanceCode(financeCode)
			if err != nil {
				glog.Error("boss-ChannelProductBatchUp,", "GetAppChannelByFinanceCode,", financeCode)
				baseResponse.Code = 400
				baseResponse.Message = "获取店铺主体信息失败"
				return c.JSON(200, baseResponse)
			}
			inMT := pc.BatchOnTheShelfToMTRequest{
				ProductId:       productId,
				FinanceCodeList: financeCodeList,
				FinanceCode:     financeCode,
				IsAll:           isAllFinance,
				UserNo:          userNo,
				ChannelId:       channelId,
				Category:        category,
				StoreMasterId:   storeMasterId,
			}
			if result, err := client.RPC.BatchOnTheShelfToMT(client.Ctx, &inMT); err != nil {
				glog.Error(err)
				baseResponse.Message = err.Error()
			} else if result.Code != 200 {
				glog.Error(err)
			}
		}
		baseResponse.Code = 200
		baseResponse.Message = "创建任务成功"
		return c.JSON(200, baseResponse)

	case 3:
		//饿了么
		if userInfo.FinancialCode != "" {
			baseResponse.Message = "单门店账号无法批量上架"
			return c.JSON(200, baseResponse)
		}
		rpcRequest.ExtendedData = enum.ChannelMapText[channelId] + rpcRequest.ExtendedData
		if _, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
			return c.JSON(400, err)
		} else {
			inElm := pc.BatchOnTheShelfToMTRequest{
				ProductId:       productId,
				FinanceCodeList: financeCodeList,
				FinanceCode:     financeCode,
				IsAll:           isAllFinance,
				UserNo:          userNo,
				ChannelId:       channelId,
				OperateType:     0,
			}
			if result, err := client.RPC.BatchOnTheShelfToElm(client.Ctx, &inElm); err != nil {
				glog.Error(err)
				baseResponse.Message = err.Error()
				return c.JSON(200, baseResponse)
			} else if result.Code != 200 {
				glog.Error(err)
				baseResponse.Message = result.Message
				return c.JSON(200, baseResponse)
			}
		}

		//门店上下架成功商品写入数据库
		if out, err := client.RPC.BatchOnTheShelfToAW(client.Ctx, &pc.BatchOnTheShelfToAWRequest{ProductId: productId, ChannelId: channelId, FinanceCode: financeCode, IsAll: 0, UserNo: userNo, UpDownState: 1}); err != nil {
			glog.Error(err)
			baseResponse.Message = err.Error()
			return c.JSON(200, baseResponse)
		} else if out.Code != 200 {
			baseResponse.Message = out.Message
			return c.JSON(200, baseResponse)
		}
	case 4:
		//京东批量上架
		//var params pc.CreateBatchTaskRequest
		//params.ChannelId = ChannelJddjId
		//params.TaskContent = 4
		//params.CreateId = userNo

		params_str := &pc.BatchOnTheShelfToJddjRequest{
			ProductId:       productId,
			FinanceCodeList: financeCodeList,
			FinanceCode:     financeCode,
			IsAll:           isAllFinance,
			UserNo:          userNo,
			ChannelId:       channelId,
			UpDownState:     1,
			Category:        category,
		}

		params_OperationFileUrl, err := json.Marshal(params_str)
		if err != nil {
			glog.Error("批量上架京东渠道反序列化OperationFileUrl,err:", err)
		}
		rpcRequest.OperationFileUrl = string(params_OperationFileUrl)
		//params.OperationFileUrl = string(params_OperationFileUrl)

		rpcRequest.RequestHeader = userInfoStr
		rpcRequest.ExtendedData = enum.ChannelMapText[channelId] + rpcRequest.ExtendedData
		res, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest)
		if err != nil {
			glog.Error("CreateBatchTask,err:", err)
			baseResponse.Code = 400
			baseResponse.Message = "操作失败，请检查重试"
			return c.JSON(400, baseResponse)
		}
		if res.Code == 200 {
			baseResponse.Code = 200
			baseResponse.Message = "操作已成功提交，请在“批量任务查看”中查看相关结果"
			return c.JSON(200, baseResponse)
		}
	}
	/*上架【阿闻、美团、饿了么】 End*/

	baseResponse.Code = 200
	baseResponse.Message = "批量上架成功"
	return c.JSON(200, baseResponse)
}

// @Summary 平台商品库--新增异步任务数据接口
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param task_content formData int true "任务内容:1:批量新建;2:批量更新;3：渠道-批量新建 4：渠道-批量上架 5:批量认领 6:认领全部商品 7:批量认领或全部认领 8:药品全部下架 9:药品恢复上架 10:药品相关任务 11:渠道--商品导出 12:渠道--批量更新 13批量同步 15批量脚本 16 商品分类同步 17 全量同步分类到指定门店 18 商品从渠道下架处理 19 批量导入前置仓价格信息 20 实物平台商品导出 21 虚拟平台商品导出 35:互联网医院商品价格(批量导入商品价格)"
// @Param operation_file_url formData string true "操作文件路径"
// @Param create_id formData string true "创建人id"
// @Param update_type formData int true "批量更新类型 1通过SKU匹配更新   2通过条形码匹配更新  3通过商品货号匹配更新"
// @Success 200 {object} pc.CreateBatchTaskResponse
// @Failure 400 {object} pc.CreateBatchTaskResponse
// @Router /boss/product/CreateBatchTask [post]
func CreateBatchTask(c echo.Context) error {
	var model models.CreateBatchTaskParams
	//此处保留校验扩展
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.CreateBatchTaskResponse{Code: 400, Message: err.Error(), Error: err.Error()})
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return c.JSON(400, &pc.CreateBatchTaskResponse{Code: 400, Message: err.Error(), Error: err.Error()})
	}

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var OperationFileUrlJson []byte
	var OperationFileUrl string
	operationFileUrl := make(map[string]interface{})
	operationFileUrl["url"] = model.OperationFileUrl
	if model.TaskContent == 2 {
		operationFileUrl["update_type"] = model.UpdateType
		OperationFileUrlJson, _ = json.Marshal(operationFileUrl)
		OperationFileUrl = string(OperationFileUrlJson)
	} else {
		OperationFileUrl = model.OperationFileUrl
	}

	//组装request
	rpcRequest := pc.CreateBatchTaskRequest{
		TaskContent:      model.TaskContent,
		OperationFileUrl: OperationFileUrl,
		CreateId:         model.CreateId,
		CreateName:       userInfo.UserName,
		CreateMobile:     userInfo.Mobile,
		CreateIp:         c.RealIP(),
		IpLocation:       GetIpAddress(c.RealIP()),
	}
	rpcRequest.ExtendedData = enum.TaskContentMapText[rpcRequest.TaskContent]

	if out, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 平台商品库--删除异步任务数据接口
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param task_id formData int true "任务id"
// @Param modifyId_id formData string true "修改人id"
// @Success 200 {object} pc.DeleteTaskResponse
// @Failure 400 {object} pc.DeleteTaskResponse
// @Router /product/product/DeleteTask [post]
func DeleteTask(c echo.Context) error {
	var model models.DeleteTaskParams
	//此处保留校验扩展
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.DeleteTaskResponse{Code: 400, Message: err.Error(), Error: err.Error()})
	}
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	//组装request
	rpcRequest := pc.DeleteTaskRequest{
		TaskId: model.TaskId,
		//修改id
		ModifyId: model.ModifyId,
	}
	if out, err := client.RPC.DeleteTask(client.Ctx, &rpcRequest); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary v6.6.0重新执行任务操作接口
// @Tags v6.6.0 商品库
// @Accept plain
// @Produce plain
// @Param task_id query string true "重新执行的任务id"
// @Param task_content query string true "重新执行的任务类型 40：移动商品任务，22：默认同步第三方分类任务"
// @Success 200 {object} pc.DeleteTaskResponse
// @Failure 400 {object} pc.DeleteTaskResponse
// @Router /boss/product/product/RollBackTask [get]
func RollBackTask(c echo.Context) error {

	taskId := cast.ToInt32(c.QueryParam("task_id"))
	taskContent := cast.ToInt32(c.QueryParam("task_content"))

	glog.Info("重新执行任务id: ", taskId)
	response := models.BaseResponse{Msg: "successful", Code: 200}

	if taskId <= 0 {
		response.Msg = "任务参数异常"
		response.Code = 400
		return c.JSON(http.StatusBadRequest, response)
	}
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	//组装request
	rpcRequest := pc.DeleteTaskRequest{
		TaskId:      taskId,
		TaskContent: taskContent,
	}

	client.Ctx = AppendToOutgoingContext(client.Ctx, c)
	if _, err := client.RPC.RollBackTask(client.Ctx, &rpcRequest); err != nil {
		response.Msg = err.Error()
		response.Code = 400
		return c.JSON(http.StatusBadRequest, response)
	} else {
		return c.JSON(http.StatusOK, response)
	}

}

// @Summary v6.4.0 平台商品库--异步任务列表接口(只展示当天的异步任务信息)
// @Tags v6.4.0 商品库
// @Accept plain
// @Produce plain
// @Param page query int false "当前页，默认第1页"
// @Param pageSize query int false "每页显示数据条数，默认显示10条"
// @Param sort query string false "排序类型:createTimeDesc：按创建时间顺序倒序；"
// @Param task_status query int false "任务状态:1:调度中;2:进行中;3:已完成；"
// @Param task_content query int false "任务内容:1:批量新建;2:批量更新;3：渠道-批量新建 4：渠道-批量上架 5:批量认领 6:认领全部商品 7:批量认领或全部认领 8:药品全部下架 9:药品恢复上架 10:药品相关任务 11:渠道--商品导出 12:渠道--批量更新 13批量同步 15批量脚本 16 商品分类同步 17 全量同步分类到指定门店 18 商品从渠道下架处理 19 批量导入前置仓价格信息 20 实物平台商品导出 21 虚拟平台商品导出, 22 同步分类任务查询 ,31:渠道门店分类同步任务查询 151 平台--商品库页面 152 管家--商品库"
// @Param status query int false "状态:1:正常;2:冻结;"
// @Param promoter query int false "0:自己;1:全部;2:其他;"
// @Success 200 {object} pc.GetTaskListResponse
// @Failure 400 {object} pc.GetTaskListResponse
// @Router /boss/product/product/GetTaskList [get]
func GetTaskList(c echo.Context) error {
	//接收参数
	page := c.QueryParam("page")
	pageSize := c.QueryParam("pageSize")
	sort := c.QueryParam("sort")                //排序类型:createTimeDesc：按创建时间顺序倒序；
	taskStatus := c.QueryParam("task_status")   //任务状态:1:调度中;2:进行中;3:已完成；
	taskContent := c.QueryParam("task_content") //任务内容:1:平台-批量新建; 2:平台-批量更新 3：渠道-批量新建 4：渠道-批量上架 5:批量认领 6:认领全部商品 7:批量认领或全部认领 8:药品全部下架 9:药品恢复上架 10:药品相关任务"
	status := c.QueryParam("status")            //状态:1:正常;2:冻结;
	promoter := cast.ToInt32(c.QueryParam("promoter"))

	channelId := cast.ToInt32(c.Request().Header.Get("channel_id"))

	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	//组装request
	rpcRequest := pc.GetTaskListRequest{
		Createtime: time.Now().Format("2006-01-02"),
	}
	//互联网医院商品价价格 仓库白名单不分渠道
	if cast.ToInt32(taskContent) == 35 || cast.ToInt32(taskContent) == 37 {
		channelId = 0
	}

	rpcRequest.ChannelId = channelId
	rpcRequest.Page = cast.ToInt32(page)
	if rpcRequest.Page == 0 {
		rpcRequest.Page = 1
	}
	rpcRequest.PageSize = cast.ToInt32(pageSize)
	if rpcRequest.PageSize == 0 {
		rpcRequest.PageSize = 10
	}
	if sort != "" {
		rpcRequest.Sort = sort
	}
	rpcRequest.TaskStatus = cast.ToInt32(taskStatus)
	rpcRequest.TaskContent = cast.ToInt32(taskContent)
	rpcRequest.Status = cast.ToInt32(status)
	rpcRequest.Promoter = promoter

	err := errors.New("")
	var userInfo models.LoginUserInfo
	if userInfo, err = utils.GetPayloadDirectlyToInterface(c); err != nil {
		glog.Error(err)
		return c.JSON(200, &pc.ChannelStoreProductResponse{Code: 400, Error: err.Error()})
	}

	rpcRequest.CreateId = userInfo.UserNo
	//部分任务查询不需要传createdID
	if taskContent == "10" || taskContent == "15" {
		rpcRequest.CreateId = ""
		rpcRequest.ChannelId = -1

	}
	if taskContent == "19" {
		rpcRequest.ChannelId = -1
	}
	if taskContent == "5" {
		rpcRequest.ChannelId = 1
	}
	if taskContent == "2240" { // 不需要channel_id
		rpcRequest.ChannelId = -1
		rpcRequest.Createtime = "" // 展示全部的内容
	}
	// 切仓展示所有任务记录
	if taskContent == "67" || taskContent == "15" || taskContent == "31" {
		rpcRequest.Createtime = ""
	}

	if out, err := client.RPC.GetTaskList(client.Ctx, &rpcRequest); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

//导出渠道门店商品
/*func exportChannelStoreProduct(channelID int32, productIds []int32) string {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if res, err := client.RPC.QueryChannelStoreProduct(client.Ctx, &pc.ChannelStoreProductRequest{ChannelId: channelID, ProductId: productIds, PageIndex: 1, PageSize: 9999, UpDownState: -1}); err != nil || res.Code != 200 {
		glog.Error(err)
		return err.Error()
	} else {
		if res2, err := client.RPC.QueryChannelProductOnly(client.Ctx, &pc.OneofIdRequest{ChannelId: int32(channelID), Id: &pc.OneofIdRequest_ProductId{&pc.ArrayIntValue{Value: productIds}}}); err != nil || res2.Code != 200 {
			glog.Error(err)
			return err.Error()
		} else {
			var out models.ChannelStoreProductResponse
			out.TotalCount = res.TotalCount
			for _, v := range res.Details {
				var csp models.ChannelStoreProduct
				for _, v2 := range res2.Details {
					if v.ProductId == v2.Id {
						csp.ChannelStoreProduct = v

						//取快照，显示用户更改过的信息
						arrSnapshot := getChannelProductSnapshot([]int32{v.ProductId}, channelID, c)
						csp.ChannelProduct = arrSnapshot[0].Product
						break
					}
				}
				for _, v2 := range outStoreInfo.Details {
					if v.FinanceCode == v2.FinanceCode {
						csp.StoreInfo = v2
						break
					}
				}
				out.Details = append(out.Details, &csp)
			}

			//导出
			if pageSize == 9999 {
				var channelName string
				switch channelID {
				case 1:
					channelName = "阿闻"
					break
				case 2:
					channelName = "美团"
					break
				case 3:
					channelName = "饿了么"
					break
				}

				f := excelize.NewFile()
				header := []string{"门店ID", "门店名称", "商品名称", "商品类目", "店内商品分类", "渠道", "状态"}
				for i := 0; i < len(header); i++ {
					f.SetCellValue("Sheet1", string(65+i)+"1", header[i])
				}
				for i := 0; i < len(out.Details); i++ {
					v := out.Details[i]
					f.SetCellValue("Sheet1", "A"+strconv.Itoa(i+2), v.StoreInfo.FinanceCode)
					f.SetCellValue("Sheet1", "B"+strconv.Itoa(i+2), v.StoreInfo.Name)
					f.SetCellValue("Sheet1", "C"+strconv.Itoa(i+2), v.ChannelProduct.Name)
					f.SetCellValue("Sheet1", "D"+strconv.Itoa(i+2), v.ChannelProduct.CategoryName)
					f.SetCellValue("Sheet1", "E"+strconv.Itoa(i+2), v.ChannelProduct.ChannelCategoryName)
					f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), channelName)
					if v.ChannelStoreProduct.UpDownState == 0 {
						f.SetCellValue("Sheet1", "G"+strconv.Itoa(i+2), "下架")
					} else {
						f.SetCellValue("Sheet1", "G"+strconv.Itoa(i+2), "上架")
					}
				}
				f.Save()
				var buff bytes.Buffer
				if err = f.Write(&buff); err != nil {
					return c.JSON(200, &models.ChannelStoreProductResponse{Code: 400, Message: "导出文件失败"})
				}
				fileName := "store-product -" + time.Now().Format("20060102150405") + ".xlsx"
				c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
				return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
			}

			out.Code = 200
			return c.JSON(200, out)
		}
	}
}*/

// @Summary 导出所有商品
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param model query pc.QueryProductRequest true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/all/export [get]
func ExportAllProduct(c echo.Context) error {
	var out pc.BaseResponse
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var userInfo = models.LoginUserInfo{}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	}

	req := &pc.QueryProductRequest{
		Where:            c.QueryParam("where"),
		WhereType:        c.QueryParam("where_type"),
		CategoryId:       cast.ToInt32(c.QueryParam("category_id")),
		BrandId:          cast.ToInt32(c.QueryParam("brand_id")),
		IsGroup:          cast.ToInt32(c.QueryParam("is_group")),
		ProductType:      cast.ToInt32(c.QueryParam("product_type")),
		IsDel:            cast.ToInt32(c.QueryParam("is_del")),
		IsGj:             cast.ToInt32(c.QueryParam("is_gj")),
		IsDrugs:          cast.ToInt32(c.QueryParam("is_drugs")),
		UseRange:         cast.ToInt32(c.QueryParam("use_range")),
		IsExclude:        cast.ToInt32(c.QueryParam("is_exclude")),
		SelectType:       cast.ToInt32(c.QueryParam("select_type")),
		SourceType:       cast.ToInt32(c.QueryParam("source_type")),
		IsPrescribedDrug: cast.ToInt32(c.QueryParam("is_prescribed_drug")),
	}

	reqJson, _ := json.Marshal(req)

	userTask, err := client.RPC.GetUserUnFinishedTask(client.Ctx, &pc.GetUserUnFinishedTaskRequest{
		UserNo:        userInfo.UserNo,
		ChannelId:     0,
		TaskContent:   20,
		OperationData: string(reqJson),
	})
	if err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	}
	if userTask.Id > 0 {
		return c.JSON(400, models.BaseResponse{Code: 400, Msg: "用户存在进行中的任务"})
	}

	rpcRequest := pc.CreateBatchTaskRequest{
		OperationFileUrl: string(reqJson),
		CreateId:         userInfo.UserNo,
		ChannelId:        0,
		CreateName:       userInfo.UserName,
		CreateMobile:     userInfo.Mobile,
		CreateIp:         c.RealIP(),
		IpLocation:       GetIpAddress(c.RealIP()),
	}
	rpcRequest.ExtendedData = enum.TaskContentMapText[rpcRequest.TaskContent]

	productType := cast.ToInt32(c.QueryParam("product_type"))
	if productType == 1 {
		rpcRequest.TaskContent = enum.TaskContentGoodsExport
		rpcRequest.ExtendedData = enum.TaskContentMapText[enum.TaskContentGoodsExport]
	} else if productType == 2 {
		rpcRequest.TaskContent = enum.TaskContentVirtualGoodsExport
		rpcRequest.ExtendedData = enum.TaskContentMapText[enum.TaskContentVirtualGoodsExport]
	}
	if _, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
		return c.JSON(400, err)
	}
	out.Code = 200
	out.Message = "操作已成功提交，请在“批量任务查看”中查看相关结果"

	return c.JSON(http.StatusOK, &out)
}

// @Summary 批量更新时需要更新的字段
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param channel_id query int true "渠道ID"
// @Router /boss/product/get-update-field [get]
func GetUpdateFieldByChannelId(c echo.Context) error {
	channelId := c.QueryParam("channel_id")
	type Response struct {
		Code    int32                `json:"code"`
		Message string               `json:"message"`
		Data    []*map[string]string `json:"data"`
	}
	const (
		AWEN    = "1"
		MT      = "2"
		ELEMENT = "3"
	)
	out := &Response{}
	data := map[string][]*map[string]string{
		AWEN:    {{"key": "name", "label": "商品名称"}, {"key": "selling_point", "label": "商品卖点"}, {"key": "pic", "label": "商品图片"}, {"key": "video", "label": "商品视频"}, {"key": "weight_for_unit", "label": "重量"}, {"key": "third_sku_id", "label": "货号"}, {"key": "market_price", "label": "市场价"}, {"key": "bar_code", "label": "商品条码"}, {"key": "content_pc", "label": "图片详情"}},
		MT:      {{"key": "name", "label": "商品名称"}, {"key": "selling_point", "label": "商品卖点"}, {"key": "pic", "label": "商品图片"}, {"key": "video", "label": "商品视频"}, {"key": "brand_id", "label": "商品品牌"}, {"key": "attr_value_id", "label": "商品属性"}, {"key": "third_sku_id", "label": "货号"}, {"key": "market_price", "label": "市场价"}, {"key": "retail_price", "label": "建议零售价"}, {"key": "bar_code", "label": "商品条码"}, {"key": "content_pc", "label": "图片详情"}},
		ELEMENT: {{"key": "name", "label": "商品名称"}, {"key": "selling_point", "label": "商品卖点"}, {"key": "pic", "label": "商品图片"}, {"key": "weight_for_unit", "label": "重量"}, {"key": "market_price", "label": "市场价"}, {"key": "third_sku_id", "label": "货号"}, {"key": "bar_code", "label": "商品条码"}, {"key": "content_pc", "label": "图片详情"}},
	}
	if channelId == "" || data[channelId] == nil {
		out.Code = 400
		out.Message = "无效的channel_id"
	} else {
		out.Code = 200
		out.Message = "请求成功"
		out.Data = data[channelId]
	}
	return c.JSON(http.StatusOK, out)
}

// 商品关联
// @Summary 商品关联列表
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param name query string false "商品名称"
// @Param type query string false "查询方式"
// @Success 200 {object} sh.SkuRelResponse
// @Failure 400 {object} sh.SkuRelResponse
// @Router /boss/product/sku-rel-list [post]
func SkuRelList(c echo.Context) error {
	out := sh.SkuRelResponse{Code: 400}
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()

	name := c.QueryParam("name")
	queryType := c.QueryParam("type")

	res, err := client.RPC.SkuRelList(client.Ctx, &sh.SkuRelRequest{
		Name: name,
		Type: queryType,
	})
	if err != nil {
		out.Message = err.Error()
		out.Error = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(int(out.Code), res)
	}
}

// @Summary 关联名称列表
// @Tags 电商商品
// @Param ListGoodsRelevance body sh.SkuRelListRequest true " "
// @Success 200 {object} sh.SkuRelListResponse
// @Router /boss/product/product/getRelationList [post]
func ListGoodsRelevance(c echo.Context) error {
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	page := cast.ToInt32(c.FormValue("page_index"))
	page_size := cast.ToInt32(c.FormValue("page_size"))
	keyword_type := cast.ToInt32(c.FormValue("keyword_type"))
	keyword := c.FormValue("keyword")
	store_id := cast.ToInt32(c.FormValue("store_id"))

	out := sh.BaseResponse{Code: 400}
	res, err := client.RPC.GoodsRelevanceList(client.Ctx, &sh.SkuRelListRequest{
		PageIndex:   page,
		PageSize:    page_size,
		KeywordType: keyword_type,
		Keyword:     keyword,
		StoreId:     store_id,
	})
	if err != nil {
		glog.Error("关联名称列表错误：", err.Error())
		out.Message = err.Error()
		return c.JSON(400, out)
	}
	if res.Code != 200 {
		out.Message = res.Message
		return c.JSON(400, out)
	}
	return c.JSON(200, res)
}

// @Summary 添加关联名称
// @Tags 电商商品
// @Param NewGoodsRelevance body sh.GoodsRelevanceRequest true " "
// @Success 200 {object} sh.BaseResponse
// @Router /boss/product/product/addGoodsRelation [post]
func NewGoodsRelevance(c echo.Context) error {
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()
	out := sh.BaseResponse{Code: 400}
	name := c.FormValue("name")
	if name == "" {
		out.Message = "名称不能为空"
		return c.JSON(400, out)
	}
	id := c.FormValue("id")
	//ApicType, _ := strconv.Atoi(c.FormValue("apic_type"))
	desc := c.FormValue("desc")
	if desc == "" {
		out.Message = "规格文字不能为空"
		return c.JSON(400, out)
	}

	//timeUnix := time.Now().Unix()

	data := sh.GoodsRelevanceRequest{
		Id:   cast.ToInt32(id),
		Name: name,
		Desc: desc,
	}

	if res, err := client.RPC.NewGoodsRelevance(client.Ctx, &data); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(int(res.Code), res)
	}
}

// @Summary 删除关联名称
// @Tags 电商商品
// @Param DeleteGoodsRelevance body sh.GoodsRelevanceRequest true " "
// @Success 200 {object} sh.BaseResponse
// @Router /boss/product/product/deleteGoodsRelation [post]
func DeleteGoodsRelevance(c echo.Context) error {
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()

	id := c.FormValue("id")

	data := sh.GoodsRelevanceDeleteRequest{
		Id: id,
	}

	if out, err := client.RPC.DeleteGoodsRelevance(client.Ctx, &data); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除关联商品
// @Tags 电商商品
// @Param DeleteGoodsRelevanceSku body sh.GoodsRevanceSkuRequest true " "
// @Success 200 {object} sh.BaseResponse
// @Router /boss/product/product/deleteGoodsRelationSku [post]
func DeleteGoodsRelevanceSku(c echo.Context) error {
	client := GetUpetCenter()
	defer client.Conn.Close()
	defer client.Cf()

	id := c.FormValue("id")

	data := sh.GoodsRevanceSkuRequest{
		Id: cast.ToInt32(id),
	}
	if out, err := client.RPC.DeleteGoodsRelevanceSku(client.Ctx, &data); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 电商商品变动通知（新增、编辑、删除、上下架等所有变动都需要通知）
// @Tags v3.2.3
// @Accept plain
// @Produce json
// @Param model body sh.GoodsToEsUpdateRequest true " "
// @Success 200 {object} models.BaseResponseV2
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/product/bbc/change-notify [post]
func BbcSkuChangeNotify(c echo.Context) error {
	req := new(sh.GoodsToEsUpdateRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &models.BaseResponseV2{Code: 400, Message: err.Error()})
	}
	// 获取grpc链接
	client := sh.GetUpetCenterClient()
	if out, err := client.PS.UpdateGoodsToEs(client.Ctx, req); err != nil {
		return c.JSON(400, &models.BaseResponseV2{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 根据财务编码查询该门店下的所有商品价格并新增到同步（查询北京接口）
// @Tags sync_price
// @Accept plain
// @Produce json
// @Param finance_code query string false "财务编码"
// @Success 200 {object} models.BaseResponseV2
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/product/product/searchAndInsertSyncPrice [post]
func SearchAndInsertSyncPrice(c echo.Context) error {
	var out_model models.BaseResponseV2
	out_model.Code = 400
	finance_code := c.FormValue("finance_code")
	if len(finance_code) == 0 {
		out_model.Message = "财务编码不能为空"
		return c.JSON(400, out_model)
	}
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()
	var params pc.GetProductPriceByBJRequest
	params.StructCode = append(params.StructCode, finance_code)
	if out, err := client.RPC.GetProductPriceByBJ(client.Ctx, &params); err != nil {
		glog.Error("查询北京价格数据失败，err:", err)
		return c.JSON(400, "查询北京价格数据失败，err:"+err.Error())
	} else {
		if len(out.Data) == 0 {
			out_model.Code = 200
			out_model.Message = "查无数据处理"
			return c.JSON(200, out_model)
		}

		//获取到数据
		var params_add pc.AddProductRequest
		params_json, err := json.Marshal(out.Data)
		if err != nil {
			glog.Error("SearchAndInsertSyncPrice解析参数失败，err:", err)
			out_model.Error = err.Error()
			out_model.Message = err.Error()
			out_model.Code = 400
			return c.JSON(400, out_model)
		}

		params_add.Response = string(params_json)
		res, err := client.RPC.AddProductPrice(client.Ctx, &params_add)
		if err != nil {
			glog.Error("SearchAndInsertSyncPrice的AddProductPrice请求失败,err:", err)
			out_model.Error = err.Error()
			out_model.Message = err.Error()
			out_model.Code = 400
			return c.JSON(400, out_model)
		}
		if res.Code != 200 {
			out_model.Code = 400
			out_model.Message = res.Message
			return c.JSON(400, out_model)
		}
		out_model.Code = res.Code
		return c.JSON(200, out_model)
	}
}

// @Summary 根据财务编码查询该门店下的所有商品价格并新增到同步（查询北京接口）
// @Tags sync_price
// @Accept plain
// @Produce json
// @Param finance_code query string false "财务编码"
// @Success 200 {object} models.BaseResponseV2
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/noAuth/store/AllStoreSyncPrice [post]
func AllStoreSyncPrice(c echo.Context) error {
	var out_model models.BaseResponseV2
	out_model.Code = 400
	//finance_code := c.FormValue("finance_code")
	//if len(finance_code) == 0 {
	//	out_model.Message = "财务编码不能为空"
	//	return c.JSON(400, out_model)
	//}
	//先查询出所有子龙门店信息
	db := GetDatacenterDBConn()
	var finance_codes = make([]string, 0)
	err := db.Table("datacenter.store").Alias("a").Join("INNER", "datacenter.store_business_setup b", "a.finance_code=b.finance_code").
		Where("a.org_id=1 and a.app_channel=1 and b.business_status=1").Select("a.finance_code").Find(&finance_codes)
	if err != nil {
		glog.Error("批量处理门店商品价格，err:", err)
		out_model.Error = err.Error()
		out_model.Message = err.Error()
		out_model.Code = 400
		return c.JSON(400, out_model)
	}

	batchSize := 20
	totalCodes := len(finance_codes)
	for i := 0; i < totalCodes; i += batchSize {
		// Create a temporary slice to hold the current batch
		end := i + batchSize
		if end > totalCodes {
			end = totalCodes
		}
		batchCodes := finance_codes[i:end]
		var params pc.GetProductPriceByBJRequest
		params.StructCode = append(params.StructCode, batchCodes...)
		client := GetDcProductClient()

		if out, err := client.RPC.GetProductPriceByBJ(client.Ctx, &params); err != nil {
			glog.Error("查询北京价格数据失败，err:", err)
			//return c.JSON(400, "查询北京价格数据失败，err:"+err.Error())
			continue
		} else {
			glog.Info("批量处理门店商品价格", batchCodes)
			if len(out.Data) == 0 {
				out_model.Code = 200
				out_model.Message = "查无数据处理"
				//return c.JSON(200, out_model)
				continue
			}

			//获取到数据
			var params_add pc.AddProductRequest
			params_json, err := json.Marshal(out.Data)
			if err != nil {
				continue
				glog.Error("批量处理门店商品价格，err:", err)
				out_model.Error = err.Error()
				out_model.Message = err.Error()
				out_model.Code = 400
				//return c.JSON(400, out_model)
			}

			params_add.Response = string(params_json)
			res, err := client.RPC.AddProductPrice(client.Ctx, &params_add)
			if err != nil {

				glog.Error("批量处理门店商品价格,err:", err)
				out_model.Error = err.Error()
				out_model.Message = err.Error()
				out_model.Code = 400
				//return c.JSON(400, out_model)
				continue
			}
			if res.Code != 200 {
				out_model.Code = 400
				out_model.Message = res.Message
				//	return c.JSON(400, out_model)
				glog.Error("批量处理门店商品价格,err:", res.Message)
				continue
			}
		}
		client.Conn.Close()
		client.Cf()

	}
	out_model.Code = 200
	return c.JSON(200, out_model)
}

// 过期的虚拟商品自动下架任务（定时任务）
func TestExpireProductDown(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.RPC.TestExpireProductDown(client.Ctx, &pc.ProductTagsRequest{}); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary v6.6.0 移动商品接口
// @Tags v6.6.0 商品库
// @Accept plain
// @Produce plain
// @Param old_category_id query string true "老的分类id"
// @Param new_category_id query string true "新的分类id"
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /boss/product/category/move-product [get]
func MoveProduct(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	oldCatrgory := cast.ToInt32(c.QueryParam("old_category_id"))
	NewCatrgory := cast.ToInt32(c.QueryParam("new_category_id"))

	if oldCatrgory <= 0 || NewCatrgory <= 0 {
		return c.JSON(400, models.BaseResponse{Code: 400, Msg: "选中的分类id不能为空"})
	}
	var userInfo = models.LoginUserInfo{}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Msg: err.Error()})
	}

	rpcRequest := pc.MoveProductVo{
		OldCategoryId: oldCatrgory,
		NewCategoryId: NewCatrgory,
		CreateId:      userInfo.UserNo,
		CreateName:    userInfo.UserName,
		CreateMobile:  userInfo.Mobile,
		CreateIp:      c.RealIP(),
		IpLocation:    GetIpAddress(c.RealIP()),
	}

	if out, err := client.RPC.MoveCategoryProduct(client.Ctx, &rpcRequest); err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Msg: err.Error()})
	} else {
		return c.JSON(200, out)
	}

}

// @Summary v6.6.7.1 删除渠道商品
// @Tags v6.6.7.1 商品库
// @Accept json
// @Produce plain
// @Param vo  body  dto.DeleteChannelProduct true "参数"
// @Success 200 {object} dto.CommonPageHttpResponse
// @Failure 400 {object} dto.CommonPageHttpResponse
// @Router /boss/product/channel-product/delete [post]
func DeleteChannelProduct(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var res = dto.CommonPageHttpResponse{}

	vo := dto.DeleteChannelProduct{}
	if err := c.Bind(&vo); err != nil {
		res.Message = "参数绑定异常"
		return c.JSON(400, res)
	}

	if vo.ProductId <= 0 {
		res.Message = "请选择商品id"
		return c.JSON(400, res)
	}
	if vo.ChannelId <= 0 {
		res.Message = "请选择渠道id"
		return c.JSON(400, res)
	}
	if vo.ErpId <= 0 {
		res.Message = "货号来源erp_id不能为空"
		return c.JSON(400, res)
	}

	if len(vo.ThirdSkuId) <= 0 {
		res.Message = "第三方货号不能为空"
		return c.JSON(400, res)
	}

	var userInfo = models.LoginUserInfo{}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Message = err.Error()
		return c.JSON(400, res)
	}

	rpcRequest := pc.DeleteProductVo{
		ProductId:    int32(vo.ProductId),
		CreateId:     userInfo.UserNo,
		CreateName:   userInfo.UserName,
		CreateMobile: userInfo.Mobile,
		CreateIp:     c.RealIP(),
		ChannelId:    int32(vo.ChannelId),
		IpLocation:   GetIpAddress(c.RealIP()),
		ErpId:        int32(vo.ErpId),
		ThirdSkuId:   vo.ThirdSkuId,
	}

	out, err := client.RPC.DeleteChannelProduct(client.Ctx, &rpcRequest)
	if err != nil {
		res.Message = err.Error()
		return c.JSON(400, res)
	}

	if out.Code != 200 {
		res.Message = out.Message
		return c.JSON(400, res)
	}

	return c.JSON(200, res)

}

// @Summary v6.6.7.1 渠道商品库日志信息查询
// @Tags v6.6.7.1 商品库
// @Accept plain
// @Produce plain
// @Param channel_id query string false "渠道id"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param where query string false "查询条件sku_id,product_name,third_sku_id"
// @Param value query string false "查询条件的输入值"
// @Param finance_code query string true "门店财务编码"
// @Param record_type query string true "记录类型(1删除商品2上架3批量上架4下架 5批量下架6自动上架7自动下架)"
// @Param page_index query string false "分页"
// @Param page_size query string false "分页大小"
// @Success 200 {object} dto.CommonPageHttpResponse
// @Failure 400 {object} dto.CommonPageHttpResponse
// @Router /boss/product/channel-product/records [get]
func ChannelProductRecords(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var res = dto.CommonPageHttpResponse{}

	channel_id := cast.ToInt32(c.QueryParam("channel_id"))
	start_time := c.QueryParam("start_time")
	end_time := c.QueryParam("end_time")
	where := c.QueryParam("where")
	value := c.QueryParam("value")
	finance_code := c.QueryParam("finance_code")
	record_type := cast.ToInt64(c.QueryParam("record_type"))
	page_index := cast.ToInt64(c.QueryParam("page_index"))
	page_size := cast.ToInt64(c.QueryParam("page_size"))

	if len(finance_code) <= 0 {
		res.Message = "门店参数必传"
		return c.JSON(400, res)
	}

	if page_size <= 0 {
		page_size = 10
	}
	if page_index <= 0 {
		page_index = 1
	}
	// 默认展示近三天的数据
	if len(start_time) <= 0 || len(end_time) <= 0 {
		add := time.Now().Add(-3 * 24 * time.Hour)
		format := add.Format(utils.DATE_TIME_LAYOUT)
		start_time = format
	}

	rpcRequest := pc.ChannelProductRecordsVo{
		RecordType:  record_type,
		ChannelId:   channel_id,
		StartTime:   start_time,
		EndTime:     end_time,
		Where:       where,
		Value:       value,
		FinanceCode: finance_code,
		PageSize:    page_size,
		PageIndex:   page_index,
	}

	out, err := client.RPC.ChannelProductRecords(client.Ctx, &rpcRequest)
	if err != nil {
		res.Message = "查询渠道商品记录异常"
		glog.Error(res.Message, " ", err.Error())
		return c.JSON(400, res)
	}

	if out.Code != 200 {
		res.Message = "为查询到记录信息"
		glog.Error(res.Message, " ", out.Error)
		return c.JSON(400, res)
	}

	res.Total = cast.ToInt32(out.Total)
	res.Data = out.Data
	return c.JSON(200, res)
}

// @Summary v6.26.0 饿了么错误日志列表
// @Tags v6.26.0 商品库
// @Accept plain
// @Produce plain
// @Param shop_id query string false "饿了么渠道店铺ID"
// @Param finance_code query string false "财务编码"
// @Param sku_id query string false "SKUID"
// @Param page_index query string false "分页"
// @Param page_size query string false "分页大小"
// @Success 200 {object} models.ElmErrorResponse
// @Failure 400 {object} models.ElmErrorResponse
// @Router /boss/product/channel-product/elm-error/list [get]
func ChannelProductElmRecords(c echo.Context) error {

	var res = models.ElmErrorResponse{}
	shop_id := c.QueryParam("shop_id")
	finance_code := c.QueryParam("finance_code")
	sku_id := c.QueryParam("sku_id")
	page_index := cast.ToInt64(c.QueryParam("page_index"))
	page_size := cast.ToInt64(c.QueryParam("page_size"))

	if page_size <= 0 {
		page_size = 10
	}
	if page_index <= 0 {
		page_index = 1
	}

	conn := GetDcProductDBConn()
	conn.ShowSQL()
	session := conn.Table("elm_error").Alias("d").Join("inner", "datacenter.store_relation s", "s.channel_store_id = d.shop_id and s.channel_id=3").Where("1=1")
	if len(shop_id) > 0 {
		session.Where("d.shop_id = ? ", shop_id)
	}
	if len(finance_code) > 0 {
		session.Where("s.finance_code = ? ", finance_code)
	}
	if len(sku_id) > 0 {
		session.Where("d.sku_id = ? ", sku_id)
	}

	// 老版本的xorm的FindAndCount不能用proto
	//var data []models.ElmError
	data := make([]models.ElmErrorList, 0)
	count, err := session.Select("d.*,s.finance_code").Limit(int(page_size), int(page_index*page_size)-int(page_size)).OrderBy("d.id desc").FindAndCount(&data)
	if err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(200, res)
	}
	res.Code = 200
	res.Total = cast.ToInt32(count)
	res.Data = data
	return c.JSON(200, res)
}

// ChannelProductElmDelete @Summary v6.26.0 饿了么错误日志删除
// @Tags v6.26.0 商品库
// @Accept json
// @Produce plain
// @Param ChannelProductElmDelete body models.ElmErrorDelPar true " "
// @Success 200 {object} models.ElmErrorResponse
// @Failure 400 {object} models.ElmErrorResponse
// @Router /boss/product/channel-product/elm-error/del [POST]
func ChannelProductElmDelete(c echo.Context) error {

	var res = models.ElmErrorResponse{}
	model := new(models.ElmErrorDelPar)
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	conn := GetDcProductDBConn()
	_, err := conn.In("id", model.Ids).Delete(&models.ElmError{})
	if err != nil {
		glog.Error("ChannelProductElmDelete ", err.Error())
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}

	res.Code = 200
	return c.JSON(200, res)
}

// @Summary v6.6.7.1 平台商品库修改货号信息查询
// @Tags v6.6.7.1 商品库
// @Accept plain
// @Produce plain
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param where query string false "条件类型"
// @Param value query string false "条件值"
// @Param record_type query int false "记录类型:1修改货号，2删除商品"
// @Param page_index query int true "当前页"
// @Param page_size query int true "分页大小"
// @Success 200 {object} dto.CommonPageHttpResponse
// @Failure 400 {object} dto.CommonPageHttpResponse
// @Router /boss/product/product/records [get]
func ProductRecords(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var res = dto.CommonPageHttpResponse{}

	start_time := c.QueryParam("start_time")
	end_time := c.QueryParam("end_time")
	where := c.QueryParam("where")
	record_type := cast.ToInt64(c.QueryParam("record_type"))
	page_index := cast.ToInt64(c.QueryParam("page_index"))
	page_size := cast.ToInt64(c.QueryParam("page_size"))
	value := c.QueryParam("value")

	if page_size <= 0 {
		page_size = 10
	}
	if page_index <= 0 {
		page_index = 1
	}

	// 默认展示近三天的数据
	if len(start_time) <= 0 || len(end_time) <= 0 {
		add := time.Now().Add(-3 * 24 * time.Hour)
		format := add.Format(utils.DATE_TIME_LAYOUT)
		start_time = format
	}

	rpcRequest := pc.ProductRecordsVo{
		StartTime:  start_time,
		EndTime:    end_time,
		Where:      where,
		RecordType: record_type,
		PageIndex:  page_index,
		PageSize:   page_size,
		Value:      value,
	}

	out, err := client.RPC.ProductRecords(client.Ctx, &rpcRequest)
	if err != nil {
		res.Message = "查询平台商品记录异常"
		glog.Error(res.Message, " ", err.Error())
		return c.JSON(400, res)
	}
	if out.Code != 200 {
		res.Message = "未查询到记录信息"
		glog.Error(res.Message, " ", out.Error)
		return c.JSON(400, res)
	}

	res.Total = cast.ToInt32(out.Total)
	res.Data = out.Data
	return c.JSON(200, res)

}

// @Summary 对应病症列表
// @Tags 商品库
// @Accept json
// @Produce json
// @Param model query pc.ProductDiagnoseDicReq true " "
// @Success 200 {object} pc.ProductDiagnoseDicRes
// @Failure 400 {object} pc.ProductDiagnoseDicRes
// @Router /boss/product/diagnose-dic [get]
func ProductDiagnoseDic(c echo.Context) error {
	req := &pc.ProductDiagnoseDicReq{
		Keyword:   c.QueryParam("keyword"),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
	}

	client := GetDcProductClient()
	defer client.Close()

	if out, err := client.RPC.DiagnoseDic(client.Ctx, req); err != nil {
		return c.JSON(400, &pc.ProductDiagnoseDicRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 获取药品用量对应的宠物类型
// @Tags 商品库
// @Accept json
// @Produce json
// @Success 200 {object} pc.ProductPetTypeRes
// @Failure 400 {object} pc.ProductPetTypeRes
// @Router /boss/product/pet-type [get]
func ProductPetType(c echo.Context) error {
	client := GetDcProductClient()
	defer client.Close()

	if out, err := client.RPC.PetType(client.Ctx, &empty.Empty{}); err != nil {
		return c.JSON(400, &pc.ProductPetTypeRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 通过货号子龙商品信息
// @Tags 商品库
// @Accept json
// @Produce json
// @Param model body et.ZiLongProductListReq true " "
// @Success 200 {object} et.ZiLongProductListRes
// @Failure 400 {object} et.ZiLongProductListRes
// @Router /boss/product/zilong-list [post]
func ProductZiLongList(c echo.Context) error {
	req := new(et.ZiLongProductListReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &et.ZiLongProductListRes{Code: 400, Message: err.Error()})
	}

	if len(req.ProductCode) == 0 {
		return c.JSON(400, &et.ZiLongProductListRes{Code: 400, Message: "商品货号不能为空"})
	}

	if req.Number == 0 {
		req.Number = 1
	}
	if req.Size == 0 {
		req.Size = 20
	}

	client := et.GetExternalClient()
	if out, err := client.ZiLong.ProductList(client.Ctx, req); err != nil {
		return c.JSON(400, &et.ZiLongProductListRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
