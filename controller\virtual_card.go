package controller

import (
	"_/models"
	"_/params"
	"_/proto/cc"
	"_/proto/oc"
	"_/utils"
	"fmt"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/status"
)

// @summary 创建虚拟卡信息接口
// @Tags 虚拟卡券模块
// @Accept json
// @Produce json
// @Param params.VirtualTaskListRequest body params.VirtualTaskListRequest true "参数结构体"
// @Success 200 {object} models.VirtualTaskListResponse
// @Failure 400 {object} models.VirtualTaskListResponse
// @Router /boss/vip/virtual_card/create-list [POST]
func CreateVirtualCardList(c echo.Context) error {
	var (
		param            = new(params.VirtualTaskListRequest)
		out              = new(models.VirtualTaskListResponse)
		userNo, userName = "", ""
	)
	out.Code = 400
	//获取用户信息
	if claims, err := utils.GetPayloadDirectly(c); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		userNo = utils.InterfaceToString(claims["userno"])
		userName = utils.InterfaceToString(claims["name"])
		param.UserId = userNo
	}
	fmt.Printf("%s,%s", userNo, userName)
	if err := c.Bind(param); err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if param.PageIndex < 1 {
		param.PageIndex = 1
	}
	if param.PageSize < 1 {
		param.PageSize = 10
	}
	var riskUser []*models.VipCardVirtualCreate
	q := GetDatacenterDBConn().Table("vip_card_virtual_create")
	//都查询出来，不给下载
	//q.Where("user_id = ?", param.UserId)
	if count, err := q.OrderBy("id desc").Limit(int(param.PageSize), int(param.PageSize*(param.PageIndex-1))).FindAndCount(&riskUser); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else {
		out.TotalCount = int32(count)
	}
	for index, item := range riskUser {
		if item.UserId != param.UserId {
			riskUser[index].FileUrl = ""
		}
	}
	out.Data = append(out.Data, riskUser...)
	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// @summary 创建虚拟卡信息接口
// @Tags 虚拟卡券模块
// @Accept json
// @Produce json
// @Param cc.CreateRequest body cc.CreateRequest true "参数结构体"
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/vip/virtual_card/create [POST]
func CreateVirtualCard(c echo.Context) error {
	var (
		param            = new(cc.CreateRequest)
		out              = new(params.BaseResponse)
		userNo, userName = "", ""
	)
	out.Code = 400
	if err := c.Bind(param); err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	//获取用户信息
	if claims, err := utils.GetPayloadDirectly(c); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		userNo = utils.InterfaceToString(claims["userno"])
		userName = utils.InterfaceToString(claims["name"])
		param.UserId = userNo
		param.UserName = userName
	}
	if param.CardCount > 10000 || param.CardCount < 1 {
		out.Message = "生成数量只能是1-10000"
		return c.JSON(400, &out)
	}
	if param.SellType == 1 {
		param.CardPrice = 0
	}

	fmt.Printf("%s,%s", userNo, userName)
	client := cc.GetCustomerCenterClient()
	defer client.Close()

	rpcResponse, err := client.VirtualCard.CreateVirtualCard(client.Ctx, param)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(400, &out)
	}
	if rpcResponse.Code != 200 {
		out.Message = rpcResponse.Message
		return c.JSON(400, &out)
	}
	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// @Summary 虚拟卡导出
// @Tags 虚拟卡券模块
// @Param params.VipCardVirtualListReq body params.VipCardVirtualListReq true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/vip/virtual_card/export [post]
func VirtualCardExport(c echo.Context) error {
	model := new(params.VipCardVirtualListReq)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	Ip := strings.Split(c.Request().RemoteAddr, ":")[0]
	IpLocation := GetIpAddress(Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}
	//model.CardStr = c.QueryParam("card_str")
	if model.CardStr != "" {
		if len(model.CardStr) < 12 && !strings.Contains(model.CardStr, "FY") {
			return r.NewHTTPError(400, "卡号格式不正确")
		}
		model.CardId = cast.ToInt64(model.CardStr[2:len(model.CardStr)])
	}

	rpcReq := cc.VirtualCardListReq{
		BatchId:         model.BatchId,
		OrgId:           model.OrgId,
		TemplateId:      model.TemplateId,
		UserId:          model.UserId,
		UserMobile:      model.UserMobile,
		UseTimeStart:    model.UseTimeStart,
		UseTimeEnd:      model.UseTimeEnd,
		CreateTimeStart: model.CreateTimeStart,
		CreateTimeEnd:   model.CreateTimeEnd,
		CardIds:         model.CardIds,
		PageIndex:       model.PageIndex,
		PageSize:        model.PageSize,
		UserNo:          UserNo,
		Status:          model.Status,
		CardId:          model.CardId,
	}

	taskContent := 15
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, Ip, IpLocation, kit.JsonEncode(rpcReq), taskContent, "", "虚拟卡券兑换查询记录", 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// VirtualCardList
// @summary 获取虚拟卡券信息列表
// @Tags 虚拟卡券模块
// @Accept json
// @Produce json
// @Param VipCardVirtualListReq body params.VipCardVirtualListReq true "参数结构体"
// @Success 200 {object} params.VipCardVirtualListRes
// @Failure 400 {object} params.VipCardVirtualListRes
// @Router /boss/vip/virtual_card/list [GET]
func VirtualCardList(c echo.Context) error {
	var (
		out    = new(params.VipCardVirtualListRes)
		param  = new(params.VipCardVirtualListReq)
		userNo = ""
	)
	logPrefix := "VirtualCardList ======== 接口地址：/boss/vip/virtual_card/list"
	//获取用户信息
	if claims, err := utils.GetPayloadDirectly(c); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		userNo = utils.InterfaceToString(claims["userno"])
	}
	if err := c.Bind(param); err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	param.CardStr = c.QueryParam("card_str")
	if param.CardStr != "" {
		if len(param.CardStr) < 12 && !strings.Contains(param.CardStr, "FY") {
			out.Message = "卡号格式不正确"
			return c.JSON(http.StatusBadRequest, out)
		}
		param.CardId = cast.ToInt64(param.CardStr[2:len(param.CardStr)])
	}
	if param.PageIndex == 0 {
		param.PageIndex = 1
	}
	if param.PageSize == 0 {
		param.PageSize = 10
	}
	rpcReq := cc.VirtualCardListReq{
		BatchId:         param.BatchId,
		OrgId:           param.OrgId,
		TemplateId:      param.TemplateId,
		UserId:          param.UserId,
		UserMobile:      param.UserMobile,
		UseTimeStart:    param.UseTimeStart,
		UseTimeEnd:      param.UseTimeEnd,
		CreateTimeStart: param.CreateTimeStart,
		CreateTimeEnd:   param.CreateTimeEnd,
		CardIds:         param.CardIds,
		PageIndex:       param.PageIndex,
		PageSize:        param.PageSize,
		UserNo:          userNo,
		Status:          param.Status,
		CardId:          param.CardId,
	}
	customerClient := cc.GetCustomerCenterClient()
	defer customerClient.Conn.Close()
	defer customerClient.Cf()

	glog.Info(logPrefix, "数据返回：rpc请求参数：", utils.InterfaceToJSON(rpcReq))
	if rpcRes, err := customerClient.VirtualCard.VirtualCardList(customerClient.Ctx, &rpcReq); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logPrefix, "rpc错误：", err.Error(), "rpc请求参数：", utils.InterfaceToJSON(rpcReq))
			out.Message = s.Message()
			return c.JSON(http.StatusBadRequest, out)
		}
	} else {
		glog.Info(logPrefix, "数据返回：rpc请求参数：", utils.InterfaceToJSON(rpcReq), ";返回结果：", utils.InterfaceToJSON(rpcRes))
		if err := utils.MapTo(&rpcRes.List, &out.Data); err != nil {
			glog.Error(logPrefix, "返回结果数据结构转换失败：", err)
			out.Message = err.Error()
			return c.JSON(http.StatusBadRequest, out)
		}
		out.PageCount = rpcRes.PageCount
		//处理手机号信息
		for k, _ := range out.Data {

			//out.Data[k].UserMobile = utils.MobileReplaceWithStar(v.UserMobile)
			out.Data[k].CardStr = "FY" + cast.ToString(out.Data[k].CardId)
		}
	}
	return c.JSON(http.StatusOK, out)
}
