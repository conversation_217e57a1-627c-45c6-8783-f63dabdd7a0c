package route

import (
	"_/controller"
	"bytes"
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
)

// 路由
func AuthRoute(e *echo.Group) *echo.Group {
	//用户中心
	userGroup(e)

	//商品中心
	productGroup(e)

	//美团接口
	mtGroup(e)

	//京东到家接口
	jddjGroup(e)

	//饿了么接口
	elmGroup(e)

	//订单中心
	orderCenterGroup(e)

	//调度中心
	warehouseGroup(e)

	//库存中心
	inventoryCenterGroup(e)

	//数据中心
	datacenterGroup(e)
	//支付中心
	paycenterGroup(e)
	//门店数据远程和对应关系
	storerelationControllerGroup(e)

	//门店基础数据本地
	shopsetControllerGroup(e)

	refundGroup(e)

	// 营销活动
	promotionGroup(e)
	//流浪救助活动
	marketcenterYearGroup(e)

	//运营中心
	marketcenterGroup(e)

	//百万新宠
	petmillionsGroup(e)
	//广告
	advertisementGroup(e)

	//智慧中心前置仓信息统计
	intelligenceCenterGroup(e)

	// 库存可视化
	stockVisualGroup(e)
	// 水印活动
	watermarkGroup(e)
	// 在线问诊
	diagnoseGroup(e)

	//文件上传记录
	fileUploadRecordGroup(e)

	//SCRM企业微信
	scrmGroup(e)
	//会员
	member(e)

	// 勃林格文章
	article(e)

	//用户反馈
	feedback(e)

	// 活动
	activityGroup(e)

	dispatch(e)

	// 电商分销模板
	distributionGroup(e)

	// 积分商城
	integralStore(e)

	// 积分明显
	integralRecord(e)

	//省份城市区域划分
	regionalDivision(e)

	//全国社群
	groupChat(e)

	//搜索词
	SearchRoute(e)

	// 代运营商品配置
	AgencyRoute(e)

	// 切仓配置
	switchWarehouseRoute(e)

	//抽奖组件
	LuckyDraw(e)

	//风控管理
	risk(e)

	//付费会员卡
	vipCard(e)

	//工具:完成特殊化或者一次性功能
	tool(e)

	e.POST("/es/init-store", controller.EsInitStore)
	e.POST("/es/init-store-product", controller.EsInitStoreProduct)

	// 海报裂变
	posterFission(e)

	//第三方APPID配置管理

	return e
}

func feedback(e *echo.Group) {
	feed := e.Group("/feedback")
	feed.GET("/list", controller.GetFeedbackList)
	feed.GET("/detail", controller.GetFeedback)
	feed.POST("/comment-add", controller.AddFeedbackComment)
	feed.GET("/comment-list", controller.GetFeedbackCommentList)
}
func DeliveryConfig(e *echo.Group) {
	g := e.Group("/delivery")
	g.GET("/list", controller.DeliveryConfigGetList)
	g.GET("/get", controller.DeliveryConfigGet)
	g.POST("/add", controller.DeliveryConfigInsert)
	g.POST("/modify", controller.DeliveryConfigModify)
}

func member(e *echo.Group) {
	g := e.Group("/member")
	//会员等级列表
	g.GET("/level/list", controller.UserLevelList)
	//会员等级启用停用
	g.POST("/level/set", controller.UserLevelSet)
	//会员等级编辑权益项
	g.GET("/level/equity-list", controller.GetUserEditEquityList)
	//获取会员关联的权益
	g.GET("/level/equity-detail", controller.GetUserEditEquityDetail)
	//会员等级编辑
	g.POST("/level/edit", controller.UserLevelEdit)
	//更新会员等级
	g.POST("/level/refresh", controller.RefreshUserLevel)

	g.GET("/merge", controller.MemberMerge)
	//任务列表
	g.GET("/task/list", controller.TaskList)
	//任务保存
	g.POST("/task/save", controller.TaskSave)
	//会员权益列表
	g.GET("/equity/list", controller.MemberEquityList)
	//会员权益编辑
	g.POST("/equity/edit", controller.MemberEquityEdit)
	//获取权益详情
	g.GET("/equity/detail", controller.MemberEquityDetail)
	//会员权益显示或隐藏
	g.POST("/equity/set", controller.MemberEquitySet)
	//获取会员概况
	g.GET("/counts", controller.MemberCounts)
}

func userGroup(e *echo.Group) {
	g := e.Group("/user")

	g.GET("/hospital/list", controller.GetHospitalListByUserNo)
	g.GET("/hospital/bjlist", controller.GetBjHospitalListByUserNo)
	g.GET("/hospitalCondition/list", controller.GetHospitalListByCondition)
	// v6.23.0
	g.GET("/org-store/list", controller.GetOrgStoreList)
	g.GET("/permits/judge", controller.JudgeUserPower)
}

func orderCenterGroup(e *echo.Group) {
	g := e.Group("/ordercenter")

	g.POST("/performance/save", controller.SavePerformance) //保存业绩分配
	//订单中心
	g.GET("/order/bookingorder/list", controller.GetBookingOrderList)
	g.POST("/order/bookingorder/print", controller.PrintBookingOrder)
	g.POST("/order/exports", controller.AwenOrdersExport)
	g.POST("/order/export", controller.AwenOrderExport)
	g.POST("/order/product/export", controller.AwenOrderProductExport)
	g.POST("/order/parent-export", controller.AwenParentOrderExport)
	//配送报表导出
	g.POST("/order/delivery-report-export", controller.DeliveryReportExport)
	g.POST("/order/product/parent-export", controller.AwenParentOrderProductExport)
	g.POST("/order/virtual-export", controller.AwenVirtualOrderExport)
	g.POST("/order/product/virtual-export", controller.AwenVirtualOrderProductExport)
	g.POST("/order/re-push-third", controller.MtRePushThird)
	g.POST("/order/search-delivery-price", controller.SearchDeliveryPrice)
	g.GET("/order/refund-foods", controller.OrderGetPartRefundFoods)
	g.POST("/order/apply-part-refund", controller.OrderApplyPartRefund)
	g.POST("/order/MtOrderRefundReject", controller.MtOrderRefundReject)
	g.POST("/order/MtOrderRefundAgree", controller.MtOrderRefundAgree)
	g.POST("/order/OrderRefundReject", controller.OrderRefundReject)
	g.POST("/order/OrderRefundAgree", controller.OrderRefundAgree)
	g.POST("/order/OrderRetrunGetList", controller.OrderRetrunGetList)
	g.POST("/order/OrderRetrunGet", controller.OrderRetrunGet)
	g.POST("/order/refund-order-list", controller.RefundOrderList)
	g.POST("/order/refund-order-export", controller.RefundOrderExport)
	g.POST("/order/refund-re-push-third", controller.RefundRePushThird)
	g.POST("/order/update-order-sku", controller.UpdateOrderSku)

	//查询报表信息
	g.GET("/order/delivery/report", controller.OrderDeliveryReportList)
	g.GET("/order/parent/list", controller.AwenParentOrderList)                     //父订单分页列表
	g.GET("/order/parent/basedetail", controller.AwenParentOrderBaseDetail)         //父订单详情基础信息
	g.GET("/order/parent/deliverystate", controller.AwenParentOrderDeliveryState)   //父订单详情订单状态信息
	g.GET("/order/parent/deliverydetail", controller.AwenParentOrderDeliveryDetail) //父订单详情配送流程信息

	g.POST("/order/update-expressinfo", controller.AwenOrderExpressInfoUpdate) //更新物流信息
	g.GET("/order/expressinfo", controller.AwenOrderExpressinfo)               //物流查询

	g.GET("/order/mater/list", controller.AwenMaterOrderList)                     //实物订单分页列表
	g.GET("/order/mater/group-list", controller.AwenMaterGroupOrderList)          //团长拼团-实物子订单分页列表
	g.GET("/order/mater/basedetail", controller.AwenMaterOrderBaseDetail)         //实物订单详情基础信息
	g.GET("/order/mater/deliverystate", controller.AwenMaterOrderDeliveryState)   //实物订单详情订单状态信息
	g.GET("/order/mater/deliverydetail", controller.AwenMaterOrderDeliveryDetail) //实物订单详情配送流程信息

	g.GET("/order/virtual/list", controller.AwenVirtualOrderList)                     //虚拟订单分页列表
	g.GET("/order/virtual/group-list", controller.AwenVirtualGroupOrderList)          //团长拼团-虚拟子订单分页列表
	g.GET("/order/virtual/basedetail", controller.AwenVirtualOrderBaseDetail)         //虚拟订单详情基础信息
	g.GET("/order/virtual/deliverystate", controller.AwenVirtualOrderDeliveryState)   //虚拟订单详情订单状态信息
	g.GET("/order/virtual/deliverydetail", controller.AwenVirtualOrderDeliveryDetail) //虚拟订单详情配送流程信息

	g.POST("/order/accept-order", controller.AcceptOrder)
	g.POST("/order/cancel-accept-order", controller.CancelAcceptOrder)
	g.POST("/order/picking-order", controller.PickingOrder)
	//异常订单
	g.GET("/OrderExceptionList", controller.OrderExceptionList) //配送异常列表
	g.POST("/OrderCancel", controller.OrderCancel)              //取消配送
	g.POST("/OrderOwnDeliver", controller.OrderOwnDeliver)      //发起自配
	g.POST("/GoodArrive", controller.GoodArrive)                //已送达
	g.POST("/DistributionAgain", controller.DistributionAgain)  //再次发起配送
	g.POST("/order/reDelivery", controller.ReDelivery)          // 订单列表-发起配送
	g.POST("/order/zt-confirm", controller.ConfirmUpetDj)       // 订单列表-发起配送

	//订单导出任务
	g.GET("/order/get-task-list", controller.GetOrderExportTaskList) //订单导出任务——获取列表
	//详情取消接口
	g.POST("/order/CancelDelivery", controller.CancelDelivery) //订单详情，取消配送配送
	//达达异常妥投确认送回物品
	g.POST("/order/DaDaConfirmGoodsReturn", controller.DaDaConfirmGoodsReturn)
	//骑手送回商品
	g.POST("/order/ConfirmGoodsReturn", controller.ConfirmGoodsReturn)

	g.POST("/order/directlyFee/refund", controller.BatchRefundDirectlyFee)
	g.GET("/order/directlyFee/get", controller.GetRefundDirectlyFee)
	g.GET("/order/directlyFee/export", controller.ExportRefundDirectlyFee)

	// 设置订单发货
	g.POST("/order/setDelivery", controller.OrderSetDelivery)
	// 确认已发货
	g.POST("/order/confirmDelivered", controller.OrderConfirmDelivered)

	// 虚拟订单手动核销
	g.GET("/order/virtual/manual-written-off-check", controller.ManualWrittenOffCheck)
	g.POST("/order/virtual/manual-written-off", controller.ManualWrittenOff)

	//电商虚拟订单延长核销时间
	g.GET("/order/virtual/queryMallVirtualOrderExtendInfo", controller.QueryMallVirtualOrderExtendInfo)
	g.POST("/order/virtual/extendMallVirtualOrderVerifyCodeExpiryDate", controller.ExtendMallVirtualOrderVerifyCodeExpiryDate)

	g.GET("/order/update-order-data", controller.UpdateOrderData)

	//团长制拼团-团订单
	g.GET("/order/community_group/group-list", controller.OrderCommunityGroupList)
	//团长制拼团-团员订单
	g.GET("/order/community_group/member-list", controller.OrderCommunityGroupMemberList)
	//团长制拼团-导出团员订单
	g.POST("/order/community_group/member-order-export", controller.AwenCommunityGroupMemberOrderExport)

	//用户手机号解密
	g.POST("/order/mobile/decrypt", controller.MobileDecrypt)
}

func shopsetControllerGroup(e *echo.Group) {
	g := e.Group("/shopsetcontroller")

	g.POST("/ShopSetList", controller.ShopSetList)    //获取门店数据本地和配置
	g.POST("/ShopSetAdd", controller.ShopSetAdd)      //修改或者设置基础数据
	g.GET("/ShopSetGet", controller.ShopSetGet)       //获取单个门店基础数据
	g.GET("/GetBJShopTree", controller.GetBJShopTree) //获取北京门店树形结构

	g.POST("/RemindSetAdd", controller.RemindSetAdd) //新增店铺管理-系统设置-订单提醒接口
	g.POST("/AutoSetAdd", controller.AutoSetAdd)     //新增店铺管理-系统设置-自动接单设置接口
}

func storerelationControllerGroup(e *echo.Group) {
	g := e.Group("/storerelationcontroller")

	g.POST("/StoreList", controller.StoreList)                 //获取门店数据
	g.POST("/BindStoreRelation", controller.BindStoreRelation) //绑定对应关系
	g.POST("/CreateStore", controller.CreateStore)             //创建配送门店
}

func inventoryCenterGroup(e *echo.Group) {
	g := e.Group("/inventorycenter")

	g.POST("/stock/import", controller.ImportThirdStock)
	//g.GET("/stock/getstockbysku", controller.GetStockByProductId)
	//g.POST("/stock/freezestock", controller.FreezeStock)
}

func warehouseGroup(e *echo.Group) {
	g := e.Group("/warehouse")

	g.POST("/warehouselist", controller.GetWarehouseList)
	g.GET("/warehousebyid", controller.GetWarehouseById)
	g.POST("/updatewarehousestatus", controller.UpdateWarehouseStatus)
	g.GET("/getbasearea", controller.GetBaseArea)
	g.GET("/getAreabywarehouseid", controller.GetAreaByWarehouseId)
	g.POST("/warehouserelation", controller.WarehouseRelation)
	g.GET("/getwarehouserelationlist", controller.GetWarehouseRelationList)
	g.POST("/add", controller.AddWarehouse)
	g.POST("/edit", controller.EditWarehouse)
	g.POST("/addwarehousearea", controller.AddWarehouseArea)
	g.GET("/getwarehousetype", controller.GetWarehouseType)
	g.GET("/getwarehouselevel", controller.GetWarehouseLevel)
	g.POST("/getwarehousebyarea", controller.GetWarehouseByArea)
	g.GET("/warehouselog", controller.GetWarehouseLog)
	// 区域仓关联前置仓
	g.POST("/region/relation", controller.PreposeWarehouseRelation)
	// 根据仓库id获取已绑定的前置仓信息列表
	g.GET("/region/relation/list", controller.GetPreposeWarehouseRelationList)
	//查询前置仓关联的所有城市列表
	g.GET("/prepose/city/list", controller.PreposeWarehouseCityList)
	//美团店铺关联虚拟仓批量导入
	g.GET("/shop_bind_warehouse", controller.ShopBindWarehouse)
	//门店修改仓库
	g.POST("/shop_bind_warehouse/edit", controller.EditShopBindWarehouse)
	//获取门店关联列表
	g.GET("/shop_warehouse_info", controller.ShopBindWarehouseList)
	//获取绑定了仓库的门店数量
	g.GET("/bind_shop", controller.BindShops)
	//取消绑定任务
	g.GET("/cancel_task", controller.CancelTask)
	//移除门店渠道的仓库绑定关系
	g.GET("/remove_shop_channel_relation", controller.RemoveShop)
	//初始化数据库
	g.GET("/init_shop", controller.InitWarehouseBindShopDB)
	//仓库白名单列表
	g.GET("/white-list", controller.WarehouseWhiteList)
	//仓库白名单移除
	g.POST("/white-del", controller.WarehouseWhiteDel)
	//g.POST("/white-import", controller.WarehouseWhiteImport)
	//仓库白名单导入 task-content传37  仓库白名单导出 task-content传38
	///g.POST("/CreateBatchTask", controller.CreateBatchTask) //仓库白名单导入
	g.GET("/white-export", controller.ExportWhiteExport) //仓库白名单导出
	//获取操作记录
	g.GET("/operate-record", controller.OperateRecord)
}

func datacenterGroup(e *echo.Group) {
	g := e.Group("/datacenter")
	DeliveryConfig(g)
	g.GET("/platform/channelusable", controller.QueryPlatformChannelUsable)
	g.GET("/store/list", controller.QueryStores)
	g.POST("/message/messagecreate", controller.MessageCreate)
	g.POST("/message/messageupdate", controller.MessageUpdate)
	g.GET("/message/list", controller.MessageList)
	g.POST("/message/send", controller.MessageSend)
	g.POST("/shop/shopstoreupdate", controller.ShopStoreUpdate) //v6.27.1

	g.POST("/shop/shopbasicsetupupdate", controller.ShopBasicSetupUpdate)
	g.POST("/shop/ShopSelfLiftingUpdate", controller.ShopSelfLiftingUpdate)           //店铺自提更新
	g.POST("/shop/InitializationDeliveryarea", controller.InitializationDeliveryarea) //初始化,更新所有门店开启同步美团配送范围
	g.GET("/shop/shopbusinesssetupget", controller.ShopBusinessSetupGet)
	g.POST("/shop/shopbusinesssetupupdate", controller.ShopBusinesssetupUpdate)

	g.GET("/shop/GetMtStorey", controller.GetMtStorey)
	g.POST("/shop/MtStoreySave", controller.MtStoreySave)

	g.POST("/shop/MtStoreDeliveryList", controller.MtStoreDeliveryList)
	g.POST("/shop/MtStoreDeliveryUpdate", controller.MtStoreDeliveryUpdate)
	g.POST("/shop/MtStoreDeliveryDelete", controller.MtStoreDeliveryDelete)

	g.POST("/shop/shopdeliveryserviceset", controller.ShopDeliveryServiceSet)
	g.GET("/shop/shopdeliveryserviceget", controller.ShopDeliveryServiceGet)
	g.POST("/shop/shopcateandimageupdate", controller.ShopCateAndImageupdate)
	g.POST("/shop/shopotherupdate", controller.ShopOtherUpdate)
	g.GET("/shop/shopstoreget", controller.ShopstoreGet) //v6.27.1
	g.POST("/shop/shopotherupdate", controller.ShopOtherUpdate)
	g.POST("/shop/shopreturnaddressset", controller.ShopReturnAddressSet)
	g.GET("/near-shops", controller.NearShops)
	g.POST("/shop/shopbannerupdate", controller.ShopBannerUpdate)
	g.GET("/staff/list", controller.StaffList)
	g.POST("/auto/GetAcpUserInfo", controller.GetAcpUserInfo)
	g.POST("/auto/AddAcpUserInfo", controller.AddAcpUserInfo)
	g.POST("/auto/UpdateAcpUserInfo", controller.UpdateAcpUserInfo)
	g.POST("/auto/DeleteAcpUserInfo", controller.DeleteAcpUserInfo)
	g.POST("/auto/SeleteAcpUserInfoList", controller.SeleteAcpUserInfoList)
	g.POST("/remind/AddRemind", controller.AddRemind)
	g.POST("/remind/DeleteRemind", controller.DeleteRemind)
	g.POST("/remind/UpdateRemind", controller.UpdateRemind)
	g.POST("/remind/SelectRemindList", controller.SelectRemindList)
	g.POST("/remind/SelectRemindByUserNo", controller.SelectRemindByUserNo)
	g.POST("/integral/query", controller.OrderIntegralQuery)                       // 阿闻积分查询
	g.POST("/integral/export", controller.OrderIntegralExport)                     // 阿闻积分导出
	g.GET("/integral/exportlist", controller.OrderIntegralExportList)              // 阿闻积分下载列表
	g.GET("/integral/download", controller.OrderIntegralDownload)                  // 阿闻积分导出下载
	g.POST("/integral/historyorderintegra", controller.AwHistoryOrderIntegralSync) // 阿闻历史订单积分同步

	g.GET("/shop/GetStoreDeliveryList", controller.GetStoreDeliveryList)
	g.POST("/shop/SetStoreDelivery", controller.SetStoreDelivery)
	g.POST("/shop/SetStoreDeliveryStatus", controller.SetStoreDeliveryStatus)
	g.POST("/shop/BatchStoreDeliveryTask", controller.BatchStoreDeliveryTask)

	//v6.6.0 店铺配送方式设置
	g.GET("/shop/delivery-method-list", controller.GetStoreDeliveryMethodList) //店铺配送设置列表
	g.POST("/shop/set-delivery-method", controller.SetStoreDeliveryMethod)     //设置店铺配送方式

	//店铺操作日志
	g.GET("/shop/operate-logs", controller.GetStoreOperateLogs)
	//店铺支持的快递公司列表
	g.GET("/shop/extend-express-list", controller.StoreExpressList)
	//店铺所有大区+城市
	g.GET("/shop/regionAndCityOptions", controller.StoreRegionAndCityOptions)

	g.GET("/version/get", controller.GetAppVersion)            //获取最新版本
	g.POST("/version/add", controller.AddAppVersion)           //新增版本
	g.POST("/navigationbar/add", controller.AddNavigationbar)  //新增或修改金刚区配置
	g.GET("/navigationbar/list", controller.NavigationbarList) //获取金刚区配置
	g.POST("/navigationbar/up", controller.NavigationbarUp)    //金刚区配置上下架

	// saas1.0 Api
	g.POST("/store-master/create", controller.CreateStoreMaster)        // 创建店铺类型
	g.POST("/store-master/update", controller.UpdateStoreMaster)        // 更新店铺类型
	g.POST("/store-master/delete", controller.DeleteStoreMaster)        // 删除店铺类型
	g.GET("/store-master/list", controller.GetStoreMasterList)          // 获取店铺类型列表
	g.GET("/store-master/selecter", controller.GetStoreMasterSelecter)  // 获取店铺类型Selecter
	g.GET("/store-master/sku-list", controller.GetStoreMasterSkuList)   // 获取店铺可上架商品
	g.POST("/store-master/sku-remove", controller.RemoveMasterStoreSku) // 移除店铺可上架商品

	g.POST("/pickup/list", controller.PickupList)
	g.POST("/pickups", controller.PickupStore)
	g.POST("/pickup/delete", controller.PickupDelete)

	//社区团购-自提点列表
	g.GET("/pickup/station", controller.PickupStations)
	//社区团购-自提点新增或保存
	g.POST("/pickup/station", controller.PickupStationStore)
	//社区团购-自提点禁用或启用
	g.PATCH("/pickup/station", controller.PickupStationPatch)
	//社区团购-自提点详情
	g.GET("/pickup/station/detail", controller.PickupStationDetail)
	//社区团购-自提点导入模板
	g.GET("/pickup/station/import/template", controller.PickupStationTemplate)
	//社区团购-自提点导入
	g.POST("/pickup/station/import", controller.PickupStationImport)
	//社区团购-自提点导入历史
	g.GET("/pickup/station/import/list", controller.PickupStationImportHistory)
	//统一接口-获取导入模板
	g.GET("/import-template", controller.ImportTemplate)
	//统一接口-导入
	g.POST("/import", controller.Import)
	//统一接口-导入历史
	g.GET("/import-history", controller.ImportHistory)

	//通用导出
	//统一接口-导入
	g.POST("/export-task-create", controller.ExportTaskCreate)
	//统一接口-导入历史
	g.GET("/export-history", controller.ExportHistory)

	//社区团购活动
	//活动列表
	g.POST("/group-activity/list", controller.GroupActivityList)
	//活动详情
	g.GET("/group-activity/query", controller.GroupActivityQuery)
	//活动新增编辑
	g.POST("/group-activity/edit", controller.GroupActivityEdit)
	//活动状态更新
	g.POST("/group-activity/status", controller.GroupActivityStatus)

	// 商城频道
	// 新增频道
	g.POST("/mall-channel/add", controller.AddMallChannel)
	// 频道列表
	g.GET("/mall-channel/list", controller.GetMallChannelList)
	// 编辑频道
	g.POST("/mall-channel/edit", controller.EditMallChannel)
	// 删除频道
	g.POST("/mall-channel/delete", controller.DeleteMallChannel)
	// 新增频道内容
	g.POST("/mall-channel/item/add", controller.AddMallChannelItem)
	// 频道内容列表
	g.GET("/mall-channel/item/list", controller.GetMallChannelItemList)
	// 编辑频道内容
	g.POST("/mall-channel/item/edit", controller.EditMallChannelItem)
	// 删除频道内容
	g.POST("/mall-channel/item/delete", controller.DeleteMallChannelItem)

	//佣金提现申请列表
	g.POST("/commission-cashout/list", controller.CommissionCashoutList)
	//佣金提现申请列表详情
	g.GET("/commission-cashout/query", controller.CommissionCashoutQuery)
	//佣金提现申请列表审核
	g.POST("/commission-cashout/audit", controller.CommissionCashoutAudit)
	//佣金提现申请导入已打款记录
	g.POST("/commission-cashout/import", controller.CommissionCashoutImport)
	//佣金提现申请导入已打款记录历史列表
	g.POST("/commission-cashout/importlist", controller.CommissionCashoutImportList)
	//佣金提现申请导出
	g.POST("/commission-cashout/export", controller.CashOutExport)

	// 饿了么门店查询
	g.GET("/shop/get-ele-storey", controller.GetElmStorey)
	// 修改饿了么门店信息
	g.POST("/shop/update-ele-storey", controller.UpdateElmStorey)
	// 查看饿了么门店营业状态
	g.GET("/shop/get-ele-busstatus", controller.GetElmBusStatus)
	// 设置饿了么门店营业状态
	g.POST("/shop/set-ele-busstatus", controller.SetElmBusStatus)
	// 查看饿了么商户公告
	g.GET("/shop/get-ele-announcement", controller.GetElmAnnouncement)
	// 设置饿了么商户公告
	g.POST("/shop/set-ele-announcement", controller.SetElmAnnouncement)

	// 获取饿了么配送范围
	g.GET("/shop/GetElmDeliveryInfo", controller.GetElmDeliveryInfo)
	// 设置饿了么配送范围
	g.POST("/shop/UpDateElmDelivery", controller.UpDateElmDelivery)
}

func paycenterGroup(e *echo.Group) {
	g := e.Group("/paycenter")
	g.GET("/paymethod/config", controller.PayConfig)
	g.POST("/paymethod/set", controller.PaySet)
}

func productGroup(e *echo.Group) {
	g := e.Group("/product")

	g.POST("/brand/new", controller.NewBrand)
	g.GET("/brand/list", controller.QueryBrand)
	g.GET("/brand/del", controller.DelBrand)
	g.POST("/spec/new", controller.NewSpec)
	g.GET("/spec/list", controller.QuerySpec)
	g.GET("/spec/del", controller.DelSpec)
	g.POST("/specvalue/new", controller.NewSpecValue)
	g.GET("/specvalue/list", controller.QuerySpecValue)
	g.GET("/specvalue/del", controller.DelSpecValue)
	g.POST("/attr/new", controller.NewAttr)
	g.GET("/attr/list", controller.QueryAttr)
	g.GET("/attr/del", controller.DelAttr)
	g.POST("/attrvalue/new", controller.NewAttrValue)
	g.GET("/attrvalue/list", controller.QueryAttrValue)
	g.GET("/attrvalue/del", controller.DelAttrValue)
	g.POST("/type/new", controller.NewType)
	g.GET("/type/list", controller.QueryType)
	g.GET("/type/del", controller.DelType)
	g.GET("/product/get-product-tags", controller.GetProductTags)
	g.POST("/product/new", controller.NewProduct)
	g.GET("/product/list", controller.QueryProduct)
	g.GET("/product/del", controller.DelProduct)
	g.GET("/product/edit-query", controller.EditQueryProductOne)
	g.GET("/product/edit-query-channel", controller.EditQueryChannelProductOne)
	g.GET("/attr/query", controller.QueryProAttr)
	g.GET("/product/sku", controller.QuerySku)
	g.POST("/media-class/list", controller.MediaCLassList)
	g.POST("/media-item/list", controller.MediaItemList)
	g.POST("/media-item/upload", controller.MediaUpload)
	g.POST("/media-item/save", controller.MediaInfoSave)
	g.POST("/media-item/del", controller.MediaItemDel)
	g.POST("/media-class/del", controller.MediaClassDel)
	g.GET("/product/product-sku", controller.QueryProductSku)
	g.GET("/product-sku/list", controller.ListProductSku)
	//g.GET("/product/edit-isuse", controller.EditProductIsUse)
	g.GET("/product/copy-channel", controller.CopyProductToChannelProduct)
	g.GET("/channel-product/list", controller.QueryChannelProduct)
	g.POST("/channel-product/store-product-up", controller.QueryChannelProductUp)
	g.GET("/channel-product/export", controller.QueryChannelProductExport)
	g.POST("/channel-product/edit", controller.EditChannelProduct)
	g.POST("/channel-product/specialty", controller.EditChannelProductSpecialty)

	g.GET("/channel-product/store-product", controller.QueryChannelStoreProduct)
	g.POST("/channel-product/channel-up", controller.ChannelProductUp) // v6.27.1
	g.POST("/channel-product/one-key-up", controller.ChannelProductOneKeyUp)
	g.POST("/channel-product/channel-status-refresh", controller.ChannelProductStatusRefresh) //v6.27.1
	g.POST("/channel-product/channel-refresh", controller.ChannelProductRefresh)
	g.POST("/channel-product/stock-refresh", controller.ChannelProductStockRefresh)

	g.GET("/channel-product/store-product-group", controller.QueryChannelStoreProductGroupByFinanceCodeCount)
	g.POST("/channel-product/batch-new", controller.ChannelProductBatchNew)
	g.POST("/channel-product/batch-update", controller.ChannelProductBatchUpdate)
	g.POST("/channel-product/batch-up", controller.ChannelProductBatchUp)
	g.GET("/all/export", controller.ExportAllProduct)
	g.GET("/get-update-field", controller.GetUpdateFieldByChannelId)
	g.GET("/channel-product/product-export", controller.AllChannelProductExport)

	g.GET("/channel-product/channel-category-with-count", controller.ChannelCategoryWithCount) // 渠道商品库侧边分类
	g.GET("/channel-products", controller.ChannelProductList)
	g.GET("/min-count-update", controller.UpdateMinOrderCount)

	g.GET("/channel-exception-products", controller.ChannelProductExceptionList)
	// 渠道商品库，附加库存、价格
	g.GET("/channel-products/count", controller.ChannelProductCount) // 渠道商品库商品计数

	g.POST("/channel-product/price-store", controller.ChannelPriceStore)
	g.POST("/channel-product/count-by-price", controller.ChannelCountByPrice)

	//管家商品库
	g.GET("/gj-product/edit-query", controller.EditQueryGjProductOne)
	g.POST("/gj-product/edit", controller.EditGjProduct)

	//商品分类
	g.POST("/category/new", controller.NewCategory)
	g.GET("/category/list", controller.QueryCategory)
	g.GET("/category/del", controller.DelCategory)
	g.GET("/category/menu", controller.GetCategoryList)
	g.POST("/channel-category/new", controller.NewChannelCategory)
	g.GET("/channel-category/list", controller.QueryChannelCategory)
	g.GET("/channel-category/del", controller.DelChannelCategory)
	g.GET("/channel-category/sort", controller.SortChannelCategory)

	//分类导航
	g.POST("/category-nav/new", controller.NewCategoryNav)
	g.GET("/category-nav/list", controller.QueryCategoryNav)

	//ERP
	g.POST("/erp/new", controller.NewErp)
	g.GET("/erp/list", controller.QueryErp)
	g.GET("/erp/del", controller.DelErp)
	g.GET("/erp/active", controller.ActiveErp)

	//TAG
	g.POST("/tag/new", controller.NewTag)
	g.GET("/tag/list", controller.QueryTag)
	g.GET("/tag/del", controller.DelTag)

	//任务服务 35互联网医院导入  36互联网医院导出  37仓库白名单导入  38仓库白名单导出
	g.POST("/product/CreateBatchTask", controller.CreateBatchTask)
	g.POST("/product/DeleteTask", controller.DeleteTask)
	g.GET("/product/GetTaskList", controller.GetTaskList)
	g.GET("/product/RollBackTask", controller.RollBackTask)
	//过期的虚拟商品自动下架任务
	g.GET("/product/expireProductDown", controller.TestExpireProductDown)

	//商品关联
	g.POST("/sku-rel-list", controller.SkuRelList)
	g.POST("/product/getRelationList", controller.ListGoodsRelevance)
	g.POST("/product/addGoodsRelation", controller.NewGoodsRelevance)
	g.POST("/product/deleteGoodsRelation", controller.DeleteGoodsRelevance)
	g.POST("/product/deleteGoodsRelationSku", controller.DeleteGoodsRelevanceSku)
	g.POST("/bbc/change-notify", controller.BbcSkuChangeNotify)

	//根据财务编码查询该门店下的所有商品价格并新增到同步（查询北京接口）
	g.POST("/product/searchAndInsertSyncPrice", controller.SearchAndInsertSyncPrice)

	// 前置仓商品价格管理
	g.GET("/channel-product/a8-price/import", controller.ImportA8Price)
	g.GET("/channel-product/a8-price/export", controller.ExportA8Price)
	g.GET("/channel-product/a8-price/record", controller.QueryQzcOperationRecord)
	g.GET("/channel-product/a8-price/list", controller.QueryQzcPriceList)
	g.POST("/channel-product/a8-price/update", controller.UpdateQzcPrice)
	g.POST("/channel-product/a8-price/delete", controller.DelQzcPrice)

	// 手动拉取 前置仓（虚拟仓）单仓多SKU同步库存
	g.POST("/operate/manual-stock", controller.ManualStock)
	g.GET("/operate/manual-stock/record-list", controller.ManualStockRecordList)
	//开票设置
	g.POST("/invoice/set", controller.InvoiceSet)
	g.GET("/invoice/info", controller.InvoiceSetInfo)
	//分类同步
	g.GET("/channel-product/category-sync/import", controller.ImportSyncCategoryShop)
	//取消分类同步任务
	g.GET("/channel-product/cancel-category-task", controller.CancelChannelCategoryTask)
	g.GET("/channel-product/cancel-category-third-task", controller.CancelChannelCategoryThirdTask)

	// 下架子龙无效(已停用或不存在)商品
	g.GET("/offshelf-zilong-invalid-product", controller.OffshelfZilongInvalidProduct)

	// oms新增商品同步到阿闻
	g.POST("/add-product/oms", controller.AddProductFromOms)

	//互联网医院
	g.POST("/hospital-product/price-import", controller.HospitalProductPriceImport) //商品价格导入
	g.GET("/hospital-product/price-list", controller.HospitalProductPriceList)      //商品价格列表
	g.POST("/hospital-product/price-del", controller.HospitalProductPriceDel)       //商品价格移除
	g.POST("/hospital-product/price-edit", controller.HospitalProductPriceEdit)     //商品价格编辑
	g.GET("/hospital-product/price-export", controller.ExportHospitalProductPrice)  //互联网医院商品价格导出
	// 第三方门店信息同步，批量开店闭店操作（暂停/开启营业）
	g.POST("/channel-product/open-close-shop", controller.ThirdChannelOpenOrCloseShop)
	g.GET("/channel-product/open-close-shop-task", controller.OpenOrCloseShopTask)

	// 渠道多商品单门店批量接口
	g.POST("/channel-product/add-up-down-task", controller.AddUpOrDownTask)

	g.GET("/mall-product/price-list", controller.MallProductPriceList)  //商品价格列表
	g.POST("/mall-product/price-del", controller.MallProductPriceDel)   //商品价格移除
	g.POST("/mall-product/price-edit", controller.MallProductPriceEdit) //商品价格编辑

	g.GET("/category/move-product", controller.MoveProduct) //商品移动

	// 删除渠道商品的接口
	g.POST("/channel-product/delete", controller.DeleteChannelProduct)
	// 查询商品的操作记录信息
	g.GET("/channel-product/records", controller.ChannelProductRecords)
	g.GET("/channel-product/elm-error/list", controller.ChannelProductElmRecords)
	g.POST("/channel-product/elm-error/del", controller.ChannelProductElmDelete)

	//平台商品库修改货号记录查询
	g.GET("/product/records", controller.ProductRecords)

	// 子龙商品列表
	g.POST("/zilong-list", controller.ProductZiLongList)
	// 病症搜索
	g.GET("/diagnose-dic", controller.ProductDiagnoseDic)
	// 获取药品用量对应的宠物类型
	g.GET("/pet-type", controller.ProductPetType)
	//查询子龙商品信息
	g.GET("/zlproduct/list", controller.QueryZlProducts)
}

func jddjGroup(e *echo.Group) {
	g := e.Group("/jddj")

	g.GET("/brand/list", controller.JddjBrandList)
	g.GET("/category/list", controller.JddjCategoryList)
	g.POST("/order/jddjAdjustOrder", controller.JddjAdjustOrder)

	g.GET("/order/selfmention", controller.JddjSelfMention)             // 拣货完成且顾客自提接口
	g.GET("/order/checkselfpickcode", controller.JddjCheckSelfPickCode) // 订单自提码核验
	g.POST("/category/list/update", controller.JddjUpdateCategoryList)
	g.GET("/retail/getSpTagIds", controller.JddjRetailGetSpTagIds)
	g.POST("/goods/list/update", controller.JddjUpdateGoodsList)
}

func mtGroup(e *echo.Group) {
	g := e.Group("/meituan")

	g.GET("/retail/getSpTagIds", controller.RetailGetSpTagIds)
	g.GET("/category/attr/list", controller.CategoryAttrList)
	g.GET("/category/attr/value/list", controller.CategoryAttrValueList)
	//据门店financecode获取该门店支持的配送范围
	g.POST("/shop/area/query", controller.GetMpShopAreaQuery)
}

func elmGroup(e *echo.Group) {
	g := e.Group("/elm")

	g.GET("/retail/getSpTagIds", controller.ElmRetailGetSpTagIds)
	//呼叫饿了么配送
	g.POST("/order/callDelivery", controller.ElmOrderCallDelivery)
}

func promotionGroup(e *echo.Group) {
	var g = e.Group("/Promotion")
	g.GET("/QueryPromotionById", controller.QueryReachReduceById)                                 // 根据活动ID查询活动列表
	g.GET("/QueryPromotionDetailById", controller.QueryPromotionDetailById)                       // 根据活动ID查询活动详细信息
	g.POST("/QueryPromotionByShopIds", controller.QueryReachReduceByShopIds)                      // 根据店铺查询活动列表
	g.POST("/QueryPromotionSummary", controller.QueryPromotionSummary)                            // 查询店铺活动信息
	g.POST("/AddPromotion", controller.AddPromotion)                                              //新增活动
	g.GET("/QueryPromotionProduct", controller.QueryPromotionProduct)                             // 根据活动Id查询活动参与商品信息
	g.GET("/QueryOptionalSelectPromotionProduct", controller.QueryOptionalSelectPromotionProduct) // 根据活动Id查询活动可以再参与的商品信息(编辑活动时查询可选的商品信息)
	g.POST("/UpdatePromotionIsEnable", controller.UpdateReachReduceIsEnable)                      // 更新活动状态
	g.POST("/UpdatePromotion", controller.UpdatePromotion)                                        // 更新活动基础信息
	g.POST("/DeletePromotionShop", controller.DeleteReachReduceShopByShopIds)                     // 删除活动与店铺关联关系
	g.POST("/DeletePromotionShopByShopIds", controller.DeleteReachReduceShopByQuery)              // 根据条件删除活动与店铺关联关系
	g.GET("/operate-list", controller.QueryPromotionOperateList)                                  //活动操作日志列表

	g.POST("/AddReduceDelivery", controller.AddReduceDelivery)                   //满减运费--新增
	g.POST("/UpdateReduceDelivery", controller.UpdateReduceDelivery)             //满减运费--修改
	g.POST("/GetReduceDeliveryList", controller.GetReduceDeliveryList)           //满减运费--列表
	g.GET("/GetReduceDeliveryById", controller.GetReduceDeliveryById)            //满减运费--获取详情
	g.POST("/DeletePromotionShopById", controller.DeletePromotionShopById)       //满减运费--根据店铺关联ID删除活动与店铺的关联
	g.POST("/DeletePromotionShopByQuery", controller.DeletePromotionShopByQuery) //满减运费--根据查询条件删除活动与店铺的关联

	g.POST("/QueryPromotionProducByQuery", controller.QueryPromotionProducByQuery) //限时折扣--分页获取列表
	g.GET("/QueryPromotionProducById", controller.QueryPromotionProducById)        //限时折扣--获取详情
	//g.POST("/AddTimediscountPromotion", controller.AddTimediscountPromotion)                     //限时折扣--新建
	g.POST("/BatchAddTimediscountPromotion", controller.BatchAddTimediscountPromotion)           //限时折扣--批量新建（重构）
	g.POST("/UpdateTimeDiscountPromotion", controller.UpdateTimeDiscountPromotion)               //限时折扣--更新
	g.POST("/DeleteTimeDiscountProductByQuery", controller.DeleteTimeDiscountProductByQuery)     //限时折扣--删除店铺和活动的关联关系（根据条件删除）
	g.POST("/DeleteTimeDiscountProductByShopIds", controller.DeleteTimeDiscountProductByShopIds) //限时折扣--根据条件批量删除店铺和活动的关联关系（根据ids删除）

	g.POST("/PromotionConfigurationCreateInfo", controller.PromotionConfigurationCreateInfo) //批量创建活动配置信息
	g.POST("/PromotionConfigurationUpdateInfo", controller.PromotionConfigurationUpdateInfo) //更新活动配置信息
	g.GET("/GetPromotionConfigurationDetail", controller.GetPromotionConfigurationDetail)    //根据shopIds获取信息
	g.POST("/PromotionConfigurationReplaceInfo", controller.PromotionConfigurationReplaceInfo)

	g.GET("/QueryPromotionTask", controller.QueryPromotionTask)   //活动创建任务列表
	g.GET("/ExportPromotionTask", controller.ExportPromotionTask) // 任务明细导出

	g.POST("/playbill/create", controller.CreatePlayBill)
	g.GET("/playbill/alllist", controller.GetPlayBillList)
	g.GET("/playbill/singlelist", controller.GetSinglePlayBillList)
	g.POST("/playbill/detail-delete", controller.DeleteSinglePlayBillDetail) //删除推广海报
	g.GET("/playbill/download", controller.DownloadPlayBill)

	//宠物贴士
	var petTips = e.Group("/pettips")
	petTips.GET("/query", controller.QueryListPetTips)
	petTips.GET("/get", controller.GetPetTips)
	petTips.POST("/add", controller.AddPetTips)
	petTips.POST("/update", controller.UpdatePetTips)
	petTips.POST("/delete", controller.DeletePetTips)

	//标签数据
	var tags = e.Group("/tags")
	tags.GET("/product", controller.QueryProductTags)
	tags.GET("/pettips", controller.QueryPetTipsTags)
	tags.GET("/select-values", controller.QueryTagSelectValues)
	tags.GET("/content", controller.QueryContentTag)
	tags.POST("/operate", controller.TagAdd)
	tags.PUT("/operate", controller.TagUpdate)
	tags.DELETE("/operate", controller.TagDelete)

	insurance := e.Group("/Promotion/Insurance")

	insurance.GET("/ChoiceSku", controller.InsuranceChoiceSku)
	insurance.GET("/List", controller.InsuranceList)
	insurance.GET("/Detail", controller.InsuranceDetail)
	insurance.POST("/Store", controller.InsuranceStore)
	insurance.POST("/Cancel", controller.InsuranceCancel)

	g.GET("/", func(c echo.Context) error {
		var buffer bytes.Buffer
		buffer.WriteString("<h1>Marketing Api is running.....</h1>")
		routes := c.Echo().Routes()
		if len(routes) > 0 {
			buffer.WriteString("<ul>")
			for _, route := range routes {
				buffer.WriteString(fmt.Sprintf("<li>%s</li>", route.Path))
			}
			buffer.WriteString("</ul>")
		}
		return c.HTML(http.StatusOK, buffer.String())
	}) //根目录，测试用
}

func refundGroup(e *echo.Group) {
	g := e.Group("/refund")

	g.POST("/apply", controller.RefundOrderApply)
	g.POST("/cancel", controller.RefundOrderCancel)
	g.POST("/answer", controller.RefundOrderAnswer)
	g.POST("/pay", controller.RefundOrderPay)

	// 查看退款的操作记录
	g.GET("/pay/records", controller.RefundOrderPayRecords)

	// 查询退款单列表
	g.GET("/pay/getRefundOrderSn", controller.RefundOrderSnList)

	//手动退款
	g.POST("/pay/Manual", controller.RefundOrderManual)

	// 手动已完成
	g.POST("/pay/finish", controller.RefundOrderFinish)

}

func marketcenterGroup(e *echo.Group) {
	g := e.Group("/market")

	g.GET("/activity/list", controller.QueryActivity)
	g.GET("/activity/del", controller.DelActivity)
	g.GET("/activity/status-refresh", controller.StatusRefreshActivity)
	g.POST("/activity/new", controller.NewActivity)
	g.GET("/activity/setting/query", controller.QueryActivitySetting)
	g.POST("/activity/setting/save", controller.SaveActivitySetting)
	g.POST("/activity/coupon/new", controller.NewCouponActivity)
	g.GET("/activity/coupon/list", controller.CouponList)
	g.GET("/activity/coupon/end", controller.EndCouponActivity)
	g.GET("/activity/voucher/list", controller.VoucherList)
	g.GET("/activity/voucher/export", controller.VoucherListExport)
	g.POST("/activity/voucher/add", controller.AddCouponCount)

	//团购活动
	g.GET("/group-buy/list", controller.GetGroupBuyList)     //团购活动列表
	g.GET("/group-buy/detail", controller.GetGroupBuyDetail) //团购活动信息
	g.PUT("/group-buy/stop", controller.StopGroupBuy)        //团购活动/停止
	g.PUT("/group-buy", controller.UpdateGroupBuy)           //团购活动添加/编辑
	g.POST("/group-buy", controller.CreateGroupBuy)          //团购活动添加

	//团购活动商品
	g.GET("/group-buy/product/list", controller.GetGroupBuyProductList)                  //团购活动商品列表
	g.GET("/group-buy/product/detail", controller.GetGroupBuyProductDetail)              //团购活动商品信息
	g.GET("/group-buy/product/select-list", controller.GetGroupBuyUPetProductSelectList) //获取可以参加拼团活动的阿闻商城的商品
	g.POST("/group-buy/product", controller.CreateGroupBuyProduct)                       //团购活动商品添加
	g.PUT("/group-buy/product", controller.UpdateGroupBuyProduct)                        //团购活动商品编辑
	g.DELETE("/group-buy/product", controller.DeleteGroupBuyProduct)                     //删除团购活动商品
	g.POST("/group-buy/bookmark", controller.BookmarkGroupBuyProduct)                    //商品异常标识
	//周期购活动
	g.GET("/cycle-buy/list", controller.GetCycleBuyList)                       //周期购活动列表
	g.GET("/cycle-buy/detail", controller.GetCycleBuyDetail)                   //周期购活动信息
	g.PUT("/cycle-buy/stop", controller.StopCycleBuy)                          //周期购活动/停止
	g.PUT("/cycle-buy", controller.UpdateCycleBuy)                             //周期购活动编辑
	g.POST("/cycle-buy", controller.CreateCycleBuy)                            //周期购活动添加
	g.GET("/cycle-buy/unship/statistics", controller.CycleBuyUnshipStatistics) //统计周期购活动每个月未发货商品数

	//周期购活动商品
	g.GET("/cycle-buy/product/list", controller.GetCycleBuyProductList)                  //周期购活动商品列表
	g.GET("/cycle-buy/product/detail", controller.GetCycleBuyProductDetail)              //周期购活动商品信息
	g.GET("/cycle-buy/product/select-list", controller.GetCycleBuyUPetProductSelectList) //获取可以参加周期购活动的阿闻商城的商品
	g.POST("/cycle-buy/product", controller.CreateCycleBuyProduct)                       //周期购活动商品添加
	g.PUT("/cycle-buy/product", controller.UpdateCycleBuyProduct)                        //周期购活动商品编辑
	g.DELETE("/cycle-buy/product", controller.DeleteCycleBuyProduct)                     //删除周期购活动商品
	g.POST("/cycle-buy/bookmark", controller.BookmarkCycleBuyProduct)                    //异常标识
	//预售活动商品
	g.GET("/book-buy/product/list", controller.GetBookBuyProductList)                  //预售活动商品列表
	g.GET("/book-buy/product/detail", controller.GetBookBuyProductDetail)              //预售活动商品信息
	g.GET("/book-buy/product/select-list", controller.GetBookBuyUPetProductSelectList) //获取可以参加预售活动的阿闻商城的商品
	g.POST("/book-buy/product", controller.CreateBookBuyProduct)                       //预售活动商品添加
	g.PUT("/book-buy/product", controller.UpdateBookBuyProduct)                        //预售活动商品编辑
	g.DELETE("/book-buy/product", controller.DeleteBookBuyProduct)                     //删除预售活动商品
	g.POST("/book-buy/bookmark", controller.BookmarkBookBuyProduct)                    //异常标识
	//秒杀活动
	g.GET("/seckill/list", controller.GetSeckillList)     //秒杀活动列表
	g.GET("/seckill/detail", controller.GetSeckillDetail) //秒杀活动信息
	g.POST("/seckill/stop", controller.StopSeckill)       //秒杀活动停止
	g.POST("/seckill/show", controller.ShowSeckill)       //秒杀活动停止
	g.POST("/seckill/edit", controller.UpdateSeckill)     //秒杀活动编辑
	g.POST("/seckill/add", controller.CreateSeckill)      //秒杀活动添加
	g.POST("/setting", controller.SaveDiscountSetting)    //保存
	g.GET("/setting", controller.GetDiscountSetting)      //获取促销活动配置

	//秒杀活动商品
	g.GET("/seckill/product/list", controller.GetSeckillProductList)                  //秒杀活动商品列表
	g.GET("/seckill/product/detail", controller.GetSeckillProductDetail)              //秒杀活动商品信息
	g.GET("/seckill/product/select-list", controller.GetSeckillUPetProductSelectList) //获取可以参加秒杀活动的阿闻商城的商品
	g.POST("/seckill/product/add-or-edit", controller.CreateOrUpdateSeckillProduct)   //创建和编辑秒杀活动商品
	g.POST("/seckill/product/delete", controller.DeleteSeckillProduct)                //删除秒杀活动商品

	//新人专享活动商品
	g.GET("/new-buy/product/list", controller.GetNewBuyProductList)                  //新人专享活动商品列表
	g.GET("/new-buy/product/detail", controller.GetNewBuyProductDetail)              //新人专享活动商品信息
	g.GET("/new-buy/product/select-list", controller.GetNewBuyUPetProductSelectList) //获取可以参加新人专享活动的阿闻商城的商品
	g.POST("/new-buy/product", controller.CreateNewBuyProduct)                       //新人专享活动商品添加
	g.PUT("/new-buy/product", controller.UpdateNewBuyProduct)                        //新人专享活动商品编辑
	g.DELETE("/new-buy/product", controller.DeleteNewBuyProduct)                     //删除新人专享活动商品
	g.GET("/new-buy/product/import-list", controller.GetNewBuyProductImportList)     //新人专享活动商品导入列表
	g.POST("/new-buy/product/import", controller.ImportNewBuyProduct)                //新人专享活动商品导入

	//新人专享活动
	g.GET("/new_buy/detail", controller.GetNewBuyDetail)              //新人专享活动信息
	g.PUT("/new_buy", controller.UpdateNewBuy)                        //新人专享活动编辑
	g.POST("/new_buy", controller.CreateNewBuy)                       //新人专享活动添加
	g.POST("/new_buy/voucher/add", controller.AddNewPeopleVoucher)    //添加新人专享券
	g.GET("/new_buy/voucher/get", controller.GetNewPeopleVoucherList) //添加新人专享券

}

func petmillionsGroup(e *echo.Group) {
	g := e.Group("/petmillions")
	g.POST("/vaccine/add", controller.AddVaccine)
	g.POST("/vaccine/update", controller.UpdateVaccine)
	g.POST("/vaccine/updatestate", controller.UpdateVaccineState)
	g.GET("/vaccine/get", controller.GetVaccine)
	g.GET("/vaccine/list", controller.GetVaccineList)
	g.POST("/vaccine/export", controller.ExportVaccineList)

	g.GET("/marchent/alltype", controller.GetAllTypeList)

	g.POST("/marchent/import", controller.ImportMarchentInfo)
	g.GET("/marchent/download", controller.DownloadMarchentInfo)
	g.POST("/marchent/export", controller.ExportMarchentList)

	g.POST("/marchent/add", controller.CreateMarchent)
	g.GET("/marchent/get", controller.GetMarchent)
	g.GET("/marchent_base/get", controller.GetMarchentBase)
	g.POST("/marchent_base/update", controller.UpdateBaseMarchent)
	g.POST("/marchent/update", controller.UpdateMarchent)
	g.POST("/marchent_state/update", controller.UpdateMarchentState)
	g.GET("/marchent/list", controller.GetMarchentList)
	g.GET("/marchent_detail/get", controller.GetMarchentDetail)
	g.POST("/marchent_qualify/update", controller.UpdateMarchentQualify)
	g.POST("/marchent_management/update", controller.UpdateMarchentManagement)
	g.POST("/marchent_operate/update", controller.UpdateMarchentOperate)
	g.POST("/marchent_finance/update", controller.UpdateMarchentFinance)
	g.POST("/marchent_member/update", controller.UpdateMarchentMember)
	g.GET("/marchent_qualify/get", controller.GetMarchentQualify)
	g.GET("/marchent_management/get", controller.GetMarchentManagement)
	g.GET("/marchent_operate/get", controller.GetMarchentOperate)
	g.GET("/marchent_finance/get", controller.GetMarchentFinance)
	g.GET("/marchent_member/get", controller.GetMarchentMember)
	g.GET("/marchent_pet/list", controller.GetMarchentPet)
	g.GET("/marchent_pet/export", controller.ExportMarchentPet)
	g.GET("/marchent_deliveryrecord/list", controller.GetMarchentDeliveryRecord)
	g.GET("/marchent_deliveryrecord/export", controller.ExportMarchentDeliveryRecord)
	g.POST("/breeds/get", controller.GetPetBreeds)
	g.GET("/marchent_pet/referral-records", controller.GetMarchentPetReferralRecords)
	g.GET("/marchent_pet/referral-records-export", controller.GetMarchentPetReferralRecordsExport)
}

func advertisementGroup(e *echo.Group) {
	g := e.Group("/advertisement")
	//新增/编辑广告
	g.POST("/edit", controller.AdvertisementEdit)
	//获取广告列表
	g.GET("/list", controller.AdvertisementList)
	//获取广告详情
	g.GET("/info", controller.AdvertisementGet)
	//停止广告
	g.POST("/stop", controller.AdvertisementStop)
	//新增/编辑商详广告
	g.POST("/goods-detail/edit", controller.AdvertisementGoodsDetailEdit)
	//获取商详广告
	g.GET("/goods-detail/info", controller.AdvertisementGoodsDetailGet)
}

func intelligenceCenterGroup(e *echo.Group) {
	g := e.Group("/Intelligence")
	g.GET("/warehouseData", controller.WarehouseData)
}

// SCRM企业微信标签
func scrmGroup(e *echo.Group) {
	g := e.Group("/crm")
	g.POST("/create_flag", controller.CreateFlag)
	g.POST("/get_flag_list", controller.GetFlagList)
	g.POST("/delete_flag", controller.DeleteFlag)
	g.POST("/get_flag_info", controller.GetFlagInfo)
	g.POST("/get_flag_group_list", controller.GetFlagGroupList)
	g.POST("/update_flag_group", controller.UpdateFlagGroup)
	g.POST("/delete_flag_group", controller.DeleteFlagGroup)
	e.POST("/boss/crm/synchronize_to_wechat", controller.SynchronizeToWeChat)
	g.POST("/get_customer_list", controller.GetCustomerList)
	g.POST("/get_customer_detail", controller.GetCustomerDetail)
	g.POST("/export_customer_list", controller.CreateCrmTask)
	g.GET("/get_task_list", controller.GetCrmTaskList)

}

// 流浪救助年夜饭
func marketcenterYearGroup(e *echo.Group) {
	g := e.Group("/market")
	//捐赠用户列表
	g.GET("/activity/succour_user", controller.QuerySuccour)
	//救助用户编辑
	g.POST("/activity/edit_user", controller.QueryEditUser)

	//二维码群列表
	g.GET("/activity/qrcode_list", controller.QueryQrcode)
	//二维码添加
	g.POST("/activity/qrcode_add", controller.QueryQrcodeAdd)
	//二维码启用
	g.POST("/activity/qrcode_set", controller.QueryQrcodeSet)

	//救助站列表
	g.GET("/activity/salvation_list", controller.QuerySalvation)
	//救助站添加修改
	g.POST("/activity/salvation_add", controller.QuerySalvationAdd)
	g.POST("/activity/salvation_set", controller.QuerySalvationSet)
	//查看收货凭证
	g.GET("/activity/salvation_receiving", controller.QuerySalvationReceiving)
	//收货凭证添加、修改
	g.POST("/activity/salvation_receiving_add", controller.QuerySalvationReceivingAdd)
	//设置捐赠限制
	g.POST("/activity/donate_set", controller.QueryDonateSet)

	//设置访问二维码个数限制列表
	g.GET("/activity/qrcode_limit_list", controller.QueryQrcodeLimit)
	//修改
	g.POST("/activity/qrcode_edit", controller.QueryQrcodeLimitEdit)
}

// 库存可视化
func stockVisualGroup(e *echo.Group) {
	g := e.Group("/stock-visual")

	//区域仓库存列表
	g.GET("/region/list", controller.RegionWarehouseStockList)

	//前置仓库存列表
	g.GET("/prepose/list", controller.PreposeWarehouseStockList)

	// 区域仓库存导出
	g.GET("/region/export", controller.RegionWarehouseStockExport)

	// 前置仓库存导出
	g.GET("/prepose/export", controller.PreposeWarehouseStockExport)

	// 效期列表查询
	g.GET("/effective/search", controller.EffectiveList)
	// 导入效期数据
	g.POST("/effective/import", controller.EffectiveImport)
	// 效期列表导出
	g.GET("/effective/export", controller.EffictiveExportExcle)

	// 选取渠道商品
	g.GET("/product/search", controller.GetChannelProduct)

	// 仓库规则新增
	g.POST("/warehouse-rule-configuration/add", controller.WarehouseRuleConfigurationAdd)
	// 仓库规则查询
	g.GET("/warehouse-rule-configuration/search", controller.WarehouseRuleConfiguration)
	// 商品规则设置
	g.POST("/product-rule-configuration/add", controller.ProductRuleConfigurationAdd)
	// 查询商品
	g.GET("/product-rule-configuration/search", controller.ProductRuleConfiguration)
	// 删除商品
	g.POST("/product-rule-configuration/delete", controller.ProductRuleConfigurationDelete)
	// 批量启用禁用商品的状态
	g.POST("/product-rule-configuration/update", controller.BatchUseOrDisable)

	// 任务查询
	g.GET("/task/list", controller.GetStockVisualTaskList)

	// 模板导出
	g.GET("/excel/template", controller.DownloadTemplateExcel)
	// 读取excle
	g.POST("/excel/upload", controller.UploadTemplateExcel)

	// 查询权限设置信息列表
	g.GET("/auth/list", controller.AuthUserInfoList)
	// 根据id查询权限设置信息详情
	g.GET("/auth/detail", controller.AuthUserInfoDetail)
	// 新增权限设置
	g.POST("/auth/add", controller.AddAuthUserInfo)
	// 编辑权限设置
	g.POST("/auth/update", controller.UpdateAuthUserInfo)

	// 查询权限设置仓库列表
	g.GET("/warehouse/list", controller.GetStockVisualWarehouseList)

	// 初始化全量任务
	g.GET("/effective/InitDiscountTask", controller.InitDiscountTask)
	// 增量初始化promotion任务
	g.GET("/effective/IncrementPromotionDiscountTask", controller.IncrementPromotionDiscountTask)

	// 新增按钮权限
	g.POST("/auth/AddButtonPermissions", controller.AddButtonPermissions)

	// 查询用户是否有按钮权限
	g.GET("/auth/GetButtonPermissions", controller.GetButtonPermissions)
}

func watermarkGroup(e *echo.Group) {
	g := e.Group("/watermark")
	g.GET("/list", controller.WatermarkList)
	g.GET("/detail", controller.GetWatermark)
	g.POST("/save", controller.SaveWatermark)
	//水印关联的商品列表
	g.GET("/goods/list", controller.WatermarkGoodsList)
	g.POST("/goods/add", controller.WatermarkGoodsAdd)
	g.POST("/goods/price", controller.WatermarkGoodsPrice)
	g.POST("/goods/delete", controller.WatermarkGoodsDelete)
	//批量导入GOODS
	g.POST("/goods/import", controller.WatermarkGoodsImport)
	//水印添加商品时的商品列表项
	g.GET("/goods/search", controller.SearchGoods)
	//终止水印
	g.GET("/stop", controller.StopWatermark)
	//阿里云图片上传
	g.POST("/upload", controller.UploadFile)
}

func fileUploadRecordGroup(e *echo.Group) {
	g := e.Group("/uploadRecord")
	g.GET("/list", controller.GetFileUploadRecords)
	g.POST("/add", controller.AddFileUploadRecord)
	g.POST("/update", controller.UpdateFileUploadRecordStatus)

}

func diagnoseGroup(e *echo.Group) {
	g := e.Group("/diagnose")
	//问诊列表
	g.POST("/list", controller.DiagnoseList)
	//问诊列表
	g.POST("/export", controller.ExportDiagnoseList)
	//问诊详情
	g.GET("/details", controller.DiagnoseDetails)
	//医生接单管理-医生列表
	g.POST("/doctor/list", controller.ScrmDoctorList)
	//医生接单管理-禁用、启用
	g.POST("/doctor/isforbidden", controller.DoctorIsForbidden)
	//医生接单设置
	g.POST("/config/set", controller.DiagnoseSet)
	//获取 系统医生接单设置（医生排班时间内不支持接单）
	g.GET("/system/info", controller.DiagnoseSystemInfo)
	//新增互联网医生
	g.POST("/doctor/internet/add", controller.DiagnoseInternetDoctorAdd)
	//获取互联网医生信息
	g.POST("/doctor/internet/info", controller.InternetDoctorInfo)
	//互联网医生列表
	g.POST("/doctor/internet/list", controller.DiagnoseInternetDoctorList)
	//删除互联网医生
	g.POST("/doctor/internet/forbidden", controller.InternetDoctorForbidden)
	//互联网医生在线、离线状态变更
	g.POST("/doctor/online/change", controller.DoctorOnlineChange)
	//接单后台-医生信息和状态
	g.POST("/doctor/status", controller.DoctorStatus)
	//接单后台-搜索医院
	g.POST("/hospital/list", controller.HospitalList)
	//接单后台-搜索医生
	g.POST("/scrmdoctor/list", controller.ScrmDoctorList)
	//互联网医生管理-根据手机号匹配医生信息
	g.POST("/acpuser/info", controller.AcpUserInfo)
	//医生接单管理-医生列表
	g.POST("/doctor/export", controller.ScrmDoctorListExport)
	//获取最新一条订单信息
	g.GET("/order/get-latest-order", controller.DiagnoseGetLatestOrder)
	//消息获取
	g.GET("/im/get-message", controller.GetMessage)
	//用户注册
	g.POST("/im/user-registration", controller.UserRegistration)
	//追加回复次数自增
	g.POST("/doctor/add-finish-message", controller.DiagnoseAddFinishMessage)
	g.GET("/list", controller.GetFileUploadRecords)
	g.POST("/add", controller.AddFileUploadRecord)
	g.POST("/update", controller.UpdateFileUploadRecordStatus)
}

func article(e *echo.Group) {
	g := e.Group("/content")
	// 文章新增，修改
	g.POST("/article/save", controller.ArticleSave)
	// 文章详情
	g.GET("/article/detail", controller.ArticleDetail)
	// 文章列表
	g.GET("/article/list", controller.ArticleList)
	// 文章类别搜索条件
	g.GET("/article/condition", controller.ArticleSearchCondition)
	// 文章下架、发布
	g.POST("/article/status", controller.ArticleStatus)

	// 类目新增，修改
	g.POST("/category/save", controller.ArticleCategorySave)
	// 类目删除
	g.POST("/category/delete", controller.ArticleCategoryDelete)
	// 类目查询
	g.GET("/category/list", controller.ArticleCategoryList)

	// 文章操作历史查询
	g.GET("/article/edit_records", controller.ArticleEditRecords)

	//获取文章统计
	g.GET("/census/article", controller.ArticleCensus)
	//获取勃林格统计
	g.GET("/census/boehringere", controller.BoehringereCensus)
	//获取医生列表
	g.GET("/queryDoctor", controller.QueryDoctorList)
}

func activityGroup(e *echo.Group) {
	g := e.Group("/dinner/2022")

	g.GET("/settings", controller.DinnerSettings)
	g.GET("/settings/value", controller.DinnerSettingValue)
	g.POST("/settings/batch", controller.DinnerSettingsBatchUpdate)
	g.POST("/settings/update", controller.SettingUpdate)

	g2 := e.Group("/activity")
	g2.DELETE("/delete", controller.ActivityDelete)
	// 活动提交审核
	g2.POST("/submit-check", controller.ActivitySubmitCheck)
	// 活动审核
	g2.POST("/check", controller.ActivityCheck)
	// 活动异常标记
	g2.POST("/exception-mark", controller.ActivityExceptionMark)
}

func dispatch(e *echo.Group) {
	g := e.Group("/dispatch")
	//同步仓库信息列表
	g.GET("/wareHouse/A8List", controller.GetA8WareHouseList)
	//门店仓库管理列表
	g.GET("/wareHouse/List", controller.GetWarehouseRelationShopList)
}

func distributionGroup(e *echo.Group) {
	g := e.Group("/distribution")

	g.GET("/categories", controller.DisCategories)
	g.GET("/spus", controller.DisSpuList)
	g.GET("/spu/detail", controller.DisSpuDetail)
	g.PATCH("/spu/update", controller.DisSpuUpdate)
	g.GET("/spu/logs", controller.DisSpuLogs)

	g.PATCH("/sku/update", controller.DisSkuUpdate)
	g.GET("/not-dis-skus", controller.DisNotDisSkus)

	g.GET("/import/template", controller.DisImportTemplate)
	g.POST("/import", controller.DisImport)
	g.GET("/import/list", controller.DisImportList)

	// 分销商品列表
	g.GET("/goods-list", controller.DisGoodsList)
	// 导出非分销的商品
	g.GET("/export-no-dis-goods", controller.ExportNoDisGoods)
	// 设置全局默认佣金
	g.POST("/set-global-commission", controller.DisSetGlobalCommission)
	// 获取全局默认佣金
	g.GET("/global-commission", controller.DisGlobalCommission)

	// 限时佣金-活动列表
	g.GET("/limit-activity/list", controller.DisLimitActivityList)
	// 限时佣金-活动新增/编辑
	g.POST("/limit-activity/operate", controller.DisLimitActivityOperate)
	// 限时佣金-活动失效
	g.POST("/limit-activity/stop", controller.DisLimitActivityStop)
	// 限时佣金-商品删除
	g.DELETE("/limit-activity/goods-delete", controller.DisLimitActivityGoodsDelete)
	// 限时佣金-活动分销商品列表
	g.GET("/limit-activity/goods-list", controller.DisLimitActivityGoodsList)
	// 限时佣金-商品限时佣金批量导入
	g.POST("/limit-activity/goods-import", controller.DisLimitActivityGoodsImport)
	// 限时佣金-活动佣金设置
	g.POST("/limit-activity/commission-set", controller.DisLimitActivityCommissionSet)
	// 限时佣金-活动日志
	g.GET("/limit-activity/log", controller.DisLimitActivityLog)
}
func regionalDivision(e *echo.Group) {
	g := e.Group("/regionalDivision")

	g.GET("/query", controller.QueryRegionalDivision)
}
func groupChat(e *echo.Group) {
	g := e.Group("/groupChat")

	g.GET("/query", controller.QueryGroupChat)
	g.POST("/edit", controller.EditGroupChat)
	g.POST("/delete", controller.DeleteGroupChat)
	g.POST("/add-join-way", controller.AddJoinWay)
	g.POST("/chat-list", controller.ChatList)
	g.GET("/wx-chat-list", controller.WxChatList)
	// 设置门店微信
	g.POST("/set-hospital-wechat", controller.SetHospitalWechat)
	// 关联、取消关联群门店
	g.POST("/link-hospital", controller.LinkHospital)
}

func SearchRoute(e *echo.Group) {
	g := e.Group("/search")

	// 热词列表
	g.GET("/hot-word", controller.HotWord)
	// 热词新增、更新
	g.POST("/hot-word-operate", controller.HotWordOperate)
	// 热词隐藏
	g.POST("/hot-word-delete", controller.HotWordDelete)

	// 默认词列表
	g.GET("/default-word", controller.DefaultWord)
	// 默认词新增、更新
	g.POST("/default-word-operate", controller.DefaultWordOperate)
	// 默认词隐藏
	g.POST("/default-word-delete", controller.DefaultWordDelete)

	//搜索词库列表
	g.GET("/keyword", controller.SearchKeyword)
	//添加搜索词库
	g.POST("/keyword", controller.SearchKeywordStore)
	//删除搜索词库
	g.DELETE("/keyword", controller.SearchKeywordDelete)
}

// v6.5.8代运营设置
func AgencyRoute(e *echo.Group) {
	g := e.Group("/agency")
	//g.GET("/downTemplate", controller.DownAgencyTemplate)
	//可上架商品导入
	g.POST("/product-import", controller.ImportAgencyProduct)

}

// 切仓相关路由
func switchWarehouseRoute(e *echo.Group) {
	feed := e.Group("/warehouse")
	feed.GET("/switch-warehouse", controller.ListSwitchWarehouseLog)
}

func LuckyDraw(e *echo.Group) {
	g := e.Group("/luckyDraw")

	g.GET("/list", controller.LuckyDrawActivityList)
	g.GET("/info", controller.LuckyDrawActivityInfo)
	g.POST("/edit", controller.EditLuckyDrawActivity)

	g.POST("/recommend", controller.LuckyDrawActivityRecommend)
}

func risk(e *echo.Group) {
	g := e.Group("/risk")
	//风控配置
	g.GET("/setting", controller.RiskSetting)
	g.POST("/setting", controller.SaveRiskSetting)
	g.GET("/user", controller.RiskUser)
	g.GET("/userlog", controller.RiskUserLog)
	g.POST("/unlock", controller.RiskUnlock)
	g.GET("/whitelist", controller.RiskWhitelist)
	g.DELETE("/whitelist", controller.DeleteRiskWhitelist)
	g.POST("/whitelist", controller.AddRiskWhitelist)
}

func posterFission(e *echo.Group) {
	g := e.Group("/poster-fission")
	g.GET("/list", controller.PosterFissionList)
	g.GET("/detail", controller.PosterFissionDetail)
	g.POST("/store", controller.PosterFissionStore)
}
