package controller

import (
	"_/proto/dac"

	"github.com/labstack/echo/v4"
	"github.com/ppkg/kit/cast"
)

// @Summary 新增商城频道
// @Tags 商城频道
// @Accept json
// @Produce plain
// @Param model body dac.AddMallChannelRequest true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router /boss/datacenter/mall-channel/add [POST]
func AddMallChannel(c echo.Context) error {
	res := &dac.Response{}
	p := &dac.AddMallChannelRequest{}
	if err := c.Bind(p); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	if len(p.Title) == 0 || len(p.SubTitle) == 0 {
		res.Code = 400
		res.Message = "标题或副标题不能为空"
		return c.JSON(400, res)
	}

	dacClient := GetDataCenterClient()
	defer dacClient.Close()
	var err error
	res, err = dacClient.RPC.AddMallChannel(dacClient.Ctx, p)
	if err != nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 获取商城频道列表
// @Tags 商城频道
// @Accept json
// @Produce plain
// @Param page_index query int true "当前页"
// @Param page_size query int true "每页显示条数"
// @Success 200 {object} dac.GetMallChannelListResponse
// @Failure 400 {object} dac.GetMallChannelListResponse
// @Router /boss/datacenter/mall-channel/list [GET]
func GetMallChannelList(c echo.Context) error {
	p := &dac.GetMallChannelListRequest{
		PageIndex: cast.ToInt64(c.QueryParam("page_index")),
		PageSize:  cast.ToInt64(c.QueryParam("page_size")),
	}
	if p.PageIndex <= 0 {
		p.PageIndex = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 10
	}
	dacClient := GetDataCenterClient()
	defer dacClient.Close()

	res, err := dacClient.RPC.GetMallChannelList(dacClient.Ctx, p)
	if err != nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 编辑商城频道
// @Tags 商城频道
// @Accept json
// @Produce plain
// @Param model body dac.EditMallChannelRequest true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router /boss/datacenter/mall-channel/edit [POST]
func EditMallChannel(c echo.Context) error {
	res := &dac.Response{}
	p := &dac.EditMallChannelRequest{}
	if err := c.Bind(p); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	if p.ChannelId <= 0 {
		res.Code = 400
		res.Message = "频道id不能为空"
		return c.JSON(400, res)
	}
	dacClient := GetDataCenterClient()
	defer dacClient.Close()

	var err error
	res, err = dacClient.RPC.EditMallChannel(dacClient.Ctx, p)
	if err != nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 删除商城频道
// @Tags 商城频道
// @Accept json
// @Produce plain
// @Param model body dac.DeleteMallChannelRequest true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router  /boss/datacenter/mall-channel/delete [POST]
func DeleteMallChannel(c echo.Context) error {
	res := &dac.Response{}
	p := &dac.DeleteMallChannelRequest{}
	if err := c.Bind(p); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	if p.ChannelId <= 0 {
		res.Code = 400
		res.Message = "频道id不能为空"
		return c.JSON(400, res)
	}
	dacClient := GetDataCenterClient()
	defer dacClient.Close()

	var err error
	res, err = dacClient.RPC.DeleteMallChannel(dacClient.Ctx, p)
	if err != nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 新增商城频道内容
// @Tags 商城频道
// @Accept json
// @Produce plain
// @Param model body dac.AddMallChannelItemRequest true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router /boss/datacenter/mall-channel/item/add [POST]
func AddMallChannelItem(c echo.Context) error {
	res := &dac.Response{}
	p := &dac.AddMallChannelItemRequest{}
	if err := c.Bind(p); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	if p.ChannelId <= 0 || len(p.Image) == 0 || len(p.LinkUrl) == 0 {
		res.Code = 400
		res.Message = "参数错误"
		return c.JSON(400, res)
	}

	dacClient := GetDataCenterClient()
	defer dacClient.Close()
	var err error
	res, err = dacClient.RPC.AddMallChannelItem(dacClient.Ctx, p)
	if err != nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 获取商城频道内容列表
// @Tags 商城频道
// @Accept json
// @Produce plain
// @Param channel_id query int true "频道id"
// @Param page_index query int true "当前页"
// @Param page_size query int true "每页显示条数"
// @Success 200 {object} dac.GetMallChannelItemListResponse
// @Failure 400 {object} dac.GetMallChannelItemListResponse
// @Router /boss/datacenter/mall-channel/item/list [GET]
func GetMallChannelItemList(c echo.Context) error {
	res := &dac.GetMallChannelItemListResponse{}
	p := &dac.GetMallChannelItemListRequest{
		ChannelId: cast.ToInt64(c.QueryParam("channel_id")),
		PageIndex: cast.ToInt64(c.QueryParam("page_index")),
		PageSize:  cast.ToInt64(c.QueryParam("page_size")),
	}
	if p.ChannelId <= 0 {
		res.Code = 400
		res.Message = "频道id不能为空"
		return c.JSON(400, res)
	}
	if p.PageIndex <= 0 {
		p.PageIndex = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 10
	}
	dacClient := GetDataCenterClient()
	defer dacClient.Close()

	var err error
	res, err = dacClient.RPC.GetMallChannelItemList(dacClient.Ctx, p)
	if err != nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 编辑商城频道内容
// @Tags 商城频道
// @Accept json
// @Produce plain
// @Param model body dac.EditMallChannelItemRequest true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router /boss/datacenter/mall-channel/item/edit [POST]
func EditMallChannelItem(c echo.Context) error {
	res := &dac.Response{}
	p := &dac.EditMallChannelItemRequest{}
	if err := c.Bind(p); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	if p.ChannelItemId <= 0 {
		res.Code = 400
		res.Message = "频道内容id不能为空"
		return c.JSON(400, res)
	}
	dacClient := GetDataCenterClient()
	defer dacClient.Close()

	var err error
	res, err = dacClient.RPC.EditMallChannelItem(dacClient.Ctx, p)
	if err != nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 删除商城频道内容
// @Tags 商城频道
// @Accept json
// @Produce plain
// @Param model body dac.DeleteMallChannelItemRequest true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router  /boss/datacenter/mall-channel/item/delete [POST]
func DeleteMallChannelItem(c echo.Context) error {
	res := &dac.Response{}
	p := &dac.DeleteMallChannelItemRequest{}
	if err := c.Bind(p); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	if p.ChannelItemId <= 0 {
		res.Code = 400
		res.Message = "频道id不能为空"
		return c.JSON(400, res)
	}
	dacClient := GetDataCenterClient()
	defer dacClient.Close()

	var err error
	res, err = dacClient.RPC.DeleteMallChannelItem(dacClient.Ctx, p)
	if err != nil {
		return c.JSON(400, dac.BaseResponse{Code: 400, Message: err.Error()})
	}
	if res.Code != 200 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}
