package controller

import (
	"_/proto/pay"
	"_/proto/pc"
	"github.com/labstack/echo/v4"
	"net/http"
)

// @Summary 支付列表
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param PayListRequest body pay.QueryPwdRequest true " "
// @Success 200 {object} pay.PayConfigResponse
// @Failure 400 {object} pay.PayConfigResponse
// @Router /boss/paycenter/paymethod/config [get]
func PayConfig(c echo.Context) error {
	model := new(pay.QueryPwdRequest)

	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.PayInfo.PayConfig(client.Ctx, model); err != nil {
		return c.JSON(http.StatusBadRequest, err)
	} else {
		return c.JSON(http.StatusOK, out)
	}
}

// @Summary 支付设置
// @Tags 支付中心
// @Accept json
// @Produce json
// @Param PayListRequest body pay.PaySetRequest true " "
// @Success 200 {object} pay.BaseResponse
// @Failure 400 {object} pay.BaseResponse
// @Router /boss/paycenter/paymethod/set [POST]
func PaySet(c echo.Context) error {
	model := new(pay.PaySetRequest)

	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := GetpayInfoClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.PayInfo.PaySet(client.Ctx, model); err != nil {
		return c.JSON(http.StatusBadRequest, err)
	} else {
		return c.JSON(http.StatusOK, out)
	}
}