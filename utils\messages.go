package utils

import (
	"_/models"
	"encoding/xml"
	"io/ioutil"
	"net/http"
	"net/url"

	"github.com/go-xorm/xorm"
)

//短信发送接口
//Mobile 发送目标手机号
//Content 参数替换 @1@=9527 多个需要逗号隔开
//Tempid 美圣模板编号
//JSM40580-0023【登录】
func SendMessage(engine *xorm.Engine, Mobile string, Content string, Tempid string) bool {
	//return string("<?xml version="1.0" encoding="UTF-8"?><sms><mt><status>0</status><msgid>aef39796d8de470b8868ea3125a37e3d</msgid></mt></sms>")
	//构造参数
	u, err := url.Parse("http://112.74.76.186:8030/service/httpService/httpInterface.do?method=sendMsg&code=utf-8")
	if err != nil {
		panic(err)
	}
	q := u.Query()
	q.Set("method", "sendMsg&code=utf-8")
	q.Set("username", "JSM4058003")
	q.Set("password", "6bBRtANf")
	q.Set("veryCode", "ZnSHSes6QG7H")
	q.Set("mobile", Mobile)
	q.Set("content", Content)
	q.Set("msgtype", "2")
	q.Set("tempid", Tempid)
	//发起post表单请求
	resp, err := http.PostForm(u.String(), q)
	if err != nil {
		panic(err)
	}
	//解析响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}

	//解析xml 数据返回
	data := models.Sms{}
	err = xml.Unmarshal(body, &data)
	if err != nil {
		panic(err)
	}
	if data.Mt.Status != 0 {
		return false
	}

	var item = models.MessageLog{
		Id:      0,
		Mobile:  Mobile,
		Tempid:  Tempid,
		Success: 1,
		Params:  Content,
	}
	engine.Table(models.MessageLog{}).InsertOne(item)

	return true
}
