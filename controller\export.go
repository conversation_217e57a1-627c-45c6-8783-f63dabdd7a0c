package controller

import (
	"_/proto/dac"
	"_/utils"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// ExportTaskCreate
// @Summary 导出任务创建
// @Tags 导出
// @Accept multipart/form-data
// @Produce json
// @Param model body dac.ExportRequest true " "
// @Success 200 {object} dac.ExportResponse
// @Failure 400 {object} dac.ExportResponse
// @Router /boss/datacenter/export-task-create [post]
func ExportTaskCreate(c echo.Context) error {
	req := &dac.ExportRequest{
		Type:    cast.ToInt32(c.FormValue("type")),
		Payload: c.FormValue("payload"),
		IsSync:  cast.ToInt32(c.FormValue("is_sync")),
	}
	client := dac.GetDataCenterClient()
	// 附加操作人信息
	ctx, _ := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if out, err := client.RPC.ExportTaskCreate(ctx, req); err != nil {
		return c.JSON(400, &dac.ExportResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// ExportHistory
// @Summary 导出历史记录
// @Tags 导出
// @Accept json
// @Produce json
// @Param model query dac.ExportHistoryRequest true " "
// @Success 200 {object} dac.ExportHistoryResponse
// @Failure 400 {object} dac.ExportHistoryResponse
// @Router /boss/datacenter/export-history [get]
func ExportHistory(c echo.Context) error {
	req := &dac.ExportHistoryRequest{
		Type:      cast.ToInt32(c.QueryParam("type")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
	}
	client := dac.GetDataCenterClient()
	// 附加操作人信息
	ctx, _ := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if out, err := client.RPC.ExportHistories(ctx, req); err != nil {
		return c.JSON(400, &dac.ExportHistoryResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
