package controller

import (
	"_/models"
	"_/proto/pc"
	"_/utils"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"net/http"
)

// @Summary v6.5.1 互联网医院商品价格导入
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param params body pc.HospitalProductPriceImportReq true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/hospital-product/price-import [post]
func HospitalProductPriceImport(c echo.Context) error {
	baseResponse := pc.BaseResponse{
		Code: http.StatusBadRequest,
	}

	// 序列化参数
	params := new(pc.HospitalProductPriceImportReq)
	if err := c.Bind(params); err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	client := GetDcProductClient(c)
	defer client.Close()

	resp, err := client.RPC.HospitalProductPriceImport(client.Ctx, params)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}
	if resp.Code != http.StatusOK {
		baseResponse.Message = resp.Message
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	baseResponse.Code = http.StatusOK
	return c.JSON(http.StatusOK, baseResponse)
}

// @Summary v6.5.1 互联网医院商品价格列表
// @Tags 商品库
// @Accept plain
// @Param skuid query string false "skuid"
// @Param page_index query int false "分页索引"
// @Param page_size query int false "分页大小"
// @Success 200 {object} pc.HospitalProductPriceListRes
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/hospital-product/price-list [get]
func HospitalProductPriceList(c echo.Context) error {
	out := pc.HospitalProductPriceListRes{
		Code: http.StatusBadRequest,
	}

	var params = new(pc.HospitalProductPriceListReq)
	params.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	params.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	params.Skuid = c.QueryParam("skuid")

	client := GetDcProductClient(c)
	defer client.Close()

	resp, err := client.RPC.HospitalProductPriceList(client.Ctx, params)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if resp.Code != http.StatusOK {
		out.Message = resp.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = http.StatusOK
	out.Data = resp.Data
	out.Total = resp.Total
	return c.JSON(http.StatusOK, out)
}

// @Summary v6.5.1 互联网医院商品价格移除
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param params body pc.IdRequest true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/hospital-product/price-del [post]
func HospitalProductPriceDel(c echo.Context) error {
	baseResponse := pc.BaseResponse{
		Code: http.StatusBadRequest,
	}

	// 序列化参数
	params := new(pc.IdRequest)
	if err := c.Bind(params); err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	client := GetDcProductClient(c)
	defer client.Close()

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return c.JSON(400, baseResponse)
	}
	params.CreateId = userInfo.UserNo
	params.CreateName = userInfo.UserName
	params.IpAddr = c.RealIP()
	params.IpLocation = GetIpAddress(c.RealIP())

	resp, err := client.RPC.HospitalProductPriceDel(client.Ctx, params)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}
	if resp.Code != http.StatusOK {
		baseResponse.Message = resp.Message
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	baseResponse.Code = http.StatusOK
	return c.JSON(http.StatusOK, baseResponse)
}

// @Summary v6.5.1 互联网医院商品价格编辑
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param params body pc.HospitalProductPriceEditReq true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/hospital-product/price-edit [post]
func HospitalProductPriceEdit(c echo.Context) error {
	baseResponse := pc.BaseResponse{
		Code: http.StatusBadRequest,
	}

	// 序列化参数
	params := new(pc.HospitalProductPriceEditReq)
	if err := c.Bind(params); err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	client := GetDcProductClient(c)
	defer client.Close()
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return c.JSON(400, baseResponse)
	}
	params.CreateId = userInfo.UserNo
	params.CreateName = userInfo.UserName
	params.IpAddr = c.RealIP()
	params.IpLocation = GetIpAddress(c.RealIP())

	resp, err := client.RPC.HospitalProductPriceEdit(client.Ctx, params)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}
	if resp.Code != http.StatusOK {
		baseResponse.Message = resp.Message
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	baseResponse.Code = http.StatusOK
	return c.JSON(http.StatusOK, baseResponse)
}

// @Summary 导出互联网医院商品价格
// @Tags 商品库
// @Accept plain
// @Produce plain
// @Param skuid query int true "商品skuid"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/hospital-product/price-export [get]
func ExportHospitalProductPrice(c echo.Context) error {
	var out pc.BaseResponse
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var userInfo = models.LoginUserInfo{}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	}

	skuid := cast.ToInt32(c.QueryParam("skuid"))
	userTask, err := client.RPC.GetUserUnFinishedTask(client.Ctx, &pc.GetUserUnFinishedTaskRequest{
		UserNo:        userInfo.UserNo,
		ChannelId:     0,
		TaskContent:   36,
		OperationData: cast.ToString(skuid),
	})
	if err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	}
	if userTask.Id > 0 {
		return c.JSON(400, models.BaseResponse{Code: 400, Msg: "用户存在进行中的任务"})
	}

	rpcRequest := pc.CreateBatchTaskRequest{
		OperationFileUrl: cast.ToString(skuid),
		CreateId:         userInfo.UserNo,
		ChannelId:        0,
		CreateName:       userInfo.UserName,
		CreateMobile:     userInfo.Mobile,
		CreateIp:         c.RealIP(),
		IpLocation:       GetIpAddress(c.RealIP()),
		TaskContent:      36,
		ExtendedData:     "互联网医院商品价格-导出",
	}

	if _, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
		return c.JSON(400, err)
	}
	out.Code = 200
	out.Message = "操作已成功提交，请在“批量任务查看”中查看相关结果"

	return c.JSON(http.StatusOK, &out)
}

// @Summary 导出仓库白名单
// @Tags 参数配置
// @Accept plain
// @Produce plain
// @Param warehouse_key query string true "仓库编码或仓库名称"
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/warehouse/white-export [get]
func ExportWhiteExport(c echo.Context) error {
	var out pc.BaseResponse
	client := GetDcProductClient()
	defer client.Conn.Close()
	defer client.Cf()

	var userInfo = models.LoginUserInfo{}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	}

	warehouseKey := c.QueryParam("warehouse_key")
	userTask, err := client.RPC.GetUserUnFinishedTask(client.Ctx, &pc.GetUserUnFinishedTaskRequest{
		UserNo:        userInfo.UserNo,
		ChannelId:     0,
		TaskContent:   38,
		OperationData: warehouseKey,
	})
	if err != nil {
		return c.JSON(400, models.BaseResponse{Code: 400, Details: err.Error()})
	}
	if userTask.Id > 0 {
		return c.JSON(400, models.BaseResponse{Code: 400, Msg: "用户存在进行中的任务"})
	}

	rpcRequest := pc.CreateBatchTaskRequest{
		OperationFileUrl: warehouseKey,
		CreateId:         userInfo.UserNo,
		ChannelId:        0,
		CreateName:       userInfo.UserName,
		CreateMobile:     userInfo.Mobile,
		CreateIp:         c.RealIP(),
		IpLocation:       GetIpAddress(c.RealIP()),
		TaskContent:      38,
		ExtendedData:     "仓库白名单-导出",
	}

	if _, err := client.RPC.CreateBatchTask(client.Ctx, &rpcRequest); err != nil {
		return c.JSON(400, err)
	}
	out.Code = 200
	out.Message = "操作已成功提交，请在“批量任务查看”中查看相关结果"

	return c.JSON(http.StatusOK, &out)
}
