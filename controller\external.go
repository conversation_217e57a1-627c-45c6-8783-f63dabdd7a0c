package controller

import (
	"_/models"
	"_/proto/dac"
	"_/proto/et"
	"_/proto/oc"
	"_/proto/pc"
	"_/utils"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	sysos "os"
	"strconv"
	"strings"
	"sync"
	"time"
	"unsafe"

	kit "github.com/tricobbler/rp-kit"

	"github.com/labstack/echo/v4"
	els "github.com/legofun/elasticsearch"
	"github.com/maybgit/glog"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
)

// @Summary 修改京东到家商品信息(包括下架)
// @Tags 京东到家服务接口
// @Produce json
// @Param OutSkuId query string true "商家商品编码"
// @Param TraceId query string true "请求唯一编码"
// @Success 200 {object} models.JddjUpdateGoodsListResponse
// @Failure 400 {object} models.BaseResponse
// @Router /boss/jddj/Goods/list/update [post]
func JddjUpdateGoodsList(c echo.Context) error {
	out := &models.JddjUpdateGoodsListResponse{}
	//前台传参
	type params struct {
		OutSkuId        string   `query:"OutSkuId" validate:"" label:"商家商品编码"`
		CategoryId      int64    `query:"CategoryId" validate:"" label:"到家类目编号"`
		BrandId         int64    `query:"BrandId" validate:"" label:"到家品牌编号"`
		SkuName         string   `query:"SkuName" validate:"" label:"商品名称"`
		Weight          float32  `query:"Weight" validate:"" label:"重量"`
		UpcCode         string   `query:"UpcCode" validate:"" label:"UPC编码（商品条码)"`
		FixedStatus     int32    `query:"FixedStatus" validate:"" label:"商家商品上下架状态"`
		Images          []string `query:"Images" validate:"" label:"商品图片地址"`
		ProductDesc     string   `query:"ProductDesc" validate:"" label:"商品描述"`
		IfViewDesc      int32    `query:"IfViewDesc" validate:"" label:"商品描述是否在app端展示"`
		Slogan          string   `query:"Slogan" validate:"" label:"广告词"`
		SloganStartTime string   `query:"SloganStartTime" validate:"" label:"广告词生效时间"`
		SloganEndTime   string   `query:"SloganEndTime" validate:"" label:"广告词失效时间"`
		SellCities      []string `query:"SellCities" validate:"" label:"城市ID"`
		TraceId         string   `query:"TraceId" validate:"" label:"请求唯一编码"`
	}
	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	client := et.GetExternalClient()
	defer client.Close()
	if res, err := client.JddjProduct.JddjUpdateGoodsList(client.Ctx, &et.JddjUpdateGoodsListRequest{
		OutSkuId:        p.OutSkuId,
		CategoryId:      p.CategoryId,
		BrandId:         p.BrandId,
		SkuName:         p.SkuName,
		Weight:          p.Weight,
		UpcCode:         p.UpcCode,
		FixedStatus:     p.FixedStatus,
		Images:          p.Images,
		ProductDesc:     p.ProductDesc,
		IfViewDesc:      p.IfViewDesc,
		Slogan:          p.Slogan,
		SloganStartTime: p.SloganStartTime,
		SloganEndTime:   p.SloganEndTime,
		SellCities:      p.SellCities,
		TraceId:         p.TraceId,
	}); err != nil {
		glog.Error("调用JddjUpdateGoodsList失败，", err)
		return r.NewHTTPError(400, err.Error())
	} else {
		jsonData, _ := json.Marshal(res)
		if err := json.Unmarshal(jsonData, out); err != nil {
			glog.Error("JddjUpdateGoodsList pb消息转结构体失败，", err)
			return r.NewHTTPError(400, err.Error())
		}
		return c.JSON(200, out)
	}
	return nil
}

// @Summary 品牌列表
// @Tags 京东到家服务接口
// @Accept plain
// @Produce json
// @Param brand_name query string false "品牌名称(模糊查询);不传代表查询所有"
// @Param page_index query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} models.JddjBrandListResponse
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/jddj/brand/list [get]
func JddjBrandList(c echo.Context) error {
	type params struct {
		BrandName string `query:"brand_name" validate:"" label:"品牌名称"`
		PageIndex int    `query:"page_index" validate:"" label:"页码"`
		PageSize  int    `query:"page_size" validate:"" label:"每页数量"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	pageIndex, pageSize := p.PageIndex, p.PageSize
	if pageIndex <= 0 {
		pageIndex = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	es, err := els.NewEsClient()
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	es.SetTimeout(3 * time.Second)

	if len(p.BrandName) > 0 {
		es.SetQuery("brandName", []interface{}{p.BrandName})
	}
	//只筛选状态可用的
	es.SetQuery("brandStatus", []interface{}{2})
	res, err := es.Search(JddjBrandEsIndex, pageIndex, pageSize, []elastic.Sorter{
		elastic.NewFieldSort("_id").Asc(),
	})
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	out := models.JddjBrandListResponse{}
	out.Code = 200
	out.Data = []*models.JddjBrandList{}

	if res.Hits != nil && res.Hits.TotalHits.Value > 0 {
		out.TotoalCount = int32(res.Hits.TotalHits.Value)

		out.Data = make([]*models.JddjBrandList, len(res.Hits.Hits))
		for k, v := range res.Hits.Hits {
			source := new(models.EsJddjBrand)
			if err = json.Unmarshal(v.Source, source); err != nil {
				glog.Error("json解析错误，err: ", err, " json: ", source)
			}
			out.Data[k] = &models.JddjBrandList{
				BrandId:   int(source.Id),
				BrandName: source.BrandName,
			}
		}
	}

	return c.JSON(200, out)
}

// @Summary 拣货完成且顾客自提接口
// @Tags 京东到家服务接口
// @Accept json
// @Produce json
// @Param orderid query string true "订单编号"
// @Param operator query int true "操作人（不能为空）"
// @Success 200 {object} et.JddjBaseResponse
// @Failure 400 {object} et.JddjBaseResponse
// @Router /boss/jddj/order/selfmention [get]
func JddjSelfMention(ctx echo.Context) error {
	client := et.GetExternalClient()
	defer client.Close()
	if resp, err := client.JddjOrder.JddjOrderSelfMention(context.Background(), &et.JddjOrderSelfMentionRequest{
		OrderId:  ctx.FormValue("orderid"),
		Operator: ctx.FormValue("operator"),
	}); err != nil {
		return ctx.JSON(http.StatusBadRequest, err)
	} else {
		return ctx.JSON(http.StatusOK, resp)
	}
}

// @Summary 订单自提码核验
// @Tags 京东到家服务接口
// @Accept json
// @Produce json
// @Param orderid query string true "订单编号"
// @Param operator query int true "操作人（不能为空）"
// @Param selfpickcode query int true "提取码（不能为空）"
// @Success 200 {object} et.JddjBaseResponse
// @Failure 400 {object} et.JddjBaseResponse
// @Router /boss/jddj/order/checkselfpickcode [get]
func JddjCheckSelfPickCode(c echo.Context) error {
	orderSn := c.FormValue("orderid")
	if len(orderSn) == 0 {
		return r.NewHTTPError(400, "订单编号不能为空")
	}

	storeMasterId, err := GetAppChannelByOrderSn(orderSn)
	if err != nil {
		glog.Error("JddjCheckSelfPickCode,", "GetAppChannelByOrderSn,", orderSn)
		return r.NewHTTPError(400, err.Error())
	}

	client := et.GetExternalClient()
	defer client.Close()

	reqData := et.JddjCheckSelfPickCodeRequest{
		OrderId:       orderSn,
		OperPin:       c.FormValue("operator"),
		SelfPickCode:  c.FormValue("selfpickcode"),
		StoreMasterId: storeMasterId,
	}
	resp, err := client.JddjOrder.JddjCheckSelfPickCode(client.Ctx, &reqData)
	if err != nil {
		err = errors.New("订单自提码核验失败，" + err.Error())
		glog.Error(orderSn, err)
		return r.NewHTTPError(400, err.Error())
	} else if resp.Code != 200 {
		err = errors.New("订单自提码核验失败，" + resp.Message)
		glog.Error(orderSn, err)
		return r.NewHTTPError(400, err.Error())
	}

	clientOc := oc.GetOrderServiceClient()
	if out, err := clientOc.RPC.AccomplishOrder(clientOc.Ctx, &oc.AccomplishOrderRequest{
		OrderSn:     orderSn,
		ConfirmTime: kit.GetTimeNow(),
	}); err != nil {
		err = errors.New("订单自提码核验，更改订单状态失败：" + err.Error())
		glog.Error(orderSn, err)
		return r.NewHTTPError(400, err.Error())
	} else if out.Code != 200 {
		err = errors.New("订单自提码核验，更改订单状态失败：" + out.Message)
		glog.Error(orderSn, err)
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(http.StatusOK, resp)
}

// @Summary v6.0类目列表
// @Tags v6.0 京东到家分类接口
// @Accept plain
// @Produce json
// @Success 200 {object} models.JddjCategoryListResponseData
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/jddj/retail/getSpTagIds [get]
func JddjRetailGetSpTagIds(c echo.Context) error {
	out := &models.JddjCategoryListResponseData{}
	out.Code = 400
	out.Data = make([]*et.JddjCategoryInfo, 0)
	client := et.GetExternalClient()
	defer client.Close()

	redisConn := GetRedisConn()
	jdKey := "boss:external:RetailGetSpTagIds:jddj"
	val := redisConn.Get(jdKey).Val()

	if len(val) > 0 {
		err := json.Unmarshal([]byte(val), &out.Data)
		if err != nil {
			glog.Error("获取redis的jddj分类异常：", err.Error())
		} else {
			out.Code = 200
			return c.JSON(200, out)
		}

	}

	response, err := client.JddjProduct.GetJddjCategoryList(context.Background(), &et.QueryChildCategoriesForOPRequest{StoreMasterId: 1})
	if err != nil {
		out.Message = "获取京东到家分类异常"
		glog.Error("JddjRetailGetSpTagIds : ", out.Message, err.Error())
		return c.JSON(400, out)
	}

	jsonData, _ := json.Marshal(response.Result)
	if len(response.Result) == 0 {
		out.Message = "拉取jd分类为空: "
		glog.Error(out.Message, jsonData)
		return c.JSON(400, out)
	} else {
		// 设置jd分类不过期
		redisConn.Set(jdKey, jsonData, 0)
	}
	out.Code = 200
	out.Data = response.Result
	return c.JSON(200, out)

}

// @Summary 类目列表
// @Tags 京东到家服务接口
// @Accept plain
// @Produce json
// @Success 200 {object} models.JddjCategoryListResponse
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/jddj/category/list [get]
func JddjCategoryList(c echo.Context) error {
	out := &models.JddjCategoryListResponse{}
	out.Code = 200

	redis := GetRedisConn()
	rk := "boss:external:JddjCategoryList"
	data := redis.Get(rk).Val()
	if len(data) > 0 {
		if err := json.Unmarshal([]byte(data), &out.Data); err != nil {
			glog.Error("解析京东到家类目列表失败，", err, ", json:", data)
		} else {
			return c.JSON(200, out)
		}
	}

	client := et.GetExternalClient()
	defer client.Close()

	//只获取宠物类目
	var firstCategory int32 = 22049

	//获取二级类目
	secondBrands := getJddjBrand(client, firstCategory)
	wg := sync.WaitGroup{}
	for _, v := range secondBrands {
		if v.CategoryStatus == 0 {
			continue
		}
		wg.Add(1)
		v := v
		go func() {
			defer wg.Done()

			//获取二级类目
			thirdBrands := getJddjBrand(client, v.Id)
			for _, vv := range thirdBrands {
				if v.CategoryStatus == 0 {
					continue
				}

				out.Data = append(out.Data, &models.JddjCategoryList{
					CategoryId:   vv.Id,
					CategoryName: vv.CategoryName,
				})
			}
		}()
	}
	wg.Wait()

	jsonRes, _ := json.Marshal(out.Data)
	if len(out.Data) == 0 {
		redis.Set(rk, jsonRes, 30*time.Second)
	} else {
		redis.Set(rk, jsonRes, 24*time.Hour)
	}

	return c.JSON(200, out)
}

func getJddjBrand(client *et.Client, brandId int32) []*et.JddjCategoryInfo {
	res, err := client.JddjProduct.QueryChildCategoriesForOP(client.Ctx, &et.QueryChildCategoriesForOPRequest{
		Id: brandId,
	})
	if err != nil {
		glog.Error(err)
		return nil
	}
	return res.Result
}

// @Summary 订单调整
// @Tags 京东到家服务接口
// @Accept plain
// @Produce json
// @Success 200 {object} et.JddjAdjustOrderRequest
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/jddj/order/jddjAdjustOrder [post]
func JddjAdjustOrder(c echo.Context) error {
	model := new(et.JddjAdjustOrderRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	if err != nil {
		glog.Error("JddjAdjustOrder,", "GetAppChannelByOrderSn,", model.OrderId)
		return r.NewHTTPError(http.StatusBadRequest, "获取店铺主体信息失败")
	}
	model.StoreMasterId = storeMasterId

	etClient := et.GetExternalClient()
	defer etClient.Close()

	//通知京东到家调整订单
	res, err := etClient.JddjOrder.JddjAdjustOrder(etClient.Ctx, model)
	if err != nil {
		glog.Error("调用JddjAdjustOrder失败，", err)
		return r.NewHTTPError(400, err.Error())
	} else if res.Code != 200 {
		return r.NewHTTPError(400, res.Message)
	}

	return c.JSON(200, &models.BaseResponseV2{
		Code: 200,
	})
}

// @Summary 修改京东到家商家店内分类信息
// @Tags 京东到家服务接口
// @Produce json
// @Param id query int true "店内分类编号"
// @Param shopCategoryName query string true "店内分类名称"
// @Success 200 {object} models.JddjCategoryListUpdateResponse
// @Failure 400 {object} models.JddjCategoryListUpdateResponse
// @Router /boss/jddj/category/list/update [post]
func JddjUpdateCategoryList(c echo.Context) error {
	out := &models.JddjCategoryListUpdateResponse{}
	//前台传参
	type params struct {
		Id               int64  `query:"id" validate:"" label:"店内分类编号"`
		ShopCategoryName string `query:"shopCategoryName" validate:"" label:"店内分类名称"`
	}
	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	id, shopCategoryName := p.Id, p.ShopCategoryName
	client := et.GetExternalClient()
	defer client.Close()
	if res, err := client.JddjProduct.JddjUpdateShopCategory(client.Ctx, &et.JddjUpdateShopCategoryRequest{
		Id:               id,
		ShopCategoryName: shopCategoryName,
	}); err != nil {
		glog.Error("调用JddjUpdateShopCategory失败，", err)
		return r.NewHTTPError(400, err.Error())
	} else {
		jsonData, _ := json.Marshal(res)
		if err := json.Unmarshal(jsonData, out); err != nil {
			glog.Error("JddjUpdateCategoryList pb消息转结构体失败，", err)
			return r.NewHTTPError(400, err.Error())
		}
		return c.JSON(200, out)
	}
	return nil
}

// @Summary 获取美团后台商品类目（末级类目id）
// @Tags 美团服务接口
// @Accept json
// @Produce json
// @Param app_poi_code query string false "APP方门店id，即商家中台系统里门店的编码"
// @Param channel_id query int false "渠道id"
// @Success 200 {object} et.RetailGetSpTagIdsResult
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/meituan/retail/getSpTagIds [get]
func RetailGetSpTagIds(c echo.Context) error {
	out := &et.RetailGetSpTagIdsResult{
		Code: 200,
	}

	rk := "boss:external:RetailGetSpTagIds"
	redis := GetRedisConn()
	data := redis.Get(rk).Val()
	if len(data) > 0 {
		if err := json.Unmarshal([]byte(data), &out.Data); err != nil {
			glog.Error("解析美团类目列表失败，", err, ", json:", data)
		} else {
			return c.JSON(200, out)
		}
	}

	client := et.GetExternalClient()
	defer client.Close()
	// todo.sean为什么这里不传参数
	if res, err := client.RPC.RetailGetSpTagIds(client.Ctx, &et.AppPoiCodeRequest{}); err != nil {
		glog.Error("调用RetailGetSpTagIds失败，", err)
		return r.NewHTTPError(400, err.Error())
	} else {
		for _, v := range res.Data {
			//只取宠物用品类目
			if strings.Contains(v.NamePath, "宠物") {
				out.Data = append(out.Data, v)
			}
		}
	}

	jsonData, _ := json.Marshal(out.Data)
	if len(out.Data) == 0 {
		redis.Set(rk, jsonData, 1*time.Minute)
	} else {
		redis.Set(rk, jsonData, 30*time.Minute)
	}

	return c.JSON(200, out)
}

// @Summary 根据末级类目id获取类目属性列表
// @Tags 美团服务接口
// @Accept json
// @Produce json
// @Param channel_id query int true "渠道id"
// @Param tag_id query int true "末级类目id，取自【retail/getSpTagIds 获取美团商品类目】接口的id字段，即末级类目id。如果这个末级类目id没有类目属性，则返回空"
// @Success 200 {object} et.CategoryAttrListResult
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/meituan/category/attr/list [get]
func CategoryAttrList(c echo.Context) error {
	client := et.GetExternalClient()
	defer client.Close()

	if out, err := client.RPC.CategoryAttrList(client.Ctx, &et.CategoryAttrListRequest{
		TagId: cast.ToInt64(c.QueryParam("tag_id")),
	}); err != nil {
		glog.Error("调用CategoryAttrList失败，", err)
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(200, out)
	}
}

// @Summary 查询特殊属性的属性值列表
// @Tags 美团服务接口
// @Accept json
// @Produce json
// @Param channel_id query int true "渠道id"
// @Param attr_id query int true "属性 id  取值范围仅支持两个：1200000088-品牌, 1200000094-产地"
// @Param keyword query string true "属性值的关键词，如果这个关键字没有对应的数据，则返回空。"
// @Param page_num query int false "页数，传1表示第1页 =1"
// @Param page_size query int false "每页大小，最大支持200 =200"
// @Success 200 {object} et.CategoryAttrValueListResult
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/meituan/category/attr/value/list [get]
func CategoryAttrValueList(c echo.Context) error {
	client := et.GetExternalClient()
	defer client.Close()

	if out, err := client.RPC.CategoryAttrValueList(client.Ctx, &et.CategoryAttrValueListRequest{
		AttrId:   cast.ToInt64(c.QueryParam("attr_id")),
		Keyword:  c.QueryParam("keyword"),
		PageNum:  cast.ToInt32(c.QueryParam("page_num")),
		PageSize: cast.ToInt32(c.QueryParam("page_size")),
	}); err != nil {
		glog.Error("调用CategoryAttrValueList失败，", err)
		return r.NewHTTPError(400, err.Error())
	} else {
		if int(out.Code) == 400 && len(out.Error) > 0 {
			errJson := &struct {
				Error map[string]interface{}
			}{}
			if err := json.Unmarshal([]byte(out.Error), errJson); err == nil {
				if errJson.Error["code"].(float64) == 6000 {
					out.Message = "品牌暂未收录"
				} else {
					out.Message = errJson.Error["msg"].(string)
				}
			}
		}
		return c.JSON(200, out)
	}
}

// @Summary 获取商品类目列表
// @Tags 饿么了服务接口
// @Accept json
// @Produce json
// @Param parent_id query string false "父类id"
// @Success 200 {object} et.ElmCategoryListResponse
// @Failure 400 {object} et.ElmCategoryListResponse
// @Router /boss/elm/retail/getSpTagIds [get]
func ElmRetailGetSpTagIds(c echo.Context) error {
	baseResponse := &pc.BaseResponse{
		Code: 400,
	}

	client := GetDcProductClient(c)
	defer client.Close()
	parentId := c.QueryParam("parent_id")

	if out, err := client.RPC.GetElmCategoryList(client.Ctx, &pc.CategoryListRequest{ParentId: parentId}); err != nil {
		glog.Error("调用ElmRetailGetSpTagIds失败，", err)
		baseResponse.Message = err.Error()
		return c.JSON(400, baseResponse)
	} else {
		if out.Code != 200 {
			baseResponse.Message = out.Error
			return c.JSON(200, baseResponse)
		} else {
			out.Code = 200
			/*jsonData, err := json.Marshal(out.Data)
			if err != nil {
				glog.Error(err)
			}
			redis.Set(rk, jsonData, 30*time.Minute)*/
		}
		return c.JSON(200, out)
	}
}

// @Summary 发起配送
// @Tags 饿么了服务接口
// @Param oldordersn query string true "饿了么订单号"
// @Success 200 {object} et.ElmOrderCallDeliveryResponse
// @Router /boss/elm/order/callDelivery [post]
func ElmOrderCallDelivery(c echo.Context) error {
	baseResponse := &pc.BaseResponse{
		Code: 400,
	}
	oldordersn := c.FormValue("oldordersn")
	//glog.Info("1饿么了呼叫骑士",oldordersn)

	appChannel, err := GetAppChannelByOrderSn(oldordersn)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(400, baseResponse)
	}

	client := et.GetExternalClient()

	out, err := client.ELMORDER.ElmOrderCallDelivery(client.Ctx, &et.ElmOrderCallDeliveryRequest{OrderId: oldordersn, AppChannel: appChannel})
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(400, baseResponse)
	}
	if out.Code != 200 {
		baseResponse.Message = out.Error
		return c.JSON(200, baseResponse)
	}
	out.Code = 200
	//glog.Info("饿么了呼叫骑士2",out.Code)
	//if out.Code==200{
	//修改未发出配送为已发出
	clientOrder := oc.GetOrderServiceClient()
	//glog.Info("3饿么了呼叫骑士", oldordersn)
	outOrder, err := clientOrder.RPC.UPDateElmOrderPushDelivery(clientOrder.Ctx, &oc.UPDateElmOrderPushDeliveryRequest{Oldordersn: oldordersn})
	//glog.Info("4饿么了呼叫骑士", oldordersn, outOrder)

	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(400, baseResponse)
	}
	if outOrder.Code != 200 {
		baseResponse.Message = out.Error
		return c.JSON(200, baseResponse)
	}
	//glog.Info("5饿么了呼叫骑士", oldordersn)
	return c.JSON(200, out)

}

// 通过财务编码获取渠道门店id
// param financeCode 门店财务编码
// param channelId 渠道id
// param keyType 返回值类型，1 - FinanceCode为key, 2 - ChannelStoreId为key
func GetAppPoiCodeByFinanceCode(financeCode []string, channelId int32, keyType int8) (appPoiCodeMap map[string]string) {
	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	appPoiCodeMap = make(map[string]string, 0)
	if out, err := client.RPC.QueryStoresChannelId(client.Ctx, &dac.StoreRelationUserRequest{
		FinanceCode: financeCode,
		Psize:       1000,
		ChannelId:   channelId,
	}); err != nil {
		glog.Error(err)
	} else {
		if out.Code == 200 && len(out.Data) > 0 {
			if keyType == 1 {
				for _, v := range out.Data {
					if len(v.FinanceCode) > 0 {
						if _, ok := appPoiCodeMap[v.FinanceCode]; !ok {
							appPoiCodeMap[v.FinanceCode] = v.ChannelStoreId
						}
					}
				}
			}

			if keyType == 2 {
				for _, v := range out.Data {
					if len(v.ChannelStoreId) > 0 {
						if _, ok := appPoiCodeMap[v.ChannelStoreId]; !ok {
							appPoiCodeMap[v.ChannelStoreId] = v.FinanceCode
						}
					}
				}
			}
		}
	}

	return
}

// @Summary 根据门店financecode获取该门店支持的配送范围 宠物saas-v1.0
// @Tags 美团服务接口
// @Param financecode query  string true "店铺财务ID"
// @Success 200 {object} et.ExternalResponse
// @Failure 400 {object} et.ExternalResponse
// @Router /boss/meituan/shop/area/query [post]
func GetMpShopAreaQuery(c echo.Context) error {
	baseResponse := &pc.BaseResponse{
		Code: 400,
	}
	client := et.GetExternalClient()
	//
	//dispatchClient := dc.GetDcDispatchClient()
	var mpShopArea models.MpShopArea
	financecode := c.FormValue("financecode")
	channelId := c.FormValue("channel_id")
	//Warehouse, err := dispatchClient.RPC.GetWarehouseInfoByFanceCode(dispatchClient.Ctx, &dc.GetWarehouseInfoByFanceCodeRequest{FinanceCode: financecode})
	//if err != nil {
	//	baseResponse.Message = err.Error()
	//	return c.JSON(400, baseResponse)
	//}
	//
	//if Warehouse.Code != 200 {
	//	baseResponse.Message = Warehouse.Message
	//	return c.JSON(400, baseResponse)
	//}
	//if Warehouse.Warehouseinfo == nil {
	//	baseResponse.Message = "仓库信息不存在,财务编码：" + financecode
	//	return c.JSON(400, baseResponse)
	//}
	glog.Infof("GetMpShopAreaQuery入参：%s，%s", financecode, channelId)
	var warehouse models.Warehouse
	//根据财务编码和渠道获取仓库信息
	db := GetDatacenterDBConn()
	ishave, err := db.SQL("select a.* from dc_dispatch.warehouse a inner join dc_dispatch.warehouse_relation_shop b on a.id=b.warehouse_id where b.channel_id=? and b.shop_id=?", channelId, financecode).Get(&warehouse)
	if err != nil {
		baseResponse.Message = "查询仓库信息出错:" + err.Error()
		return c.JSON(400, baseResponse)
	}
	glog.Infof("GetMpShopAreaQuery根据财务编码和渠道获取仓库信息：ishave=%v,仓库：%s", ishave, kit.JsonEncode(warehouse))
	if !ishave {
		baseResponse.Message = "当前渠道没有绑定仓库"
		return c.JSON(400, baseResponse)
	}
	//获取操作人信息
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		baseResponse.Message = "获取操作人信息失败!"
		return c.JSON(200, baseResponse)
	}
	warehouseDeliveryRelation := models.WarehouseDeliveryRelation{}
	if userInfo.ScrmId != "" {
		ishave, err = db.SQL("select * from dc_dispatch.warehouse_delivery_relation where warehouse_id=?", warehouse.Id).Get(&warehouseDeliveryRelation)
		if err != nil {
			baseResponse.Message = "查询仓库对应的第三方配送门店失败:" + err.Error()
			return c.JSON(400, baseResponse)
		}
		if !ishave {
			baseResponse.Message = "当前仓库没有绑定第三方配送门店id"
			return c.JSON(400, baseResponse)
		}
	}

	mpShopAreaQueryRequest := new(et.MpShopAreaQueryRequest)
	//mpShopAreaQueryRequest.ShopId = Warehouse.Warehouseinfo.Code
	shopId := warehouse.Code
	//处理环境变量信息
	envName := strings.ToLower(sysos.Getenv("ASPNETCORE_ENVIRONMENT"))
	if envName == "" || envName == "sit" || envName == "uat" || envName == "staging" {
		//shopId = "test_0001" //测试配送的门店信息
	}
	// 宠物saas 店铺的app_channel值

	// 宠物saas 店铺的app_channel值
	if userInfo.ScrmId != "" {
		mpShopAreaQueryRequest.ShopId = warehouseDeliveryRelation.ShopNo
		redis := GetRedisConn()
		val := redis.HGet("store:app_channel", financecode).Val()
		mpShopAreaQueryRequest.AppChannel = strings.Split(val, "|")[0]
	} else {
		mpShopAreaQueryRequest.ShopId = shopId
	}

	mpShopAreaQueryRequest.DeliveryServiceCode = 100029
	out, err := client.MPServer.MpShopAreaQuery(client.Ctx, mpShopAreaQueryRequest)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(400, baseResponse)
	}

	if out.Code != 200 {
		baseResponse.Message = out.Message
		return c.JSON(400, baseResponse)
	}

	var list []models.ShopAreaData
	err = json.Unmarshal([]byte(out.Data), &mpShopArea)
	x := (*[2]uintptr)(unsafe.Pointer(&mpShopArea))
	h := [3]uintptr{x[0], x[1], x[1]}
	err = json.Unmarshal(*(*[]byte)(unsafe.Pointer(&h)), &list)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(200, baseResponse)
	}
	var str strings.Builder
	str.WriteString("[")
	if len(list) > 0 {
		for i := 0; i < len(list); i++ {
			xx := strconv.FormatFloat(list[i].X, 'f', -1, 64)
			yy := strconv.FormatFloat(list[i].Y, 'f', -1, 64)
			if i == 0 {
				str.WriteString("[" + yy + "," + xx + "]")
			} else {
				str.WriteString(",[" + yy + "," + xx + "]")
			}
		}
	}
	str.WriteString("]")
	out.Code = 200
	out.Data = str.String()
	return c.JSON(200, out)

}
