#!/bin/sh

die () { echo "$@" ; exit 1; }

export NVM_DIR="$(cd ../../.. && pwd)"

\. "${NVM_DIR}/nvm.sh"

EXIT_CODE=$(nvm_resolve_alias ; echo $?)
[ $EXIT_CODE = "1" ] || die "nvm_resolve_alias without an argument did not return 1; got $EXIT_CODE"

for i in $(seq 1 10)
  do
  STABLE_ALIAS="$(nvm_resolve_alias test-stable-$i)"
  [ "_$STABLE_ALIAS" = "_v0.0.$i" ] \
    || die "'nvm_resolve_alias test-stable-$i' was not v0.0.$i; got $STABLE_ALIAS"
  UNSTABLE_ALIAS="$(nvm_resolve_alias test-unstable-$i)"
  [ "_$UNSTABLE_ALIAS" = "_v0.1.$i" ] \
    || die "'nvm_resolve_alias test-unstable-$i' was not v0.1.$i; got $UNSTABLE_ALIAS"
done

EXIT_CODE=$(nvm_resolve_alias nonexistent ; echo $?)
[ $EXIT_CODE = "2" ] || die "'nvm_resolve_alias nonexistent' did not return 2; got $EXIT_CODE"

STABLE="$(nvm_resolve_alias stable)"
[ "_$STABLE" = "_v0.0" ] || die "'nvm_resolve_alias stable' was not v0.0; got $STABLE"

NODE="$(nvm_resolve_alias node)"
[ "_$NODE" = "_stable" ] || die "'nvm_resolve_alias node' was not stable; got $NODE"

UNSTABLE="$(nvm_resolve_alias unstable)"
[ "_$UNSTABLE" = "_v0.1" ] || die "'nvm_resolve_alias unstable' was not v0.1; got $UNSTABLE"

IOJS="$(nvm_resolve_alias iojs)"
[ "_$IOJS" = "_iojs-v0.2" ] || die "'nvm_resolve_alias iojs' was not iojs-v0.2; got $IOJS"

echo "

v0.0.1
v0.0.2
v0.0.3
" > ../../../alias/test-multi-lines

EXPECTED='v0.0.1'
ACTUAL="$(nvm_resolve_alias test-multi-lines)"
EXIT_CODE="$(nvm_resolve_alias test-multi-lines 2>&1 >/dev/null; echo $?)"

[ "${ACTUAL}" = "${EXPECTED}" ] || die "expected >${EXPECTED}<, got >${ACTUAL}<"
[ "${EXIT_CODE}" = '0' ] || die "expected exit code 0, got ${EXIT_CODE}"
