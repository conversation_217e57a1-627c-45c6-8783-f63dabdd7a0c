package controller

import (
	"_/dto"
	"_/proto/ac"
	"_/proto/pc"
	"github.com/maybgit/glog"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @Summary micro-charity 查询活动列表
// @Tags 抽奖组件
// @Accept json
// @Produce json
// @Param model body ac.LuckyDrawActivityRequest true " "
// @Success 200 {object} ac.LuckyDrawActivityListRes
// @Failure 400 {object} ac.LuckyDrawActivityListRes
// @router /boss/luckyDraw/list [get]
func LuckyDrawActivityList(c echo.Context) error {
	client := ac.GetActivityCenterClient()
	out := new(ac.LuckyDrawActivityListRes)

	req := ac.LuckyDrawActivityRequest{
		Id:         cast.ToInt64(c.FormValue("id")),
		PageIndex:  cast.ToInt64(c.FormValue("page_index")),
		PageSize:   cast.ToInt64(c.FormValue("page_size")),
		IsIntegral: cast.ToInt32(c.FormValue("integral")),
	}
	if res, err := client.LD.LuckyDrawActivityList(client.Ctx, &req); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else if res.Msg != "" {
		out.Msg = res.Msg
		return c.JSON(400, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 查询活动详情
// @Tags 抽奖组件
// @Accept json
// @Produce json
// @Param model body ac.LuckyDrawActivityInfoReq true " "
// @Success 200 {object} ac.LuckyDrawActivityInfoRes
// @Failure 400 {object} ac.LuckyDrawActivityInfoRes
// @router /boss/luckyDraw/info [get]
func LuckyDrawActivityInfo(c echo.Context) error {
	client := ac.GetActivityCenterClient()
	out := new(ac.LuckyDrawActivityInfoRes)

	req := ac.LuckyDrawActivityInfoReq{
		Id: cast.ToInt64(c.FormValue("id")),
	}
	if res, err := client.LD.LuckyDrawActivityInfo(client.Ctx, &req); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else if res.Msg != "" {
		out.Msg = res.Msg
		return c.JSON(400, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary 编辑或新增活动
// @Tags 抽奖组件
// @Accept json
// @Produce json
// @Param model body ac.LuckyDrawActivity true " "
// @Success 200 {object} ac.BaseResponseNew
// @Failure 400 {object} ac.BaseResponseNew
// @router /boss/luckyDraw/edit [post]
func EditLuckyDrawActivity(c echo.Context) error {
	client := ac.GetActivityCenterClient()
	out := new(ac.BaseResponseNew)

	var model ac.LuckyDrawActivity
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, pc.BaseResponse{Code: 400, Message: err.Error()})
	}

	if len(model.Prizes) != 8 {
		out.Msg = "奖品配置有误"
		return c.JSON(400, out)
	}

	if res, err := client.LD.EditLuckyDrawActivity(client.Ctx, &model); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else if res.Msg != "" {
		out.Msg = res.Msg
		return c.JSON(400, out)
	} else {
		return c.JSON(200, res)
	}
}

// @Summary micro-charity 是否推荐到积分商城
// @Tags 抽奖组件
// @Accept json
// @Produce json
// @Param model body ac.LuckyDrawActivityRecommendReq true " "
// @Success 200 {object} ac.BaseResponseNew
// @Failure 400 {object} ac.BaseResponseNew
// @router /boss/luckyDraw/recommend [post]
func LuckyDrawActivityRecommend(c echo.Context) error {

	centerClient := ac.GetActivityCenterClient()

	out := new(dto.CommonHttpResponse)

	var req = ac.LuckyDrawActivityRecommendReq{}
	if err := c.Bind(&req); err != nil {
		glog.Error("LuckyDrawActivityRecommenderr ： ", err.Error())
		out.Message = "参数绑定异常"
		return c.JSON(400, out)
	}

	if req.Id <= 0 {
		out.Message = "活动id必填"
		return c.JSON(400, out)
	}

	if res, err := centerClient.LD.LuckyDrawActivityRecommend(centerClient.Ctx, &req); err != nil {
		out.Message = err.Error()
		return c.JSON(400, out)
	} else if res.Code != 200 {
		out.Message = res.Msg
		return c.JSON(400, out)
	} else {
		return c.JSON(200, out)
	}

}
