package controller

import (
	"_/proto/dac"
	"_/proto/dc"
	"_/proto/oc"
	"_/proto/pc"
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"sort"
	"strings"
)

func WarehouseData(c echo.Context) error {
	clientData := GetDataCenterClient()
	defer clientData.Conn.Close()
	defer clientData.Cf()

	clientDispatch := GetDispatchCenter()
	defer clientDispatch.Conn.Close()
	defer clientDispatch.Cf()

	var responseData = make(map[string]interface{})
	var warehouse = make(map[string]interface{})
	var warehouseShop = make(map[string]interface{})
	storeNum, err := clientDispatch.RPC.WarehouseList(context.Background(), &dc.WarehouseListRequest{Category: "4", Pageindex: 1, Pagesize: 9999})
	if err != nil {
		warehouse["store_num"] = 0
	} else {
		warehouse["store_num"] = storeNum.TotalCount
	}
	//前置仓关联门店列表
	warehouseRela, err := clientDispatch.RPC.GetWarehouseRelation(context.Background(), &dc.Empty{})
	if err == nil {
		for _, v := range warehouseRela.WarehouseRelationAarray {
			warehouseShop[v.ShopId] = v.Id //warehouseShop[门店名] = 仓库ID
		}
	}

	//获取前置仓门店列表
	shopNum, err := clientDispatch.RPC.GetStoreListByCategory(context.Background(), &dc.GetStoreListByCategoryRequest{Category: 4})
	if err != nil {
		warehouse["shop_num"] = 0
	} else {
		warehouse["shop_num"] = len(shopNum.FinanceCode)
	}

	cityNum, err := clientData.RPC.QueryStoreInfo(context.Background(), &dac.StoreInfoRequest{FinanceCode: shopNum.FinanceCode})
	var countArea = make(map[string]interface{})
	var countWarehouseArea = make(map[string]interface{})
	var shopSpreads = areaSlice{}
	var warehouseSpreads = areaSlice{}
	var warehouseShopID = make(map[string][]string, 0)
	if err != nil {
		warehouse["city_num"] = 0
	} else {
		var countCity = make(map[string]int)
		for _, v := range cityNum.Details {
			if countCity[v.City] == 0 && v.City != "" {
				countCity[v.City] = 1
			}
			if v.Bigregion != "" && v.Bigregion != "控股其他" && v.Bigregion != "顽皮家族" {
				//用于门店分布
				if countArea[v.Bigregion] == nil {
					countArea[v.Bigregion] = 1
				} else {
					countArea[v.Bigregion] = cast.ToInt(countArea[v.Bigregion]) + 1
				}
				//用于仓库分布
				if warehouseShop[v.FinanceCode] != nil {
					var key = cast.ToString(warehouseShop[v.FinanceCode]) + "_" + v.Bigregion
					if countWarehouseArea[key] == nil {
						countWarehouseArea[key] = 1
					}
				}
				//仓库关联的门店ID
				warehouseShopID[v.Bigregion] = append(warehouseShopID[v.Bigregion], v.FinanceCode)
			}
		}
		warehouse["city_num"] = len(countCity)
	}
	//仓库数量、辐射城市、辐射门店
	responseData["warehouse"] = warehouse
	for k, v := range countArea {
		var shopSpread = area{}
		shopSpread.Name = k
		shopSpread.Value = cast.ToString(v)
		shopSpreads = append(shopSpreads, &shopSpread)
	}
	//门店分布
	sort.Stable(shopSpreads)
	//取前7个
	if len(shopSpreads) > 7 {
		responseData["shop_spread"] = shopSpreads[0:7]
	} else {
		responseData["shop_spread"] = shopSpreads
	}

	var warehouseArea = make(map[string]interface{})
	glog.Info("前置仓仓库分布：", countWarehouseArea)
	for k, _ := range countWarehouseArea {
		areaAry := strings.Split(k, "_")
		if warehouseArea[areaAry[1]] == nil {
			warehouseArea[areaAry[1]] = 1
		} else {
			warehouseArea[areaAry[1]] = cast.ToInt(warehouseArea[areaAry[1]]) + 1
		}
	}
	for k, v := range warehouseArea {
		var warehouseSpread = area{}
		warehouseSpread.Name = k
		warehouseSpread.Value = cast.ToString(v)
		warehouseSpreads = append(warehouseSpreads, &warehouseSpread)
	}
	//仓库分布
	sort.Stable(warehouseSpreads)
	//取前7个
	if len(warehouseSpreads) > 7 {
		responseData["warehouse_spread"] = warehouseSpreads[0:7]
	} else {
		responseData["warehouse_spread"] = warehouseSpreads
	}

	//订单数据
	orderClient := oc.GetOrderServiceClient()
	productSales, err := orderClient.RPC.IntelligenceOrderSales(context.Background(), &oc.GetIntelligenceOrderRequest{FinanceCode: shopNum.FinanceCode, SaleType: 1})
	if err == nil {
		responseData["goods_top"] = productSales.Data
	} else {
		responseData["goods_top"] = []string{}
	}

	//分类销售排行
	productClient := GetDcProductClient()
	defer productClient.Conn.Close()
	defer productClient.Cf()
	var categoryParent = make(map[int32]string, 0)
	var categoryList = make(map[string][]string, 0)
	channelCategory, err := productClient.RPC.QueryChannelCategory(context.Background(), &pc.CategoryRequest{Where: &pc.Category{ChannelId: 1}})
	if err == nil {
		for _, v := range channelCategory.Details {
			if v.ParentId == 0 {
				categoryParent[v.Id] = v.Name
			} else {
				key := fmt.Sprintf("%s_%s", cast.ToString(v.ParentId), categoryParent[v.ParentId])
				categoryList[key] = append(categoryList[key], cast.ToString(v.Id))
			}
		}

		var categoryTops = areaSlice{}
		for k, v := range categoryList {
			key := strings.Split(k, "_")
			categorySales, err := orderClient.RPC.IntelligenceOrderSales(context.Background(), &oc.GetIntelligenceOrderRequest{FinanceCode: shopNum.FinanceCode, SaleType: 2, Search: strings.Join(v, ",")})
			if err == nil {
				var categoryTop = area{}
				categoryTop.Name = key[1]
				if len(categorySales.Data) > 0 {
					categoryTop.Value = categorySales.Data[0].Value
				} else {
					categoryTop.Value = "0"
				}
				categoryTops = append(categoryTops, &categoryTop)
			}
		}
		sort.Stable(categoryTops)
		//取前10个
		if len(categoryTops) > 10 {
			responseData["category_top"] = categoryTops[0:10]
		} else {
			responseData["category_top"] = categoryTops
		}
	} else {
		responseData["category_top"] = []string{}
	}

	//月销售业绩
	monthSales, err := orderClient.RPC.IntelligenceOrderSales(context.Background(), &oc.GetIntelligenceOrderRequest{FinanceCode: shopNum.FinanceCode, SaleType: 3})
	var monthDatas = make([]map[string]interface{}, 0)
	var totalMoney float32
	totalMoney = 0
	if err == nil {
		for _, v := range monthSales.Data {
			var monthData = make(map[string]interface{})
			v.Name = v.Name + "月"
			totalMoney += cast.ToFloat32(v.Value)
			monthData[v.Name] = v.Value
			monthDatas = append(monthDatas, monthData)
		}
		responseData["month_income"] = monthSales.Data
		responseData["month_datas"] = monthDatas
	} else {
		responseData["month_income"] = []string{}
		responseData["month_datas"] = []string{}
	}

	//大区销售业绩
	var areaIncomes = areaSlice{}
	for k, v := range warehouseShopID {
		if k != "控股其他" && k != "顽皮家族" {
			areaSales, err := orderClient.RPC.IntelligenceOrderSales(context.Background(), &oc.GetIntelligenceOrderRequest{FinanceCode: v, SaleType: 4})
			if err == nil {
				var areaIncome = area{}
				areaIncome.Name = k
				if len(areaSales.Data) > 0 {
					areaIncome.Value = areaSales.Data[0].Value
				} else {
					areaIncome.Value = "0"
				}
				areaIncomes = append(areaIncomes, &areaIncome)
			}
		}
	}
	//排序
	sort.Stable(areaIncomes)
	//取前7个
	if len(areaIncomes) > 7 {
		responseData["area_income"] = areaIncomes[0:7]
	} else {
		responseData["area_income"] = areaIncomes
	}

	//渠道销售业绩
	channelSales, err := orderClient.RPC.IntelligenceOrderSales(context.Background(), &oc.GetIntelligenceOrderRequest{FinanceCode: shopNum.FinanceCode, SaleType: 5})
	if err == nil {
		var total float32
		for _, v := range channelSales.Data {
			total += cast.ToFloat32(v.Value)
		}
		var channelSalesDatas = areaSlice{}
		for _, v := range channelSales.Data {
			var channelSalesData = area{}
			channelSalesData.Ratio = fmt.Sprintf("%.1f", cast.ToFloat32(v.Value)/total*100)
			channelSalesData.Name = v.Name
			channelSalesData.Value = v.Value
			channelSalesDatas = append(channelSalesDatas, &channelSalesData)
		}
		sort.Stable(channelSalesDatas)
		responseData["channel_income"] = channelSalesDatas
	} else {
		responseData["channel_income"] = []string{}
	}

	//总销售额
	responseData["total_money"] = totalMoney
	return c.JSON(200, responseData)
}

type area struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Ratio string `json:"ratio"`
}

type areaSlice []*area

func (s areaSlice) Len() int      { return len(s) }
func (s areaSlice) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s areaSlice) Less(i, j int) bool {
	return cast.ToFloat32(s[i].Value) > cast.ToFloat32(s[j].Value)
}
