package controller

import (
	"_/models"
	"_/proto/ac"
	"_/proto/crm"
	"_/proto/dac"
	"_/proto/dc"
	"_/proto/et"
	"_/proto/mk"
	"_/proto/mm"
	"_/proto/oc"
	"_/proto/pay"
	"_/proto/pc"
	"_/proto/pm"
	"_/proto/sh"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/metadata"
)

type BaseClient struct {
	Conn *grpc.ClientConn
	Ctx  context.Context
	Cf   context.CancelFunc
}

// 请求header添加到grpc上下文
func AppendToOutgoingContext(ctx context.Context, c ...echo.Context) context.Context {
	if len(c) == 0 {
		return context.Background()
	}

	ctx = metadata.AppendToOutgoingContext(ctx, "structOuterCode", c[0].Request().Header.Get("structOuterCode"))

	var userInfo models.LoginUserInfo
	userInfo.IsGeneralAccount = false
	claims, err := utils.GetPayloadDirectly(c[0])
	if err != nil {
		glog.Error("解析token错误: ", err)
	} else {
		userInfo.UserNo = cast.ToString(claims["userno"])
		userInfo.UserName = cast.ToString(claims["name"])
		//saas连锁ID
		userInfo.ChainId = cast.ToString(claims["ChainId"])
		userInfo.ScrmId = cast.ToString(claims["scrmid"])
	}

	//增加用户账号信息判断
	userInfo.FinancialCode = c[0].Request().Header.Get("financialCode") //财务编码
	TenantId := cast.ToString(claims["TenantId"])
	if userInfo.FinancialCode == "" && TenantId != "" {
		userInfo.FinancialCode = TenantId
	}

	userInfo.IsSkipGeneral = c[0].Request().Header.Get("IsSkipGeneral") == "true" //是否跳过总账号标识
	//保存信息到渠道商品【具有总帐号权限】
	adminAccount := config.GetString("adminAccountList")
	adminAccountArry := strings.Split(adminAccount, "|")
	adminAccountMap := utils.GenerateArryToMap(adminAccountArry)
	if _, ok := adminAccountMap[userInfo.UserNo]; ok {
		userInfo.IsGeneralAccount = true
	}
	bt, _ := json.Marshal(userInfo)
	ctx = metadata.AppendToOutgoingContext(ctx, "login_user_info", string(bt))

	category := cast.ToInt32(c[0].FormValue("category"))
	category_map := make(map[string]int32, 0)
	category_map["category"] = category
	category_bt, _ := json.Marshal(category_map)
	ctx = metadata.AppendToOutgoingContext(ctx, "category", string(category_bt))
	ctx = metadata.AppendToOutgoingContext(ctx, "X-Request-Id", utils.GenerateRandomStr(32))
	//channel,useragent
	channelID := c[0].Request().Header.Get("channel_id")
	userAgent := c[0].Request().Header.Get("user_agent")

	var platformChannel models.PlatformChannel
	if channelID != "" {
		platformChannel.ChannelId, _ = strconv.Atoi(channelID)
	}
	if userAgent != "" {
		platformChannel.UserAgent, _ = strconv.Atoi(userAgent)
	}

	grpcContext := models.GrpcContext{Channel: platformChannel, UserInfo: userInfo}

	grpcBt, _ := json.Marshal(grpcContext)
	return metadata.AppendToOutgoingContext(ctx, "grpc_context", string(grpcBt))
}

type OrderServiceClient struct {
	BaseClient
	RPC       oc.OrderServiceClient
	OES       oc.OrderExceptionServiceClient
	ROC       oc.RefundOrderServiceClient
	AfterSale oc.AfterSaleServiceClient
	Cart      oc.CartServiceClient
	GroupBy   oc.CartServiceClient
	Integral  oc.OrderIntegralServiceClient
}

func GetOrderServiceClient(c ...echo.Context) *OrderServiceClient {
	var client OrderServiceClient
	var err error
	url := config.GetString("grpc.order-center")
	//url = "10.1.1.242:11005"
	if url == "" {
		url = "127.0.0.1:11005"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = oc.NewOrderServiceClient(client.Conn)
		client.OES = oc.NewOrderExceptionServiceClient(client.Conn)

		client.ROC = oc.NewRefundOrderServiceClient(client.Conn)

		client.AfterSale = oc.NewAfterSaleServiceClient(client.Conn)
		client.Cart = oc.NewCartServiceClient(client.Conn)

		client.Integral = oc.NewOrderIntegralServiceClient(client.Conn)

		client.Ctx = AppendToOutgoingContext(context.Background(), c...)
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*60)
		return &client
	}
}

func (c *OrderServiceClient) Close() {
	c.Conn.Close()
	c.Cf()
}

// 阿闻到家的Grpc客户端
type UpetDjClient struct {
	Connection   *grpc.ClientConn
	UpetDjClient oc.UpetDjServiceClient
	Context      context.Context
}

// 获取阿闻到家的Grpc客户端
func GetUpetDjClient() *UpetDjClient {
	var grpcWrap = new(UpetDjClient)

	url := config.GetString("grpc.order-center")
	if url == "" {
		url = "127.0.0.1:11005" //7042
	}

	// 获取grpc链接对象
	conn, err := grpc.Dial(url, grpc.WithInsecure())
	if err != nil {
		glog.Error(err)
		return nil
	}
	if conn.GetState() == connectivity.TransientFailure {
		glog.Error(fmt.Sprintf("grpc建立链接失败，服务端远程地址：%s", url))
		return nil
	}
	grpcWrap.Connection = conn
	//获取grpc客户端
	grpcWrap.UpetDjClient = oc.NewUpetDjServiceClient(conn)

	//请求上下文
	grpcWrap.Context = context.Background()

	return grpcWrap
}

// 关闭链接
func (grpcWrap *UpetDjClient) Close() {
	// 关闭链接
	if grpcWrap.Connection.GetState() != connectivity.Shutdown {
		grpcWrap.Connection.Close()
	}
}

type DcProductClient struct {
	BaseClient
	RPC pc.DcProductClient
	CP  pc.DcChannelProductClient
}

func GetDcProductClient(c ...echo.Context) *DcProductClient {
	var client DcProductClient
	var err error
	url := config.GetString("grpc.product-center")
	//url = "127.0.0.1:11003"
	if url == "" {
		url = "127.0.0.1:11003"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = pc.NewDcProductClient(client.Conn)
		client.CP = pc.NewDcChannelProductClient(client.Conn)
		client.Ctx = AppendToOutgoingContext(context.Background(), c...)
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*1000)
		return &client
	}
}

func (c *DcProductClient) Close() {
	c.Conn.Close()
	c.Cf()
}

type DataCenterClient struct {
	BaseClient
	RPC dac.DatacenterServiceClient
	PU  dac.PickupServiceClient
}

type ScrmCenterClient struct {
	BaseClient
	RPC crm.FlagServiceClient
}

type ExternalClient struct {
	BaseClient
	RPC et.MtStoreServiceClient
	ESS et.ElmStoreServiceClient
}

func GetDataCenterClient(c ...echo.Context) *DataCenterClient {
	var client DataCenterClient
	var err error
	url := config.GetString("grpc.datacenter")
	//url = "10.1.1.242:10032"
	if url == "" {
		url = "127.0.0.1:10032" //改为10032
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = dac.NewDatacenterServiceClient(client.Conn)
		client.PU = dac.NewPickupServiceClient(client.Conn)
		client.Ctx = AppendToOutgoingContext(context.Background(), c...)
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*30)
		return &client
	}
}

func GetExternalClient(c ...echo.Context) *ExternalClient {
	var client ExternalClient
	var err error
	url := config.GetString("grpc.external")
	//url = "10.1.1.242:10032"
	if url == "" {
		url = "127.0.0.1:11031"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = et.NewMtStoreServiceClient(client.Conn)
		client.ESS = et.NewElmStoreServiceClient(client.Conn)
		client.Ctx = AppendToOutgoingContext(context.Background(), c...)
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*30)
		return &client
	}
}

// 获取SCRM的GRPC连接
func GetScrmCenterClient(c ...echo.Context) *ScrmCenterClient {
	var client ScrmCenterClient
	var err error
	url := config.GetString("grpc.crm")
	//url = "10.1.1.242:10032"
	if url == "" {
		url = "127.0.0.1:11011" //改为10032
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure(), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(1024*1024*1024))); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = crm.NewFlagServiceClient(client.Conn)
		client.Ctx = AppendToOutgoingContext(context.Background(), c...)
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*30)
		return &client
	}
}

func (d *DataCenterClient) Close() {
	d.Conn.Close()
	d.Cf()
}

func (d *ExternalClient) Close() {
	d.Conn.Close()
	d.Cf()
}

// 销活动的Grpc客户端
type PromotionClient struct {
	Connection      *grpc.ClientConn
	PromotionClient mk.PromotionServiceClient
	Context         context.Context
}

// 获取促销活动的Grpc客户端
func GetPromotionClient() *PromotionClient {
	var grpcWrap = new(PromotionClient)

	url := config.GetString("grpc.marketing-center")
	if url == "" {
		url = "127.0.0.1:7042" //7042
	}

	// 获取grpc链接对象
	conn, err := grpc.Dial(url, grpc.WithInsecure())
	if err != nil {
		glog.Error(err)
		return nil
	}
	if conn.GetState() == connectivity.TransientFailure {
		glog.Error(fmt.Sprintf("grpc建立链接失败，服务端远程地址：%s", url))
		return nil
	}
	grpcWrap.Connection = conn
	//获取grpc客户端
	grpcWrap.PromotionClient = mk.NewPromotionServiceClient(conn)

	//请求上下文
	grpcWrap.Context = context.Background()

	return grpcWrap
}

// 关闭链接
func (grpcWrap *PromotionClient) Close() {
	// 关闭链接
	if grpcWrap.Connection.GetState() != connectivity.Shutdown {
		grpcWrap.Connection.Close()
	}
}

type DispatchCenterClient struct {
	BaseClient
	RPC dc.WarehouseServiceClient
}

func GetDispatchCenter() *DispatchCenterClient {
	var client DispatchCenterClient
	var err error
	url := config.GetString("dispatch-center")
	//url = "10.1.1.242:11006"
	if url == "" {
		url = "127.0.0.1:11006"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = dc.NewWarehouseServiceClient(client.Conn)
		client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*30)
		return &client
	}
}

// 关闭链接
func (c *DispatchCenterClient) Close() {
	c.Conn.Close()
	c.Cf()
}

type DcMarketClient struct {
	BaseClient
	RPC mk.PromotionServiceClient
}

func GetDcMarketClient(c ...echo.Context) *DcMarketClient {
	var client DcMarketClient
	var err error
	url := config.GetString("grpc.marketing-center")
	if url == "" {
		url = "127.0.0.1:7042"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = mk.NewPromotionServiceClient(client.Conn)
		client.Ctx = AppendToOutgoingContext(context.Background(), c...)
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*500)
		return &client
	}
}

func (c *DcMarketClient) Close() {
	c.Conn.Close()
	c.Cf()
}

type UpetCenterClient struct {
	BaseClient
	RPC sh.ProductServiceClient
	DIS sh.DistributionServiceClient
}
type UpetCenterMemberClient struct {
	BaseClient
	RPC mm.MemberMergeServiceClient
}

func GetUpetMemberCenter() *UpetCenterMemberClient {
	var client UpetCenterMemberClient
	var err error
	url := config.GetString("grpc.upet-center")
	if url == "" {
		url = "127.0.0.1:11010"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = mm.NewMemberMergeServiceClient(client.Conn)
		client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*30)
		return &client
	}
}
func GetUpetCenter() *UpetCenterClient {
	var client UpetCenterClient
	var err error
	url := config.GetString("grpc.upet-center")
	if url == "" {
		url = "127.0.0.1:11010"
	}
	url = "127.0.0.1:11010"
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = sh.NewProductServiceClient(client.Conn)
		client.DIS = sh.NewDistributionServiceClient(client.Conn)
		client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*30)
		return &client
	}
}

type PMPetMillionsClient struct {
	BaseClient
	RPC pm.PetMillionsServiceClient
}

func GetPMPetMillionsClient(c ...echo.Context) *PMPetMillionsClient {
	var client PMPetMillionsClient
	var err error
	url := config.GetString("grpc.petmillions")
	//url = "10.1.1.248:7067"
	if url == "" {
		url = "127.0.0.1:7067"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure(), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(1024*1024*1024))); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.RPC = pm.NewPetMillionsServiceClient(client.Conn)
		client.Ctx = AppendToOutgoingContext(context.Background(), c...)
		client.Ctx, client.Cf = context.WithTimeout(client.Ctx, time.Second*500)
		return &client
	}
}

func (c *PMPetMillionsClient) Close() {
	c.Conn.Close()
	c.Cf()
}

type payCenterClient struct {
	BaseClient
	PayInfo   pay.PayInfoClient
	PayRefund pay.PayRefundClient
}

func GetpayInfoClient() *payCenterClient {
	var client payCenterClient
	var err error
	url := config.GetString("grpc.payCenter")
	if url == "" {
		url = "127.0.0.1:7036"
	}
	if client.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error(err)
		return nil
	} else {
		client.PayInfo = pay.NewPayInfoClient(client.Conn)
		client.PayRefund = pay.NewPayRefundClient(client.Conn)
		client.Ctx, client.Cf = context.WithTimeout(context.Background(), time.Second*50)
		return &client
	}
}

type acCenterClient struct {
	BaseClient
	Activity ac.ActivityServiceClient
}
