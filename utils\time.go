package utils

import "time"

var (
	DATE_TIME_LAYOUT = "2006-01-02"
)

//时间处理方法 后期可以更新到rp-kit包中

//StrTimeToUnixTime
// 字符串时间格式转转换成以秒为单位的整数
// timestamp 时间字符串 格式 Y-M-D H:i:s
// layout 渲染格式
func StrTimeToUnixTime(timestamp string, layout string) int64 {
	loc, _ := time.LoadLocation("Local") //获取时区
	tmp, _ := time.ParseInLocation(layout, timestamp, loc)
	return tmp.Unix()
}

// StrTimeToUnixMillisecondTime
// 字符串时间格式转转换成以毫秒为单位的整数
// timestamp 时间字符串 格式 Y-M-D H:i:s
// layout 渲染格式
func StrTimeToUnixMillisecondTime(timestamp string, layout string) int64 {
	loc, _ := time.LoadLocation("Local") //获取时区
	tmp, _ := time.ParseInLocation(layout, timestamp, loc)
	return tmp.UnixNano() / int64(time.Millisecond)
}

// UnixTimeToStrTime
// 将秒级时间时间戳转换成字符串
// timestamp 时间字符串
// layout 渲染格式
func UnixTimeToStrTime(unixTime int64, layout string) string {
	t := time.Unix(unixTime, 0)
	rs := t.Format(layout)
	return rs
}

// StrTimeToFormatStrTime
// 将字符串时间 转换成其他格式的字符串时间
// timestamp 时间字符串
// layout 渲染格式
func StrTimeToFormatStrTime(timestamp string, layout string, format string) string {
	loc, _ := time.LoadLocation("Local") //获取时区
	tmp, _ := time.ParseInLocation(layout, timestamp, loc)
	rs := tmp.Format(format)
	return rs
}
