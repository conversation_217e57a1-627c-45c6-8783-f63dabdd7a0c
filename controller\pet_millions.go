package controller

import (
	"_/models"
	"_/proto/pm"
	"_/utils"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	kit "github.com/tricobbler/rp-kit"

	"regexp"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/tealeg/xlsx"
)

// @Summary 创建疫苗库
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.CreateVaccineReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/vaccine/add [post]
func AddVaccine(c echo.Context) error {
	var model pm.CreateVaccineReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.CreateVaccine(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary  编辑疫苗库
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.CreateVaccineReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/vaccine/update [post]
func UpdateVaccine(c echo.Context) error {
	var model pm.CreateVaccineReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	out, _ := client.RPC.UpdateVaccine(client.Ctx, &model)
	return c.JSON(int(out.Code), out)
}

// @Summary 更新疫苗库状态
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.CreateVaccineReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/vaccine/updatestate [post]
func UpdateVaccineState(c echo.Context) error {
	var model pm.CreateVaccineReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.UpdateVaccineState(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取疫苗信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param id query string true "疫苗id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/vaccine/get [get]
func GetVaccine(c echo.Context) error {
	var model pm.CreateVaccineReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetVaccine(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 疫苗库列表数据
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param pageIndex query string true "分页页码"
// @Param pageSize query string true "每页条数"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/vaccine/list [get]
func GetVaccineList(c echo.Context) error {
	var model pm.CreateVaccineReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetVaccineList(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 商家下拉菜单集合信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param typeids query string true "分类类型id集合"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/alltype [get]
func GetAllTypeList(c echo.Context) error {
	typeIds := c.QueryParam("typeids")
	typeIdArry := strings.Split(typeIds, ",")
	out := new(pm.BaseRes)
	out.Code = 200

	selectValueList := models.SelectValueList{}
	for _, v := range typeIdArry {
		switch v {
		case "1":
			var list = make([]models.SelectValue, 0)
			shopTypeArry := []string{"1:社区店", "2:市场店", "3:ShopMall商场店", "4:商务写字楼", "5:民用住宅", "6:独立园区(含别墅/四合院)", "7:线上电商", "8:个人微商"}
			for _, v := range shopTypeArry {
				vArry := strings.Split(v, ":")
				selectValue := models.SelectValue{Value: cast.ToInt32(vArry[0]), Label: vArry[1]}
				list = append(list, selectValue)
			}
			selectValueList.ShopType = list
			break
		case "2":
			var list = make([]models.SelectValue, 0)
			petTypeArry := []string{"1001:犬", "1000:猫", "3:犬猫兼营", "1002:异宠"}
			for _, v := range petTypeArry {
				vArry := strings.Split(v, ":")
				selectValue := models.SelectValue{Value: cast.ToInt32(vArry[0]), Label: vArry[1]}
				list = append(list, selectValue)
			}
			selectValueList.PetType = list
			break
		case "3":
			var list = make([]models.SelectValue, 0)
			bussinessFormatTypeArry := []string{"1:宠物店", "2:犬猫舍", "3:宠物医院", "4:猫咖狗咖体验馆", "5:慈善组织", "6:收容领养机构", "7:培训学校"}
			for _, v := range bussinessFormatTypeArry {
				vArry := strings.Split(v, ":")
				selectValue := models.SelectValue{Value: cast.ToInt32(vArry[0]), Label: vArry[1]}
				list = append(list, selectValue)
			}
			selectValueList.BussinessFormatType = list
			break
		case "4":
			var list = make([]models.SelectValue, 0)
			bussinessTypeArry := []string{"1:活体领养与零售", "2:活体批发", "3:商品批发", "4:商品零售", "5:洗澡美容", "6:诊疗", "7:寄养", "8:宠物培训", "9:娱乐休闲(人)", "10:饮品食品(人)", "11:电商商城"}
			for _, v := range bussinessTypeArry {
				vArry := strings.Split(v, ":")
				selectValue := models.SelectValue{Value: cast.ToInt32(vArry[0]), Label: vArry[1]}
				list = append(list, selectValue)
			}
			selectValueList.BussinessType = list
			break
		case "5":
			var jsonArry = make([][]models.SelectValue, 0)
			var list = make([]models.SelectValue, 0)
			petChildDogTypeArry := []string{"北极狐", "狮毛狗", "阿富汗猎犬", "艾努犬", "万能梗", "阿克巴士犬", "秋田犬", "阿拉斯加雪橇犬", "阿尔卑斯达切斯勃拉克犬", "黑褐色猎浣羰犬", "美国斗牛犬", "美国爱斯墓糜犬", "美国猎狐犬", "美国比特犬", "美国斯塔福德郡梗", "美国水猎犬", "安纳托利32卡拉巴什牧羊犬", "安纳托利亚牧羊犬", "盎格鲁-法兰西", "阿彭策尔山地犬", "艾瑞格斯犬", "澳洲牧牛犬", "澳大利亚卡尔比犬", "澳大利亚牧羊犬", "澳大利亚梗", "奥地利黑褐猎犬", "奥地利短毛宾莎犬", "阿札瓦克犬", "巴黎山犬", "巴贝特犬", "巴辛吉犬", "阿提桑诺曼底短腿犬", "巴吉度猎犬", "阿蒂辛诺曼底短腿犬", "巴伐利亚山地猎犬", "猎免犬", "长胡须柯利犬", "法国狼犬", "贝得灵顿厚毛犬", "比利时格立芬犬", "马里努阿犬", "比利时牧羊犬", "比利时坦比连犬", "贝加马斯卡犬", "皮卡第犬", "比利牛斯牧羊犬", "伯尔尼兹山地犬", "比羰犬", "比利犬", "黑褐猎浣熊犬", "黑俄罗斯梗", "加斯科尼蓝色犬", "侦探猎犬", "布鲁泰克浣熊猎犬", "波涪尼旺犬", "边境牧羊犬", "边境梗", "俄罗斯狼犬", "波士顿狗", "法兰德斯牧牛狗", "拳狮犬", "帕金猎犬", "意大利布拉科犬", "枪猎犬", "奥弗涅指示猎犬", "波底内短毛垂耳猎犬", "布拉克.圣.热尔曼犬", "布里牧羊犬", "布列塔尼犬", "布魯塞尔杻毛猎犬", "牛头梗", "英国斗牛犬", "牛头獒犬", "凯恩犬", "卡南犬", "诺曹.塞拉", "卡斯特罗拉博雷罗犬", "卡迪根威尔士柯基犬", "加泰隆牧羊犬", "高加索", "查理士王小猎犬", "捷克福斯克犬", "乞沙比克猎犬", "阿图瓦犬", "阿特拉斯牧羊犬", "爱迪犬", "吉娃娃", "中国冠毛犬", "中国沙皮犬", "中国松狮犬", "西西里猎犬", "黄毛猎犬", "美国可卡犬", "柯利牧羊狗", "服勒大陆玩具犬", "棉花面纱犬", "克罗地亚牧羊犬", "卷毛寻回犬", "达克斯猎狗", "达尔马西旺狗", "短脚狄文梗狗", "丹麦布罗荷马獒犬", "布拉赫特", "瓦切特尔車德犬", "澳洲野狗", "杜宾犬", "阿根廷犬", "法国波尔多犬", "多萨", "德勒姆采帕里匈犬", "瑞典腊肠犬", "荷兰牧羊犬", "荷兰茨斯牟雄德犬", "英国的小猎犬", "东俄罗斯行业猎犬", "猎鹿犬", "英国可卡犬", "英国浣熊猎犬", "英国赛特犬", "英国牧羊犬", "英国史宾格犬", "可卡犬", "恩特布犬", "法兰西犬", "伊巴尼爾.皮卡第犬", "邦德-奧地梅犬", "爱斯墓靡犬", "爱沙尼亚猎犬", "拉山犬", "欧亚混血", "猛兽布列塔尼犬", "田野猎犬", "巴西菲拉犬", "芬兰猎犬", "平漫毛寻回犬", "短毛猎狐梗", "刚毛猎狐梗", "法国斗牛犬", "灵缇犬", "加斯科大猎犬", "德国猎梗", "德国指示猎梗", "德国平犬", "德国牧羊犬", "德国短毛指示犬", "德国绒毛犬", "德国硬毛波音达犬", "巨型雪纳瑞犬", "爱尔兰艾莫劳峡谷梗", "金毛猎犬", "哥顿塞特犬", "大丹狗", "大白熊犬", "大瑞士山地犬", "希腊猎犬", "格陵兰犬", "格雷伊猎犬", "凡丁犬", "萑欧丹尼斯道瓦", "萑欧丹尼斯道瓦犬", "哈瓦那犬", "萑夫瓦尔特犬", "每吉尼猎犬", "印加无毛狗", "爱尔兰长毛猎犬", "爱尔兰梗犬", "爱尔兰水猎犬", "爱尔兰猎狼犬", "意大利灵缇犬", "杰克罗素梗", "曰本犬", "日本中型犬", "日本狐狸犬", "日本梗犬", "卡累利阿熊犬", "荷兰卷尾狮毛狗", "凯利蓝梗", "匈牙利种牧羊犬", "小型荷兰水畜猎犬", "伊斯特拉牧羊犬", "克龙弗兰犬", "哥威斯犬", "拉布拉多犬", "湖畔梗", "兰开夏赫勒犬", "兰西尔犬", "拉品坡考旺犬", "拉脱维2猎犬", "兰伯格犬", "莱维斯克", "拉萨阿普索犬", "立陶宛猎犬", "罗奉犬", "隆德杭犬", "维兹拉狗", "马尔济斯犬", "曼彻斯特犬", "牧羊犬", "马士提夫犬", "中亚牧羊犬", "迷你牛头梗", "迷你笃宾犬", "迷你雪纳瑞犬", "混血", "莫斯科监察犬", "搀德犬", "纽波利顿犬", "新几内丑唱歆的犬", "纽芬兰犬", "诺波顿狐狸犬", "诺福克梗", "挪威布哈德犬", "挪威猎鹿犬", "诺里苛梗犬", "新斯科舍诱鸭寻回犬", "老丹麦鸟犬", "英国古代牧羊犬", "水獭猎犬", "奥瓦查克波德哈兰斯基犬", "蝴蝶犬", "京巴", "柯基犬", "葡萄牙犬", "皮罗.德.巴斯特.马罗奎因犬", "加纳利犬", "西班牙斗牛梗", "秘鲁印加兰花犬", "贝吉格里芬凡丁犬", "法老王猎犬", "普罗特猎犬", "指示犬", "龈狄芬犬", "波兰灰狗", "波兰猎犬", "波兰低地牧羊犬", "波美拉尼旺犬", "贵妇犬", "葡萄牙水犬", "卷毛指示犬", "巴哥犬", "波利犬", "波密犬", "丰山犬", "比利牛斯獒犬", "葡萄牙护卫犬", "瑞德朋猎浣熊犬", "罗得西亚脊背犬", "罗威纳犬", "俄罗斯花猎犬", "俄罗斯猎犬", "俄罗斯的小猎犬", "萨尔路斯猎狼犬", "大型西班牙猎犬", "圣伯纳犬", "东非猎犬", "萨摩耶犬", "萨普兰尼那克犬", "斯怡潘道斯犬", "斯差勒斯道瓦犬", "舒柏苛犬", "雪纳瑞", "苏格兰猎鹿犬", "苏格兰梗犬", "西里汶梗", "长毛猎犬", "沙皮犬", "喜乐蒂牧羊犬", "日本柴犬", "西施犬", "哈士奇", "西伯利2莱克犬", "丝毛梗", "斯凯岛梗", "斯卢夫猎犬", "斯涪伐克犬", "斯涪伐克猎犬", "爱尔兰软毛梗", "南俄罗斯犬", "西班牙獒犬", "西班牙向导猎犬", "意大利斯皮奥尼犬", "波美拉尼31丝毛狗", "斯塔比荷猎犬", "美国斯塔福德郡猎犬", "斯塔福郡斗牛梗", "标准型雪纳瑞犬", "斯塔利亚粗毛山地猎犬", "萨西克斯郡猎犬", "瑞士猎犬", "泰卢米安犬", "西蕕獒犬", "西蕕猎犬", "西蕕梗犬", "玩具猎狐梗", "特兰西瓦尼亚猎犬", "树丛浣熊措犬", "泰罗猎犬", "瑞典柯墓犬", "维希拉猎犬", "意大利狐狸犬", "威玛猎犬", "威尔斯柯墓犬", "威尔斯激飞猎犬", "威尔斯梗犬", "西高地白梗", "达切斯勃拉克犬", "荷兰水猎犬", "惠比特犬", "白色牧羊犬", "刚毛指示格里芬犬", "钢毛指示格里芬犬", "约克夏犬", "南斯拉夫猎犬", "未知", "泰迪", "贵宾", "中华田园犬", "金毛寻回犬", "比熊犬", "松狮", "萨摩耶", "博美", "玩具贵宾犬"}
			for _, v := range petChildDogTypeArry {
				vArry := strings.Split(v, ":")
				selectValue := models.SelectValue{Value: 0, Label: vArry[0]}
				list = append(list, selectValue)
			}
			jsonArry = append(jsonArry, list)

			catList := make([]models.SelectValue, 0)
			petChildCatTypeArry := []string{"阿比西尼", "美国短尾", "美国卷毛", "美国短毛", "美国硬毛猫", "巴厘猫", "孟加拉猫", "伯曼猫", "孟买猫", "英国短毛猫", "缅甸猫", "加州内高猫", "卡尔特猫", "重点色短毛猫", "柯尼斯卷毛猫", "威尔斯猫", "德文郡卷毛猫", "埃及猫", "异国短毛猫", "哈瓦那综猫", "日本短尾猫", "爪哇猫", "韩国花猫", "科拉特猫", "拉波卷毛猫", "缅因猫", "马恩猫", "混血", "杂交", "曼基康猫", "尼比龙猫", "挪威猫", "东方长毛猫", "波斯猫", "布偶猫", "俄罗斯蓝猫", "苏格兰折耳猫", "塞尔柯克卷毛猫", "暹罗猫", "西伯利旺猫", "新加坡猫", "雪鞋猫", "索马里猫", "加拿大无毛猫", "Tiffany", "东奇尼猫", "土耳其安哥拉猫", "土耳其梵猫", "约克巧克力猫", "龙猫", "其他", "加菲猫", "中华田园猫", "金吉拉猫"}
			for _, v := range petChildCatTypeArry {
				vArry := strings.Split(v, ":")
				selectValue := models.SelectValue{Value: 0, Label: vArry[0]}
				catList = append(catList, selectValue)
				list = append(list, selectValue)
			}
			jsonArry = append(jsonArry, catList)
			jsonArry = append(jsonArry, list)
			jsonArry = append(jsonArry, []models.SelectValue{})
			selectValueList.PetChildType = jsonArry
		}

	}
	outData, _ := json.Marshal(selectValueList)
	out.Data = string(outData)
	return c.JSON(200, out)
}

// @Summary 创建商家基础信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.CreateMarchentReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent/add [post]
func CreateMarchent(c echo.Context) error {
	var model pm.CreateMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.CreateMarchent(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取商户基础信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query int32 true "商户id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_base/get [get]
func GetMarchentBase(c echo.Context) error {
	var model pm.MarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}
	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetBaseMarchent(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取商户基础信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query int32 true "商户id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent/get [get]
func GetMarchent(c echo.Context) error {
	var model pm.GetBaseMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}
	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetMarchent(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 更新商家基础信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.CreateMarchentReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_base/update [post]
func UpdateBaseMarchent(c echo.Context) error {
	var model pm.CreateMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.UpdateBaseMarchent(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 更新主账号信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.MarchentReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent/update [post]
func UpdateMarchent(c echo.Context) error {
	var model pm.MarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.UpdateMarchent(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 更新合作状态
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.MarchentStateReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_state/update [post]
func UpdateMarchentState(c echo.Context) error {
	var model pm.MarchentStateReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.UpdateMarchentState(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 更新资质认证
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.MarchentQualifyReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_qualify/update [post]
func UpdateMarchentQualify(c echo.Context) error {
	var model pm.MarchentQualifyReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.UpdateMarchentQualify(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 更新经营信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.MarchentManagementReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_management/update [post]
func UpdateMarchentManagement(c echo.Context) error {
	var model pm.MarchentManagementReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.UpdateMarchentManagement(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 更新运营信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.MarchentOperateReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_operate/update [post]
func UpdateMarchentOperate(c echo.Context) error {
	var model pm.MarchentOperateReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.UpdateMarchentOperate(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 更新财务信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.MarchentFinanceReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_finance/update [post]
func UpdateMarchentFinance(c echo.Context) error {
	var model pm.MarchentFinanceReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.UpdateMarchentFinance(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 更新权限信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param product body pm.MarchentMemberReq true " "
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_member/update [post]
func UpdateMarchentMember(c echo.Context) error {
	var model pm.MarchentMemberReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}
	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.UpdateMarchentMember(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取资质认证信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query string true "商户id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_qualify/get [get]
func GetMarchentQualify(c echo.Context) error {
	var model pm.GetBaseMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetMarchentQualify(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取经营信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query string true "商户id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_management/get [get]
func GetMarchentManagement(c echo.Context) error {
	var model pm.GetBaseMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetMarchentManagement(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取运营信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query string true "商户id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_operate/get [get]
func GetMarchentOperate(c echo.Context) error {
	var model pm.GetBaseMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetMarchentOperate(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取财务信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query string true "商户id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_finance/get [get]
func GetMarchentFinance(c echo.Context) error {
	var model pm.GetBaseMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetMarchentFinance(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取用户信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query string true "商户id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_member/get [get]
func GetMarchentMember(c echo.Context) error {
	var model pm.GetBaseMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetMarchentMember(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取商家宠物
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query string true "商户id"
// @Param pageIndex query string true "分页索引"
// @Param pageSize query string true "分页大小"
// @Success 200 {object} pm.BaseRes{data=[]pm.MarchentPetListDto}
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_pet/list [get]
func GetMarchentPet(c echo.Context) error {
	var request = new(pm.GetBaseMarchentReq)
	var response = &pm.BaseRes{Code: http.StatusOK}
	// 绑定模型
	err := c.Bind(request)
	if err == nil {
		client := GetPMPetMillionsClient()
		defer client.Close()
		// 调用远程接口
		response, err = client.RPC.GetMarchentPet(client.Ctx, request)
	}

	if err != nil {
		glog.Error(err)
		return c.JSON(http.StatusOK, &pm.BaseRes{Code: http.StatusBadRequest, Message: err.Error()})
	}

	return c.JSON(http.StatusOK, response)
}

// @Summary 导出商家宠物
// @Tags 百万新宠
// @Param marchentid query string true "商户id"
// @Router /boss/petmillions/marchent_pet/export [get]
func ExportMarchentPet(c echo.Context) error {
	var request = new(pm.GetBaseMarchentReq)
	var response = &pm.BaseRes{Code: http.StatusOK}

	// 绑定模型
	err := c.Bind(request)
	if err == nil {

		var pageSize = 1024
		var exportData []*pm.MarchentPetListDto
		//追加表头数据
		exportData = append(exportData, &pm.MarchentPetListDto{NestCode: "窝代码", NestName: "窝名称", PetBreed: "品种", PetSex: "性别",
			PetCardId: "宠物身份证编码", PetCode: "宠物芯片号码", PlanRecordFirst: "首免时间", PlanRecordSecond: "二免时间", PlanRecordThird: "三免时间",
			DewormingFirst: "首次驱虫时间", DewormingSecond: "首次驱虫时间", DewormingThird: "首次驱虫时间", DeliveryState: "交付状态", DeliveryDate: "交付时间", DeliveryMobile: "客户手机号码"})
		// 获取grpc链接
		client := GetPMPetMillionsClient()
		defer client.Close()

		for i := 0; i < math.MaxInt16; i++ {
			request.PageIndex = int32(i + 1)
			request.PageSize = int32(pageSize)
			// 调用远程接口
			res, err := client.RPC.GetMarchentPet(client.Ctx, request)
			if err != nil {
				glog.Error(err)
				break
			}
			// 是否还有数据
			if res == nil || len(res.Data) == 0 {
				break
			}
			// json字符串转换为dto
			var paged []*pm.MarchentPetListDto
			err = json.Unmarshal([]byte(res.Data), &paged)
			if err == nil {
				exportData = append(exportData, paged...)
			} else {
				break
			}
			// 是否是最后一页
			if len(paged) < pageSize {
				break
			}
		}

		if len(exportData) > 1 {
			// 导出excel
			file := xlsx.NewFile()
			// 添加sheet页
			sheet, _ := file.AddSheet("Sheet1")
			for _, v := range exportData {
				row := sheet.AddRow()
				cellA := row.AddCell()
				cellA.Value = v.NestCode
				cellB := row.AddCell()
				cellB.Value = v.NestName
				cellC := row.AddCell()
				cellC.Value = v.PetBreed
				cellD := row.AddCell()
				cellD.Value = v.PetSex
				cellE := row.AddCell()
				cellE.Value = v.PetCardId
				cellF := row.AddCell()
				cellF.Value = v.PetCode
				cellG := row.AddCell()
				cellG.Value = v.PlanRecordFirst
				cellH := row.AddCell()
				cellH.Value = v.PlanRecordSecond
				cellI := row.AddCell()
				cellI.Value = v.PlanRecordThird
				cellJ := row.AddCell()
				cellJ.Value = v.DewormingFirst
				cellJ2 := row.AddCell()
				cellJ2.Value = v.DewormingSecond
				cellJ3 := row.AddCell()
				cellJ3.Value = v.DewormingThird
				cellK := row.AddCell()
				cellK.Value = v.DeliveryState
				cellL := row.AddCell()
				cellL.Value = v.DeliveryDate
				cellM := row.AddCell()
				cellM.Value = v.DeliveryMobile
			}

			//将数据存入buff中
			var buff bytes.Buffer
			if err = file.Write(&buff); err == nil {
				//设置请求头  使用浏览器下载
				c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("宠物信息")))
				return c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
			}
		}
	}

	if err != nil {
		response.Code = http.StatusBadRequest
		response.Message = err.Error()
	}

	return c.JSON(http.StatusOK, response)
}

// @Summary 获取商家客户列表
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query string true "商户id"
// @Param pageIndex query string true "分页索引"
// @Param pageSize query string true "分页大小"
// @Success 200 {object} pm.BaseRes{data=[]pm.MarchentDeliveryRecordListDto}
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_deliveryrecord/list [get]
func GetMarchentDeliveryRecord(c echo.Context) error {
	var request = new(pm.GetBaseMarchentReq)
	var response = &pm.BaseRes{Code: http.StatusOK}
	// 绑定模型
	err := c.Bind(request)
	if err == nil {
		client := GetPMPetMillionsClient()
		defer client.Close()
		// 调用远程接口
		response, err = client.RPC.GetMarchentDeliveryRecord(client.Ctx, request)
	}

	if err != nil {
		glog.Error(err)
		return c.JSON(http.StatusOK, &pm.BaseRes{Code: http.StatusBadRequest, Message: err.Error()})
	}

	return c.JSON(http.StatusOK, response)
}

// @Summary 导出商家客户
// @Tags 百万新宠
// @Param marchentid query string true "商户id"
// @Router /boss/petmillions/marchent_deliveryrecord/export [get]
func ExportMarchentDeliveryRecord(c echo.Context) error {
	var request = new(pm.GetBaseMarchentReq)
	var response = &pm.BaseRes{Code: http.StatusOK}

	// 绑定模型
	err := c.Bind(request)
	if err == nil {

		var pageSize = 1024
		var exportData []*pm.MarchentDeliveryRecordListDto
		exportData = append(exportData, &pm.MarchentDeliveryRecordListDto{Mobile: "客户手机号码", City: "城市", PetCardId: "宠物身份证编码", DateDay: "交付日期"})
		client := GetPMPetMillionsClient()
		defer client.Close()

		// 分页获取所有数据
		for i := 0; i < math.MaxInt16; i++ {
			request.PageIndex = int32(i + 1)
			request.PageSize = int32(pageSize)
			// 调用远程接口
			res, err := client.RPC.GetMarchentDeliveryRecord(client.Ctx, request)
			if err != nil {
				glog.Error(err)
				break
			}
			// 是否还有数据
			if res == nil || len(res.Data) == 0 {
				break
			}
			// 转换为dto
			var paged []*pm.MarchentDeliveryRecordListDto
			err = json.Unmarshal([]byte(res.Data), &paged)
			if err == nil {
				exportData = append(exportData, paged...)
			} else {
				glog.Error(err)
				break
			}
			// 是否是最后一页
			if len(paged) < pageSize {
				break
			}
		}

		//导出到excel
		if len(exportData) > 1 {
			// 导出excel
			file := xlsx.NewFile()
			// 添加sheet页
			sheet, _ := file.AddSheet("Sheet1")
			for _, v := range exportData {
				row := sheet.AddRow()
				cellA := row.AddCell()
				cellA.Value = v.Mobile
				cellB := row.AddCell()
				cellB.Value = v.City
				cellC := row.AddCell()
				cellC.Value = v.PetCardId
				cellD := row.AddCell()
				cellD.Value = v.DateDay
			}

			//将数据存入buff中
			var buff bytes.Buffer
			if err = file.Write(&buff); err == nil {
				//设置请求头  使用浏览器下载
				c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("客户信息")))
				return c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
			}
		}
	}

	if err != nil {
		response.Code = http.StatusBadRequest
		response.Message = err.Error()
	}

	return c.JSON(http.StatusOK, response)
}

// @Summary 获取商户信息列表
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param province_id query int32 false "省"
// @Param city_id query int32 false "市"
// @Param area_id query int32 false "区"
// @Param is_enabled query int32 false "合作状态"
// @Param pet_type query int32 false "商家经营品类"
// @Param shop_name query string false "商户名称"
// @Param pageIndex query int32 false "当前页"
// @Param pageSize query int32 false "每页个数"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent/list [get]
func GetMarchentList(c echo.Context) error {
	var request pm.GetMarchentListReq
	request.ProvinceId = cast.ToInt32(c.QueryParam("province_id"))
	request.CityId = cast.ToInt32(c.QueryParam("city_id"))
	request.AreaId = cast.ToInt32(c.QueryParam("carea_id"))
	request.IsEnabled = cast.ToInt32(c.QueryParam("is_enabled"))
	request.PetType = cast.ToInt32(c.QueryParam("pet_type"))
	request.ShopName = c.QueryParam("shop_name")
	request.PageIndex = cast.ToInt32(c.QueryParam("pageIndex"))
	request.PageSize = cast.ToInt32(c.QueryParam("pageSize"))

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetMarchentList(client.Ctx, &request); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 获取商户信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param marchentid query int32 true "商户id"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent_detail/get [get]
func GetMarchentDetail(c echo.Context) error {
	var model pm.GetBaseMarchentReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}
	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetMarchentDetail(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
	return c.JSON(200, nil)
}

// @Summary 商家信息导出
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param province_id query int32 false "省"
// @Param city_id query int32 false "市"
// @Param is_enabled query int32 false "合作状态"
// @Param pet_type query int32 false "商家经营品类"
// @Param shop_name query string false "商户名称"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent/export [post]
func ExportMarchentList(c echo.Context) error {
	var model pm.GetMarchentListReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}
	marchentList := []models.MarchentExportList{}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()

	//
	if out, err := client.RPC.GetExportMarchentList(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		err := json.Unmarshal([]byte(out.Data), &marchentList)
		if err != nil {
			return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
		}
	}
	if len(marchentList) == 0 {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: "无可下载的内容"})
	}

	// 生成一个新的文件
	file := xlsx.NewFile()
	// 添加sheet页
	sheet, _ := file.AddSheet("Sheet1")
	// 插入表头
	titleRow := sheet.AddRow()
	titleList := []string{"商户ID", "商家主账户名", "业务负责人-姓名", "业务负责人-手机号", "店铺名称", "城市", "地址", "业态类型", "活体品类", "活体品种", "总部运营负责人", "创建时间", "最后登陆时间"}
	for _, v := range titleList {
		cell := titleRow.AddCell()
		cell.Value = v
		//表头字体颜色
		cell.GetStyle().Font.Color = "********"
		cell.GetStyle().Font.Bold = true
		//居中显示
		cell.GetStyle().Alignment.Horizontal = "center"
		cell.GetStyle().Alignment.Vertical = "center"
	}

	// 插入内容
	for _, v := range marchentList {
		row := sheet.AddRow()
		row.WriteStruct(&v, -1)
	}

	//将数据存入buff中
	var buff bytes.Buffer
	if err := file.Write(&buff); err != nil {
		panic(err)
	}
	//设置请求头  使用浏览器下载
	c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("商户信息")))
	return c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
}

// @Summary 疫苗库信息导出
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/vaccine/export [post]
func ExportVaccineList(c echo.Context) error {
	var model pm.GetMarchentListReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}
	vaccineBankList := []models.VaccineBankExport{}

	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if out, err := client.RPC.GetExportVaccineList(client.Ctx, &pm.CreateVaccineReq{}); err != nil {
		return c.JSON(400, err)
	} else {
		err := json.Unmarshal([]byte(out.Data), &vaccineBankList)
		if err != nil {
			return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
		}
	}
	if len(vaccineBankList) == 0 {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: "无可下载的内容"})
	}

	// 生成一个新的文件
	file := xlsx.NewFile()
	// 添加sheet页
	sheet, _ := file.AddSheet("Sheet1")
	// 插入表头
	titleRow := sheet.AddRow()
	titleList := []string{"疫苗编号", "宠物种类", "生产公司", "疫苗名称", "预防疾病种类", "创建时间", "最后更新时间"}
	for _, v := range titleList {
		cell := titleRow.AddCell()
		cell.Value = v
		//表头字体颜色
		cell.GetStyle().Font.Color = "********"
		cell.GetStyle().Font.Bold = true
		//居中显示
		cell.GetStyle().Alignment.Horizontal = "center"
		cell.GetStyle().Alignment.Vertical = "center"
	}

	// 插入内容
	for _, v := range vaccineBankList {
		row := sheet.AddRow()
		row.WriteStruct(&v, -1)
	}

	//将数据存入buff中
	var buff bytes.Buffer
	if err := file.Write(&buff); err != nil {
		panic(err)
	}
	//设置请求头  使用浏览器下载
	c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("疫苗库")))
	return c.Stream(http.StatusOK, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
}

// @Summary 商家信息导入,如果某些信息校验失败会返回文件
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Success 200 {object} pm.BaseRes "反馈Code!=200,为有错误信息，错误数量DataCount标识，下载Excel文件的Id为 Message(客户端调用 /boss/petmillions/marchent/download?fileid= 即可下载 ) "
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/marchent/import [post]
func ImportMarchentInfo(c echo.Context) error {
	var response = &pm.BaseRes{Code: http.StatusBadRequest}
	// 通过FormFile函数获取客户端上传的文件
	_file, err := c.FormFile("file")
	if err != nil {
		response.Message = err.Error()
		return c.JSON(http.StatusOK, response)
	}
	//打开用户上传的文件
	src, err := _file.Open()
	if err != nil {
		response.Message = err.Error()
		return c.JSON(http.StatusOK, response)
	}

	// 创建目标文件，就是我们打算把用户上传的文件保存到什么地方
	// file.Filename 参数指的是我们以用户上传的文件名，作为目标文件名，也就是服务端保存的文件名跟用户上传的文件名一样
	uploadFilePath := filepath.Join(os.TempDir(), fmt.Sprintf("%s_%s", utils.RandString(12), _file.Filename))
	dst, err := os.Create(uploadFilePath)
	if err != nil {
		response.Message = err.Error()
		return c.JSON(http.StatusOK, response)
	}
	// 这里将用户上传的文件复制到服务端的目标文件
	if _, err = io.Copy(dst, src); err != nil {
		response.Message = err.Error()
		return c.JSON(http.StatusOK, response)
	}

	//解析xlsx文件
	xlFile, err := xlsx.OpenFile(uploadFilePath)
	if err != nil {
		response.Message = err.Error()
		return c.JSON(http.StatusOK, response)
	}

	defer func() {
		dst.Close()
		src.Close()
	}()

	//活体品类
	petTypeMap := map[string]int32{
		"犬":    1001,
		"猫":    1000,
		"犬猫兼营": 3,
		"异宠":   1002,
	}
	// 业务类型
	bussinessFormatTypeMap := map[string]int32{
		"宠物店":     1,
		"犬猫舍":     2,
		"宠物医院":    3,
		"猫咖狗咖体验馆": 4,
		"慈善组织":    5,
		"收容领养机构":  6,
		"培训学校":    7,
	}
	// 店铺类型
	shopTypeMap := map[string]int32{
		"社区店":           1,
		"市场店":           2,
		"ShopMall商场店":   3,
		"商务写字楼":         4,
		"民用住宅":          5,
		"独立园区(含别墅/四合院)": 6,
		"线上电商":          7,
		"个人微商":          8,
	}
	// 错误信息
	var rowErrMessageMap = make(map[int][]string)
	// 商户列表
	var marchentDtos []*pm.MarchentImportDto

	// 处理excel错误
	for _, sheet := range xlFile.Sheets {
		for i, row := range sheet.Rows { // 遍历每行的列读取
			if i == 0 {
				continue
			}
			var errMessage []string
			// 读取行记录到结构体
			var marchentRowData = new(models.MarchentInport)
			err := row.ReadStruct(marchentRowData)
			if err != nil {
				errMessage = append(errMessage, err.Error())
			} else {
				if len(marchentRowData.MerchantName) == 0 {
					errMessage = append(errMessage, "[商家主账户名]不能为空")
				}
				if len(marchentRowData.MerchantLeader) == 0 {
					errMessage = append(errMessage, "[总部运营负责人]不能为空")
				}
				if len(marchentRowData.MemberName) == 0 {
					errMessage = append(errMessage, "[业务负责人-姓名]不能为空")
				}
				if len(marchentRowData.MemberPhone) == 0 {
					errMessage = append(errMessage, "[业务负责人-手机号]不能为空")
				} else {
					// 正则表达式校验
					isMatch, _ := regexp.MatchString("^1[0-9]{10}", marchentRowData.MemberPhone)
					if !isMatch {
						errMessage = append(errMessage, "[业务负责人-手机号]格式错误")
					}
				}
				if len(marchentRowData.ManagementName) == 0 {
					errMessage = append(errMessage, "[店铺名称]不能为空")
				}
				if len(marchentRowData.ManagementCity) == 0 {
					errMessage = append(errMessage, "[城市]不能为空")
				}
				if len(marchentRowData.ManagementArea) == 0 {
					errMessage = append(errMessage, "[区域]不能为空")
				}
				if len(marchentRowData.ManagementAddress) == 0 {
					errMessage = append(errMessage, "[地址]不能为空")
				}
				if len(marchentRowData.ManagementType) > 0 {
					if _, ok := shopTypeMap[marchentRowData.ManagementType]; !ok {
						errMessage = append(errMessage, "[店铺类型]与当前种类不一致")
					}
				}
				if len(marchentRowData.ManagementBussinessFormatType) == 0 {
					errMessage = append(errMessage, "[业态类型]不能为空")
				} else {
					if _, ok := bussinessFormatTypeMap[marchentRowData.ManagementBussinessFormatType]; !ok {
						errMessage = append(errMessage, "[业态类型]与当前种类不一致")
					}
				}
				if len(marchentRowData.ManagementPetType) == 0 {
					errMessage = append(errMessage, "[活体品类]不能为空")
				} else {
					if _, ok := petTypeMap[marchentRowData.ManagementPetType]; !ok {
						errMessage = append(errMessage, "[活体品类]与当前种类不一致")
					}
				}
				if len(marchentRowData.ManagementPetVarietyType) == 0 {
					errMessage = append(errMessage, "[活体品种]不能为空")
				}
			}
			// 错误信息
			if len(errMessage) > 0 {
				// 追加错误信息
				rowErrMessageMap[i] = errMessage
				continue
			}

			// 构造dto
			marchentImportDto := new(pm.MarchentImportDto)
			marchentImportDto.RowIndex = int32(i)
			marchentImportDto.MerchantName = marchentRowData.MerchantName
			marchentImportDto.MerchantLeader = marchentRowData.MerchantLeader

			//业务负责人
			marchentImportDto.MemberName = marchentRowData.MemberName
			marchentImportDto.MemberPhone = marchentRowData.MemberPhone
			marchentImportDto.MemeberEmail = marchentRowData.MemeberEmail
			marchentImportDto.MemberBirthday = marchentRowData.MemberBirthday
			if marchentRowData.MemberSex == "男" {
				marchentImportDto.MemberSex = 1
			} else if marchentRowData.MemberSex == "女" {
				marchentImportDto.MemberSex = 2
			} else {
				marchentImportDto.MemberSex = 3
			}
			// 资质信息
			marchentImportDto.QualifyName = marchentRowData.QualifyName
			marchentImportDto.QualifyCreditCode = marchentRowData.QualifyCreditCode
			marchentImportDto.QualifyOwner = marchentRowData.QualifyOwner
			marchentImportDto.QualifyEstablish = marchentRowData.QualifyEstablish
			// 店铺信息
			marchentImportDto.ManagementName = marchentRowData.ManagementName
			marchentImportDto.ManagementCity = marchentRowData.ManagementCity
			marchentImportDto.ManagementArea = marchentRowData.ManagementArea
			marchentImportDto.ManagementAddress = marchentRowData.ManagementAddress
			marchentImportDto.ManagementType = 1
			if v, ok := shopTypeMap[marchentRowData.ManagementType]; ok {
				marchentImportDto.ManagementType = v
			}
			marchentImportDto.ManagementBussinessFormatType = bussinessFormatTypeMap[marchentRowData.ManagementBussinessFormatType]
			marchentImportDto.ManagementBussinessType = marchentRowData.ManagementBussinessType
			marchentImportDto.ManagementPetType = petTypeMap[marchentRowData.ManagementPetType]
			marchentImportDto.ManagementPetVarietyType = marchentRowData.ManagementPetVarietyType
			marchentImportDto.ManagementTelephone = marchentRowData.ManagementTelephone
			if marchentRowData.ManagementIsLinked == "是" {
				marchentImportDto.ManagementIsLinked = true
			}
			marchentImportDto.ManagementDesc = marchentRowData.ManagementDesc
			marchentImportDto.ManagementMeasure = marchentRowData.ManagementMeasure
			if len(marchentRowData.ManagementEmployeeNum) > 0 {
				number, _ := strconv.Atoi(marchentRowData.ManagementEmployeeNum)
				marchentImportDto.ManagementEmployeeNum = int32(number)
			}
			// 运营信息
			marchentImportDto.OperateDouyin = marchentRowData.OperateDouyin
			marchentImportDto.OperateKuaishou = marchentRowData.OperateKuaishou
			marchentImportDto.OperateGongzhong = marchentRowData.OperateGongzhong
			marchentImportDto.OperateShiping = marchentRowData.OperateShiping
			marchentImportDto.OperateWechat = marchentRowData.OperateWechat
			marchentImportDto.OperateJd = marchentRowData.OperateJd
			marchentImportDto.OperateTaobao = marchentRowData.OperateTaobao
			// 财务信息
			marchentImportDto.FinanceDeposit = marchentRowData.FinanceDeposit
			marchentImportDto.FinanceName = marchentRowData.FinanceName
			marchentImportDto.FinanceNum = marchentRowData.FinanceNum
			marchentImportDto.FinanceSelfDeposit = marchentRowData.FinanceSelfDeposit
			marchentImportDto.FinanceSelfName = marchentRowData.FinanceSelfName
			marchentImportDto.FinanceSelfNum = marchentRowData.FinanceSelfNum
			marchentImportDto.FinanceUserName = marchentRowData.FinanceUserName
			marchentImportDto.FinanceUserMobile = marchentRowData.FinanceUserMobile
			marchentDtos = append(marchentDtos, marchentImportDto)
		}
		break //只看第一个sheet的数据
	}
	// 本地校验是否通过
	if len(marchentDtos) > 0 {
		// 获取grpc链接
		client := pm.GetPetMillionsAppletsClient()
		defer client.Close()
		// 调用远程保存
		response, err := client.PetMillionsService.ImportMarchent(client.Ctx, &pm.MarchentImportReq{Marchents: marchentDtos})
		if err != nil {
			glog.Error(err)
			rowErrMessageMap[0] = append(rowErrMessageMap[0], err.Error())
		}
		if response.Code != 200 {
			// 解析远程错误信息
			var saveErrMessageMap = make(map[int][]string)
			json.Unmarshal([]byte(response.Message), &saveErrMessageMap)
			for key, value := range saveErrMessageMap {
				rowErrMessageMap[key] = append(rowErrMessageMap[key], value...)
			}
		}
	}
	// 是否有错误信息
	if len(rowErrMessageMap) > 0 {
		response.DataCount = int64(len(rowErrMessageMap))
		var fileName = fmt.Sprintf("%s.xlsx", kit.GetGuid32())
		response.Message = fileName
		// 新建excel文件
		f := excelize.NewFile()
		// 标题
		f.SetCellValue("Sheet1", "A1", "错误信息")
		var rowIndex int = 1
		for k, v := range rowErrMessageMap {
			rowIndex++
			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", rowIndex), fmt.Sprintf("第%d行:%s", k, strings.Join(v, ";")))
		}
		f.SaveAs(filepath.Join(os.TempDir(), fileName))
	} else {
		response.Code = http.StatusBadRequest
	}
	return c.JSON(http.StatusOK, response)
}

// @Summary 文件下载
// @Tags 百万新宠
// @Param fileid query string true "文件id"
// @Success 200 {object} pm.BaseRes
// @Router /boss/petmillions/marchent/download [get]
func DownloadMarchentInfo(c echo.Context) error {
	var fileName = c.QueryParam("fileid")
	var filePath = filepath.Join(os.TempDir(), fileName)
	_, err := os.Stat(filePath)
	if err != nil {
		return c.JSON(http.StatusBadRequest, &pm.BaseRes{Code: http.StatusBadRequest, Message: "文件已丢失"})
	}
	return c.Attachment(filePath, fileName)
}

// @Summary 获取宠物品种信息
// @Tags 百万新宠
// @Accept plain
// @Produce json
// @Param model body  pm.GetPetBreedsReq true " "
// @Success 200 {object} models.GetPetBreedsResponse
// @Failure 400 {object} pm.BaseRes
// @Router /boss/petmillions/breeds/get [post]
func GetPetBreeds(c echo.Context) error {
	var model pm.GetPetBreedsReq
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &pm.BaseRes{Code: 400, Message: err.Error()})
	}
	client := GetPMPetMillionsClient()
	defer client.Conn.Close()
	defer client.Cf()
	//
	if out, err := client.RPC.GetPetBreeds(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		if out.Code == 200 {
			if len(out.Data) > 0 {
				breeds := make([]models.PetBreedData, 0)
				_ = json.Unmarshal([]byte(out.Data), &breeds)
				res := models.GetPetBreedsResponse{
					Code: 200,
					Data: breeds,
				}
				return c.JSON(int(out.Code), res)
			}
		}
		return c.JSON(int(out.Code), out)
	}
	//return c.JSON(200, nil)
}

func petReferralRecordsSource(model pm.GetPetReferralRecordsReq) ([]*pm.GetPetReferralRecordsResUnit, error) {
	var (
		err     error
		grpcOut *pm.GetPetReferralRecordsRes
	)
	client := pm.GetPetMillionsAppletsClient()
	defer client.Conn.Close()
	defer client.Cf()
	if grpcOut, err = client.RPC.GetPetReferralRecords(client.Ctx, &model); err != nil {
		return nil, err
	}
	if grpcOut != nil {
		return grpcOut.Data, nil
	}
	return make([]*pm.GetPetReferralRecordsResUnit, 0), nil
}

// @Summary 宠物转诊记录
// @Tags 百万新宠
// @Accept json
// @Produce json
// @Param request query pm.GetPetReferralRecordsReq true " "
// @Success 200 {object} pm.GetPetReferralRecordsRes
// @Failure 400 {object} utils.ResponseGrpc
// @Router /boss/petmillions/marchent_pet/referral-records [Get]
func GetMarchentPetReferralRecords(c echo.Context) error {
	var (
		response utils.ResponseGrpc
		err      error
		data     []*pm.GetPetReferralRecordsResUnit
		model    pm.GetPetReferralRecordsReq
	)
	if err := c.Bind(&model); err != nil {
		response.Message = err.Error()
		return response.Failed(c)
	}
	if data, err = petReferralRecordsSource(model); err != nil {
		response.Message = err.Error()
		return response.Failed(c)
	}
	response.Details = data
	return response.Successful(c)
}

type GetMarchentPetReferralRecordsExportReq struct {
	MarchentName string `json:"marchent_name"`
	MarchentId   int    `json:"marchent_id"`
}

// @Summary 宠物转诊记录下载
// @Tags 百万新宠
// @Param marchent_id query int true "商户id"
// @Param marchent_name query string true "商户名"
// @Success 200 {object} pm.BaseRes
// @Failure 400 {object} models.BaseResponse
// @Router /boss/petmillions/marchent_pet/referral-records-export [get]
func GetMarchentPetReferralRecordsExport(c echo.Context) error {
	var (
		err      error
		data     []*pm.GetPetReferralRecordsResUnit
		response utils.ResponseGrpc
		intmid   int
	)
	intmid, _ = strconv.Atoi(c.QueryParam("marchent_id"))
	if data, err = petReferralRecordsSource(pm.GetPetReferralRecordsReq{
		MarchentId: int32(intmid),
		// PageNo:     1, PageSize: 10,
	}); err != nil {
		response.Message = err.Error()
		return response.Failed(c)
	}
	var sheetIndex = 1
	f := excelize.NewFile()
	sheetName := "Sheet" + strconv.Itoa(sheetIndex)
	f.NewSheet(sheetName)
	f.SetCellValue(sheetName, "A1", "商户ID")
	f.SetCellValue(sheetName, "B1", "商户名称")
	f.SetCellValue(sheetName, "C1", "宠物名称（客户取名）")
	f.SetCellValue(sheetName, "D1", "宠物种类")
	f.SetCellValue(sheetName, "E1", "宠物品种")
	f.SetCellValue(sheetName, "F1", "出生日期")
	f.SetCellValue(sheetName, "G1", "客户名称")
	f.SetCellValue(sheetName, "H1", "手机号")
	f.SetCellValue(sheetName, "I1", "病例结束日期")
	f.SetCellValue(sheetName, "J1", "就诊医院")
	f.SetCellValue(sheetName, "K1", "接诊医生")
	f.SetCellValue(sheetName, "L1", "病情诊断")
	f.SetCellValue(sheetName, "M1", "结算金额（元）")
	cellindex := 2
	for i := 0; i < len(data); i++ {
		cellindex = i + 2
		f.SetCellValue(sheetName, "A"+strconv.Itoa(cellindex), data[i].TargetId)
		f.SetCellValue(sheetName, "B"+strconv.Itoa(cellindex), data[i].TargetOrgname)
		f.SetCellValue(sheetName, "C"+strconv.Itoa(cellindex), data[i].PetName)
		f.SetCellValue(sheetName, "D"+strconv.Itoa(cellindex), data[i].PetKindof)
		f.SetCellValue(sheetName, "E"+strconv.Itoa(cellindex), data[i].PetVariety)
		f.SetCellValue(sheetName, "F"+strconv.Itoa(cellindex), data[i].PetBirthday)
		f.SetCellValue(sheetName, "G"+strconv.Itoa(cellindex), data[i].CusName)
		f.SetCellValue(sheetName, "H"+strconv.Itoa(cellindex), data[i].Cellphone)
		f.SetCellValue(sheetName, "I"+strconv.Itoa(cellindex), data[i].ApplyEndTime)
		f.SetCellValue(sheetName, "J"+strconv.Itoa(cellindex), data[i].ClinicName)
		f.SetCellValue(sheetName, "K"+strconv.Itoa(cellindex), data[i].ApplyPhysicianName)
		f.SetCellValue(sheetName, "L"+strconv.Itoa(cellindex), data[i].MainSymptom)
		f.SetCellValue(sheetName, "M"+strconv.Itoa(cellindex), data[i].ActualAmount)
	}
	//将数据存入buff中
	var buff bytes.Buffer
	if err = f.Write(&buff); err != nil {
		response.Message = "写入文件失败"
		return response.Failed(c)
	}
	var name = c.QueryParam("marchent_name") + time.Now().Format("20060102150405") + ".xlsx"
	f.SaveAs(name)
	c.Response().Header().Set(echo.HeaderContentDisposition, url.PathEscape("attachment; filename="+name))
	return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
}
