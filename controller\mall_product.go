package controller

import (
	"_/proto/pc"
	"_/utils"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"net/http"
)

// @Summary v2.17.4 电商仓商品价格列表
// @Tags 商品库
// @Accept plain
// @Param page_index query int false "分页索引"
// @Param page_size query int false "分页大小"
// @Param skuid query string false "skuid"
// @Success 200 {object} pc.MallProductPriceListRes
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/mall-product/price-list [get]
func MallProductPriceList(c echo.Context) error {
	out := pc.MallProductPriceListRes{
		Code: http.StatusBadRequest,
	}

	var params = new(pc.MallProductPriceListReq)
	params.PageIndex = cast.ToInt32(c.QueryParam("page_index"))
	params.PageSize = cast.ToInt32(c.QueryParam("page_size"))
	params.Skuid = c.QueryParam("skuid")

	client := GetDcProductClient(c)
	defer client.Close()

	resp, err := client.RPC.MallProductPriceList(client.Ctx, params)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if resp.Code != http.StatusOK {
		out.Message = resp.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = http.StatusOK
	out.Data = resp.Data
	out.Total = resp.Total
	return c.JSON(http.StatusOK, out)
}

// @Summary v2.17.4 电商仓商品价格移除
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param params body pc.IdRequest true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/mall-product/price-del [post]
func MallProductPriceDel(c echo.Context) error {
	baseResponse := pc.BaseResponse{
		Code: http.StatusBadRequest,
	}

	// 序列化参数
	params := new(pc.IdRequest)
	if err := c.Bind(params); err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return c.JSON(400, baseResponse)
	}
	params.CreateId = userInfo.UserNo
	params.CreateName = userInfo.UserName
	params.IpAddr = c.RealIP()
	params.IpLocation = GetIpAddress(c.RealIP())

	client := GetDcProductClient(c)
	defer client.Close()

	resp, err := client.RPC.MallProductPriceDel(client.Ctx, params)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}
	if resp.Code != http.StatusOK {
		baseResponse.Message = resp.Message
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	baseResponse.Code = http.StatusOK
	return c.JSON(http.StatusOK, baseResponse)
}

// @Summary v2.17.4 电商仓商品价格编辑
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param params body pc.MallProductPriceEditReq true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/mall-product/price-edit [post]
func MallProductPriceEdit(c echo.Context) error {
	baseResponse := pc.BaseResponse{
		Code: http.StatusBadRequest,
	}

	// 序列化参数
	params := new(pc.MallProductPriceEditReq)
	if err := c.Bind(params); err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return c.JSON(400, baseResponse)
	}

	params.CreateId = userInfo.UserNo
	params.CreateName = userInfo.UserName
	params.IpAddr = c.RealIP()
	params.IpLocation = GetIpAddress(c.RealIP())

	client := GetDcProductClient(c)
	defer client.Close()

	resp, err := client.RPC.MallProductPriceEdit(client.Ctx, params)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}
	if resp.Code != http.StatusOK {
		baseResponse.Message = resp.Message
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	baseResponse.Code = http.StatusOK
	return c.JSON(http.StatusOK, baseResponse)
}
