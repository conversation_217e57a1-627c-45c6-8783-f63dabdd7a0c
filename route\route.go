package route

import (
	"_/controller"

	"github.com/labstack/echo/v4"
)

// 路由
func Route(e *echo.Echo) *echo.Echo {
	//登录
	e.POST("/boss/login", controller.Login)
	//e.GET("/loginreturn", controller.LoginReturn)
	//广告
	//获取展示广告 -- 竖屏使用，暂时没有配置权限，后期迁移到product-api
	e.GET("/boss/advertisement/launch", controller.AdvertisementLaunchGet)
	e.POST("/boss/noAuth/order/push-delivery", controller.PushDelivery)
	e.POST("/boss/noAuth/order/elm-pick-complete", controller.ElmOrderPickComplete)
	e.POST("/boss/noAuth/order/mt-pick-complete", controller.MtOrderPickComplete)
	e.POST("/boss/noAuth/store/AllStoreSyncPrice", controller.AllStoreSyncPrice)
	e.POST("/boss/noAuth/message/messagecreate", controller.MessageCreate)

	// 协议管理
	e.POST("/boss/datacenter/protocol/create", controller.CreateProtocol)
	e.POST("/boss/datacenter/protocol/list", controller.GetProtocolList)

	return e
}
