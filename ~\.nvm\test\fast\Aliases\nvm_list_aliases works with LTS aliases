#!/bin/sh

\. ../../../nvm.sh

die () {
  echo "$@"
  exit 1
}

set -e

MOCKS_DIR="../Unit tests/mocks"

# sample output at the time the test was written
TAB_PATH="$MOCKS_DIR/nodejs.org-dist-index.tab"
nvm_download() {
  cat "$TAB_PATH"
}

nvm_alias_path() {
  nvm_echo "../../../alias"
}

nvm ls-remote >/dev/null || die 'nvm ls-remote (to populate LTS aliases) failed'

EXPECTED_OUTPUT="$(cat "$MOCKS_DIR/lts-star.txt")"
STDOUT_OUTPUT="$(nvm_list_aliases "lts/*")"
STDERR_OUTPUT="$(nvm_list_aliases "lts/*" 2>&1 >/dev/null)"

[ -z "${STDERR_OUTPUT}" ] || die "expected no stderr output, got >${STDERR_OUTPUT}<"
[ "${STDOUT_OUTPUT}" = "${EXPECTED_OUTPUT}" ] || die "expected ${EXPECTED_OUTPUT}, got >${STDOUT_OUTPUT}<"
