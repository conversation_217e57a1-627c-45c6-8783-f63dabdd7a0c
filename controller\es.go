package controller

import (
	"_/proto/dac"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/labstack/echo/v4"
)

// EsInitStore 初始化es门店
func EsInitStore(c echo.Context) error {
	client := dac.GetDataCenterClient()
	if out, err := client.RPC.EsInitStore(client.Ctx, new(empty.Empty)); err != nil {
		return c.JSON(400, dac.Response{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// EsInitStore 初始化指定门店商品
func EsInitStoreProduct(c echo.Context) error {
	req := new(dac.EsInitStoreProductReq)

	if err := c.Bind(req); err != nil {
		return c.JSON(400, dac.Response{Code: 400, Message: err.<PERSON><PERSON>r()})
	}

	client := dac.GetDataCenterClient()
	if out, err := client.RPC.EsInitStoreProduct(client.Ctx, req); err != nil {
		return c.JSON(400, dac.Response{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
