package utils

import (
	"errors"
	"strings"

	"github.com/tealeg/xlsx"
)

// 字符串切片去重
func SliceUnique(list []string) []string {
	result := make([]string, 0, len(list))
	temp := map[string]struct{}{}
	for _, item := range list {
		if _, ok := temp[item]; !ok {
			temp[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

func SliceContains(a []string, x string) bool {
	for _, n := range a {
		if x == n {
			return true
		}
	}
	return false
}

type XlsxRow struct {
	Row  *xlsx.Row
	Data []string
}

func NewRow(row *xlsx.Row, data []string) *XlsxRow {
	return &XlsxRow{
		Row:  row,
		Data: data,
	}
}

func (row *XlsxRow) SetRowTitle() error {
	return generateRow(row.Row, row.Data)
}

func (row *XlsxRow) GenerateRow() error {
	return generateRow(row.Row, row.Data)
}

func generateRow(row *xlsx.Row, rowStr []string) error {
	if rowStr == nil {
		return errors.New("没有数据可生成Excel")
	}
	for _, v := range rowStr {
		cell := row.AddCell()
		cell.SetString(v)
	}
	return nil
}

// 变量a:="aa", 变量b:="aa,bb,cc", 怎么判断aa是否在b中
func InStringSlice(needle string, haystack string) bool {
	slice := strings.Split(haystack, ",")
	for _, v := range slice {
		if needle == v {
			return true
		}
	}
	return false
}
