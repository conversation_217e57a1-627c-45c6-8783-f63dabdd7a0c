# Security

Please file a private vulnerability report via GitHub, email [@ljharb](https://github.com/ljharb), or see https://tidelift.com/security if you have a potential security vulnerability to report.

## OpenSSF CII Best Practices

[![CII Best Practices](https://bestpractices.coreinfrastructure.org/projects/684/badge)](https://bestpractices.coreinfrastructure.org/projects/684)

There are three “tiers”: passing, silver, and gold.

### Passing
We meet 100% of the “passing” criteria.

### Silver
We meet 100% of the “silver” criteria.

### Gold
We meet 78% of the “gold” criteria. The gaps are as follows:
  - because we only have one maintainer, the project has no way to continue if that maintainer stops being active.
  - We do not include a copyright or license statement in each source file. Efforts are underway to change this archaic practice into a suggestion instead of a hard requirement.

## Threat Model

See [THREAT_MODEL.md](.github/THREAT_MODEL.md).

## Incident Response Plan

Please see our [Incident Response Plan](.github/INCIDENT_RESPONSE_PLAN.md).
