package controller

import (
	"_/proto/oc"
	"_/utils"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
	"google.golang.org/grpc/metadata"
)

// @Summary 团长式拼团团分页列表
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param request_from query int32 false "页面来源,默认0-订单列表，1-实物订单，2-虚拟订单"
// @Param group_status query string false "团状态 0拼团中 1拼团成功 2拼团失败"
// @Param search_type query string true "搜索类型 1团长手机 2团编码 3财务编码 4团长单位名"
// @Param keyword query string false " "
// @Param group_take_type query string false "团长代收状态 0不代收 1代收"
// @Param time_type query string true "时间搜索类型 1开团时间 2拼成时间 3失败时间"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param page_size query string false "每页数量"
// @Param page_index query string false "页"
// @Success 200 {object} oc.CommunityGroupOrderListResponse
// @Failure 400 {object} oc.CommunityGroupOrderListResponse
// @Router /boss/ordercenter/order/community_group/group-list [get]
func OrderCommunityGroupList(c echo.Context) error {
	in := &oc.CommunityGroupOrderListRequest{
		RequestFrom:   cast.ToInt32(c.QueryParam("request_from")),
		GroupStatus:   c.QueryParam("group_status"),
		SearchType:    c.QueryParam("search_type"),
		Keyword:       c.QueryParam("keyword"),
		GroupTakeType: c.QueryParam("group_take_type"),
		TimeType:      c.QueryParam("time_type"),
		StartTime:     c.QueryParam("start_time"),
		EndTime:       c.QueryParam("end_time"),
		PageIndex:     cast.ToInt32(c.QueryParam("page_index")),
		PageSize:      cast.ToInt32(c.QueryParam("page_size")),
	}
	if len(in.StartTime) == 0 && len(in.EndTime) == 0 {
		return r.NewHTTPError(400, "请选择下单时间范围")
	}
	if in.PageSize < 1 {
		in.PageSize = 20
	}
	if in.PageIndex < 1 {
		in.PageIndex = 1
	}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}
	if userInfo.FinancialCode == "" {
		in.UserNo = userInfo.UserNo
	} else {
		in.ShopIds = []string{userInfo.FinancialCode}
	}
	client := oc.GetOrderServiceClient()
	client.Ctx = metadata.AppendToOutgoingContext(client.Ctx,
		"user-no", userInfo.UserNo,
	)
	out, err := client.CG.OrderList(client.Ctx, in)
	if err != nil {
		return c.JSON(400, &oc.CommunityGroupOrderListResponse{Code: 400, Message: err.Error()})
	} else if out.Code != 200 {
		return c.JSON(400, out)
	}
	return c.JSON(200, out)
}

// @Summary 团长式拼团团员订单分页列表
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param group_id query string false "团id"
// @Param order_status query string false "订单状态:20101.未接单;20102.已接单;20103.配送中;20104.已送达;20105.已取货;20106.已完成;20107.已取消;"
// @Param search_type query string false "搜索类型 1订单号 2收货人姓名 3收货手机号 4下单手机"
// @Param keyword query string false " "
// @Param time_type query string false "时间搜索类型 1开团时间 2拼成时间 3失败时间"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param page_size query string false "每页数量"
// @Param page_index query string false "页"
// @Success 200 {object} oc.AwenParentOrderListResponse
// @Failure 400 {object} oc.AwenParentOrderListResponse
// @Router /boss/ordercenter/order/community_group/member-list [get]
func OrderCommunityGroupMemberList(c echo.Context) error {
	params := &oc.AwenParentOrderListRequest{
		SearchType:           cast.ToInt32(c.QueryParam("search_type")),
		Keyword:              c.QueryParam("keyword"),
		TimeType:             cast.ToInt32(c.QueryParam("time_type")),
		StartTime:            c.QueryParam("start_time"),
		EndTime:              c.QueryParam("end_time"),
		OrderStatus:          cast.ToInt32(c.QueryParam("order_status")),
		PageIndex:            cast.ToInt32(c.QueryParam("page_index")),
		PageSize:             cast.ToInt32(c.QueryParam("page_size")),
		QuerySpecial:         1,
		OrderGroupActivityId: cast.ToInt32(c.QueryParam("group_id")),
	}
	//默认分页大小20
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageIndex <= 0 {
		params.PageIndex = 1
	}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}
	if userInfo.FinancialCode == "" {
		params.UserNo = userInfo.UserNo
	} else {
		params.Shopids = []string{userInfo.FinancialCode}
	}
	client := oc.GetOrderServiceClient()
	client.Ctx = metadata.AppendToOutgoingContext(client.Ctx,
		"user-no", userInfo.UserNo,
	)
	if out, err := client.RPC.AwenParentOrderList(client.Ctx, params); err != nil {
		glog.Error("调用AwenOrderList失败，", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 阿闻团长拼团制-团员订单导出数据到EXCEL
// @Tags 订单中心
// @Param AwenOrderExport body oc.CommunityGroupOrderListRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/community_group/member-order-export [post]
func AwenCommunityGroupMemberOrderExport(c echo.Context) error {
	model := new(oc.CommunityGroupOrderListRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	model.Ip = strings.Split(c.Request().RemoteAddr, ":")[0]
	model.IpLocation = GetIpAddress(model.Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}

	model.UserNo = UserNo
	skip := config.GetString("export-skip-user")
	if skip != "" && userInfo.UserNo == skip {
		model.UserNo = ""
	}

	if len(userInfo.FinancialCode) > 0 {
		model.ShopIds = append(model.ShopIds, userInfo.FinancialCode)
	}

	taskContent := 10
	switch model.RequestFrom {
	case 1: // 实物
		taskContent = 11
	case 2: // 虚拟
		taskContent = 12
	default:
		//
	}
	//创建导出任务
	err = createOrderExportTask(UserNo, userInfo.UserName, model.Ip, model.IpLocation, kit.JsonEncode(model), taskContent, "", "", 1, "")
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, oc.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}

// @Summary 团长式拼团团员实物子订单分页列表
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param group_id query string true "团id"
// @Param keyword query string false "搜索关键字-订单号"
// @Param order_status query string false "订单状态:20101.未接单;20102.已接单;20103.配送中;20104.已送达;20105.已取货;20106.已完成;20107.已取消;"
// @Param page_size query string false "每页数量"
// @Param page_index query string false "页"
// @Success 200 {object} oc.AwenMaterOrderListResponse
// @Failure 400 {object} oc.AwenMaterOrderListResponse
// @Router /boss/ordercenter/order/mater/group-list [get]
func AwenMaterGroupOrderList(c echo.Context) error {
	params := &oc.AwenMaterOrderListRequest{
		SearchType:           cast.ToInt32(c.QueryParam("search_type")),
		Keyword:              c.QueryParam("keyword"),
		TimeType:             cast.ToInt32(c.QueryParam("time_type")),
		StartTime:            c.QueryParam("start_time"),
		EndTime:              c.QueryParam("end_time"),
		ProductName:          c.QueryParam("product_name"),
		ChannelId:            cast.ToInt32(c.QueryParam("channel_id")),
		OrderStatus:          cast.ToInt32(c.QueryParam("order_status")),
		OrderType:            c.QueryParam("order_type"),
		DeliveryType:         cast.ToInt32(c.QueryParam("delivery_type")),
		PayMode:              cast.ToInt32(c.QueryParam("pay_mode")),
		PageIndex:            cast.ToInt32(c.QueryParam("page_index")),
		PageSize:             cast.ToInt32(c.QueryParam("page_size")),
		AppChannel:           cast.ToInt32(c.QueryParam("app_channel")),
		OrderGroupActivityId: cast.ToInt32(c.QueryParam("group_id")),
	}

	//默认分页大小20
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageIndex <= 0 {
		params.PageIndex = 1
	}

	if params.OrderGroupActivityId < 1 {
		return r.NewHTTPError(400, "拼团id错误")
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}

	if userInfo.FinancialCode == "" {
		params.UserNo = userInfo.UserNo
	} else {
		params.Shopids = []string{userInfo.FinancialCode}
	}
	glog.Info("团长拼团实物订单查询请求参数，userInfo:", kit.JsonEncode(userInfo), ",param:", kit.JsonEncode(params))
	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenMaterOrderList(client.Ctx, params); err != nil {
		glog.Error("团长拼团调用AwenOrderList失败，", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 团长式拼团团员虚拟子订单分页列表
// @Tags 订单中心
// @Accept plain
// @Produce json
// @Param group_id query string true "团id"
// @Param keyword query string false "搜索关键字-订单号"
// @Param order_status query string false "订单状态:20101.未接单;20102.已接单;20103.配送中;20104.已送达;20105.已取货;20106.已完成;20107.已取消;"
// @Param page_size query string false "每页数量"
// @Param page_index query string false "页"
// @Success 200 {object} oc.AwenVirtualOrderListResponse
// @Failure 400 {object} oc.AwenVirtualOrderListResponse
// @Router /boss/ordercenter/order/virtual/group-list [get]
func AwenVirtualGroupOrderList(c echo.Context) error {
	params := &oc.AwenVirtualOrderListRequest{
		SearchType:           cast.ToInt32(c.QueryParam("search_type")),
		Keyword:              c.QueryParam("keyword"),
		TimeType:             cast.ToInt32(c.QueryParam("time_type")),
		StartTime:            c.QueryParam("start_time"),
		EndTime:              c.QueryParam("end_time"),
		ProductName:          c.QueryParam("product_name"),
		ChannelId:            cast.ToInt32(c.QueryParam("channel_id")),
		OrderStatus:          cast.ToInt32(c.QueryParam("order_status")),
		OrderType:            c.QueryParam("order_type"),
		PageIndex:            cast.ToInt32(c.QueryParam("page_index")),
		PageSize:             cast.ToInt32(c.QueryParam("page_size")),
		PayMode:              cast.ToInt32(c.QueryParam("pay_mode")),
		OrderGroupActivityId: cast.ToInt32(c.QueryParam("group_id")),
	}

	//默认分页大小20
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageIndex <= 0 {
		params.PageIndex = 1
	}

	if params.OrderGroupActivityId < 1 {
		return r.NewHTTPError(400, "拼团id错误")
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}

	if userInfo.FinancialCode == "" {
		params.UserNo = userInfo.UserNo
	} else {
		params.Shopids = []string{userInfo.FinancialCode}
	}

	glog.Info("团长拼团虚拟订单查询请求参数，userInfo:", kit.JsonEncode(userInfo), ",param:", kit.JsonEncode(params))
	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.AwenVirtualOrderList(client.Ctx, params); err != nil {
		glog.Error("团长拼团虚拟订单调用AwenOrderList失败，", err.Error())
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}
