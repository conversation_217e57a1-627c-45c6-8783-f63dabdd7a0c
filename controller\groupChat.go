package controller

import (
	"_/models"
	"_/proto/ac"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// QueryGroupChat
// @summary 查询街道群
// @tags 全国社群
// @produce json
// @param id query integer false "省id"
// @param type query integer false "活动类型 1，宠友服务群 2，抗疫补给站"
// @Param page_index query int false "页码"
// @Param page_size query int false "页大小"
// @success 200 {object} models.QueryGroupChatResponse
// @failure 400 {object} models.QueryGroupChatResponse
// @router /boss/groupChat/query [get]
func QueryGroupChat(c echo.Context) error {
	out := new(models.QueryGroupChatResponse)
	var in ac.QueryGroupChatRequest
	// 参数绑定
	in.Id = cast.ToInt64(c.FormValue("id"))
	in.Type = cast.ToInt32(c.FormValue("type"))
	in.PageIndex = cast.ToInt32(c.FormValue("page_index"))
	in.PageSize = cast.ToInt32(c.FormValue("page_size"))

	//// rpc请求
	client := ac.GetActivityCenterClient()

	gRes, err := client.GC.QueryGroupChat(client.Ctx, &in)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = 200
	out.Data = gRes.Data
	if gRes.Data == nil {
		out.Data = []*ac.GroupChat{}
	} else {
		out.Data = gRes.Data
	}
	out.TotalCount = gRes.TotalCount

	return c.JSON(http.StatusOK, out)
}

// EditGroupChat
// @summary 新增\修改街道群
// @tags 全国社群
// @Accept json
// @Produce plain
// @Param model body ac.EditGroupChatRequest true " "
// @success 200 {object} models.BaseResponseV2
// @failure 400 {object} models.BaseResponseV2
// @router /boss/groupChat/edit [post]
func EditGroupChat(c echo.Context) error {
	out := new(models.BaseResponseV2)
	model := new(ac.EditGroupChatRequest)
	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	//// rpc请求
	client := ac.GetActivityCenterClient()
	defer client.Close()

	_, err := client.GC.EditGroupChat(client.Ctx, model)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// DeleteGroupChat
// @summary 删除街道群
// @tags 全国社群
// @Accept json
// @Produce plain
// @Param model body ac.DeleteGroupChatRequest true " "
// @success 200 {object} models.BaseResponseV2
// @failure 400 {object} models.BaseResponseV2
// @router /boss/groupChat/delete [post]
func DeleteGroupChat(c echo.Context) error {
	out := new(models.BaseResponseV2)
	model := new(ac.DeleteGroupChatRequest)
	if err := c.Bind(model); err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.JSON(400, out)
	}

	//// rpc请求
	client := ac.GetActivityCenterClient()
	defer client.Close()

	_, err := client.GC.DeleteGroupChat(client.Ctx, model)
	if err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// @summary 生成加群链接
// @tags 全国社群
// @Accept json
// @Produce plain
// @Param model body ac.AddJoinWayReq true " "
// @success 200 {object} ac.BaseResponseNew
// @failure 400 {object} ac.BaseResponseNew
// @router /boss/groupChat/add-join-way [post]
func AddJoinWay(c echo.Context) error {
	out := new(ac.BaseResponseNew)
	model := new(ac.AddJoinWayReq)
	if err := c.Bind(model); err != nil {
		out.Msg = err.Error()
		return c.JSON(400, out)
	}

	if model.Type <= 0 || model.ChatId == "" || model.ChatName == "" || model.ProvinceId == 0 || model.ProvinceName == "" ||
		model.CityId == 0 || model.CityName == "" || model.AreaId == 0 || model.AreaName == "" {
		out.Msg = "参数有误"
		return c.JSON(400, out)
	}

	//// rpc请求
	client := ac.GetActivityCenterClient()
	defer client.Close()

	_, err := client.GC.AddJoinWay(client.Ctx, model)
	if err != nil {
		out.Msg = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, out)
}

// @summary 微信群列表
// @tags 全国社群
// @Accept json
// @Produce plain
// @Param model body ac.ChatListReq true " "
// @success 200 {object} ac.ChatListRes
// @failure 400 {object} ac.ChatListRes
// @router /boss/groupChat/chat-list [POST]
func ChatList(c echo.Context) error {
	out := new(ac.ChatListRes)
	model := new(ac.ChatListReq)
	if err := c.Bind(model); err != nil {
		return c.JSON(400, out)
	}

	if model.PageSize == 0 || model.PageIndex == 0 {
		return c.JSON(http.StatusBadRequest, "参数错误")
	}

	//// rpc请求
	client := ac.GetActivityCenterClient()
	defer client.Close()

	out, err := client.GC.ChatList(client.Ctx, model)
	if err != nil {
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, out)
}

// @summary 刷新群列表
// @tags 全国社群
// @Accept json
// @Produce plain
// @success 200 {object} ac.BaseResponseNew
// @failure 400 {object} ac.BaseResponseNew
// @router /boss/groupChat/wx-chat-list [get]
func WxChatList(c echo.Context) error {
	out := new(ac.BaseResponseNew)
	req := new(ac.ChatListReq)

	//// rpc请求
	client := ac.GetActivityCenterClient()
	defer client.Close()

	_, err := client.GC.WxChatList(client.Ctx, req)
	if err != nil {
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, out)
}

// @Summary 设置门店微信
// @Tags 全国社群
// @Accept json
// @Produce json
// @Param model body ac.SetHospitalWechatRequest true " "
// @Success 200 {object} ac.BaseResponse
// @Failure 400 {object} ac.BaseResponse
// @Router /boss/groupChat/set-hospital-wechat [post]
func SetHospitalWechat(c echo.Context) error {
	resp := &ac.BaseResponse{Code: 400}
	p := &ac.SetHospitalWechatRequest{}
	c.Bind(p)
	if p.HospitalCode == "" {
		resp.Message = "医院code不能为空"
		return c.JSON(400, resp)
	}
	p.WechatQrcode = strings.TrimSpace(p.WechatQrcode)

	client := ac.GetActivityCenterClient()
	defer client.Close()

	resp, err := client.GC.SetHospitalWechat(client.Ctx, p)
	if err != nil || resp.Code != 200 {
		return c.JSON(400, resp)
	}

	resp.Code = 200
	return c.JSON(200, resp)
}

type LinkHospitalParams struct {
	Id           int64  `json:"id"`
	HospitalCode string `json:"hospital_code"` //多个','分割
}

// @Summary 关联、取消关联微信群和门店
// @Tags 全国社群
// @Accept json
// @Produce json
// @Param model body LinkHospitalParams true " "
// @Success 200 {object} ac.BaseResponse
// @Failure 400 {object} ac.BaseResponse
// @Router /boss/groupChat/link-hospital [post]
func LinkHospital(c echo.Context) error {
	resp := &ac.BaseResponse{Code: 400}
	p := &LinkHospitalParams{}
	c.Bind(p)
	if p.Id <= 0 {
		resp.Message = "参数错误"
		return c.JSON(400, resp)
	}
	codes := make([]string, 0)
	if p.HospitalCode != "" {
		codes = strings.Split(p.HospitalCode, ",")
	}

	client := ac.GetActivityCenterClient()
	defer client.Close()

	var err error
	resp, err = client.GC.LinkHospital(client.Ctx, &ac.LinkHospitalRequest{
		GroupChatId:   p.Id,
		HospitalCodes: codes,
	})
	if err != nil || resp.Code != 200 {
		return c.JSON(400, resp)
	}

	resp.Code = 200
	return c.JSON(200, resp)
}
