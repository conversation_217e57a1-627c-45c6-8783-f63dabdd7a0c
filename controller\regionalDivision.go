package controller

import (
	"_/models"
	"_/proto/dac"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"net/http"
)

// QueryRegionalDivision
// @summary 查询区域
// @tags 城市区域划分
// @produce json
// @param parent_id body integer false "父级id"
// @success 200 {object} models.QueryRegionalDivisionResponse
// @failure 400 {object} models.QueryRegionalDivisionResponse
// @router /boss/regionalDivision/query [GET]
func QueryRegionalDivision(c echo.Context) error {
	out := new(models.QueryRegionalDivisionResponse)
	var in dac.QueryRegionalDivisionRequest

	// 参数绑定
	in.ParentId = cast.ToInt64(c.FormValue("parent_id"))

	// rpc请求
	client := dac.GetDataCenterClient()

	res, err := client.RPC.QueryRegionalDivision(client.Ctx, &in)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		return c.<PERSON><PERSON><PERSON>(http.StatusBadRequest, out)
	}
	out.Code = 200
	if res.Data == nil {
		out.Data = []*dac.RegionalDivision{}
	} else {
		out.Data = res.Data
	}

	return c.JSON(http.StatusOK, out)
}
