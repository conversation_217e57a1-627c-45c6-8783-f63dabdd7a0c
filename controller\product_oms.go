package controller

import (
	"_/proto/pc"
	"_/utils"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

// @Summary v6.5.0 同步oms商品到阿闻
// @Tags 商品库
// @Accept plain
// @Produce json
// @Param params body pc.ProductRequest true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /boss/product/add-product/oms [post]
func AddProductFromOms(c echo.Context) error {

	glog.Info("同步oms商品到阿闻 run...")

	baseResponse := pc.BaseResponse{
		Code: 400,
	}

	// 序列化参数
	params := new(pc.AddTaskProductFromOmsVo)
	if err := c.Bind(params); err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(400, baseResponse)
	}

	glog.Info("同步oms商品到阿闻参数： ", utils.JsonToString(params))
	//处理参数信息，转成pc.ProductRequest的方式
	client := GetDcProductClient(c)
	defer client.Close()
	resp, err := client.RPC.AddTaskProductFromOms(client.Ctx, params)
	glog.Info("同步oms商品到阿闻返回：", kit.JsonEncode(resp), err)
	if err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(400, baseResponse)
	}
	if resp.Code != 200 {
		baseResponse.Message = resp.Message
		return c.JSON(400, baseResponse)
	}

	baseResponse.Code = 200
	baseResponse.Message = "successful"
	return c.JSON(200, baseResponse)

}
