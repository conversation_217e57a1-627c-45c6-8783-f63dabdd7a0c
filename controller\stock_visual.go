package controller

import (
	"_/models"
	"_/proto/sv"
	"_/utils"
	"context"
	"encoding/json"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"math"
	"net/http"
	"time"
)

// @Summary 区域仓库存列表
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param warehouse_id query string false "仓库ID,多个逗号分隔"
// @Param sku_id query int false "商品sku_id"
// @Param third_sku_id query string false "A8编码"
// @Param is_warning query int false "库存状态 0 全部  1 是  2 否"
// @Param stock_num query string false "库存数量, 前后逗号分隔"
// @Param goods_num query string false "补货数量, 前后逗号分隔"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} sv.RegionWarehouseStockResponse
// @Failure 400 {object} sv.RegionWarehouseStockResponse
// @Router /boss/stock-visual/region/list [get]
func RegionWarehouseStockList(c echo.Context) error {
	warehouseId := c.QueryParam("warehouse_id")
	skuId := cast.ToInt32(c.QueryParam("sku_id"))
	thirdSkuId := c.QueryParam("third_sku_id")
	isWarning := cast.ToInt32(c.QueryParam("is_warning"))
	stockNum := c.QueryParam("stock_num")
	goodsNum := c.QueryParam("goods_num")
	pageIndex := cast.ToInt32(c.QueryParam("page_index"))
	pageSize := cast.ToInt32(c.QueryParam("page_size"))

	res := new(sv.RegionWarehouseStockResponse)
	params, err := stockVisualParamsCheck(warehouseId, thirdSkuId, stockNum, goodsNum, "", skuId, isWarning)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	params.PageSize = pageSize
	params.PageIndex = pageIndex

	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err = client.RPC.RegionWarehouseStockList(context.Background(), params)
	if err != nil {
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if len(res.Msg) > 0 {
		return c.JSON(400, res)
	}

	//data := &sv.RegionWarehouseStockData{
	//	SkuId:          1020749001,
	//	ThirdSkuId:     "A8C00241ALFJ",
	//	WarehouseId:    1007895,
	//	WarehouseName:  "测试区域仓",
	//	StockWarning:   1,
	//	BookInventory:  10,
	//	AvailableStock: 10,
	//	SafeStockLevel: 10,
	//	SuggestedStock: 10,
	//}
	//
	//res.Code = 200
	//res.Data = append(res.Data, data)
	//res.Total = 1
	return c.JSON(200, res)
}

// @Summary 前置仓库存列表
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param city query int false "所属城市"
// @Param warehouse_id query string false "仓库ID, 逗号分隔"
// @Param sku_id query int false "商品sku_id"
// @Param third_sku_id query string false "A8编码"
// @Param is_warning query int false "库存状态 0 全部  1 是  2 否"
// @Param stock_num query string false "库存数量, 前后逗号分隔"
// @Param goods_num query string false "补货数量, 前后逗号分隔"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Param book_inventory_order query int false "账面库存排序 1升序 2降序"
// @Success 200 {object} sv.PreposeWarehouseStockResponse
// @Failure 400 {object} sv.PreposeWarehouseStockResponse
// @Router /boss/stock-visual/prepose/list [get]
func PreposeWarehouseStockList(c echo.Context) error {
	city := c.QueryParam("city")
	warehouseId := c.QueryParam("warehouse_id")
	skuId := cast.ToInt32(c.QueryParam("sku_id"))
	thirdSkuId := c.QueryParam("third_sku_id")
	isWarning := cast.ToInt32(c.QueryParam("is_warning"))
	stockNum := c.QueryParam("stock_num")
	goodsNum := c.QueryParam("goods_num")
	pageIndex := cast.ToInt32(c.QueryParam("page_index"))
	pageSize := cast.ToInt32(c.QueryParam("page_size"))
	bookInventoryOrder := cast.ToInt32(c.QueryParam("book_inventory_order"))

	res := new(sv.PreposeWarehouseStockResponse)
	params, err := stockVisualParamsCheck(warehouseId, thirdSkuId, stockNum, goodsNum, city, skuId, isWarning)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	params.PageSize = pageSize
	params.PageIndex = pageIndex
	params.BookInventoryOrder = bookInventoryOrder

	client := sv.GetStockVisualClient(c)
	defer client.Close()
	client.Ctx = AppendToOutgoingContext(context.Background(), c)
	res, err = client.RPC.PreposeWarehouseStockList(client.Ctx, params)
	if err != nil {
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if len(res.Msg) > 0 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 区域仓库存数据导出
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param city query int false "所属城市"
// @Param warehouse_id query string false "仓库ID, 逗号分隔"
// @Param sku_id query int false "商品sku_id"
// @Param third_sku_id query string false "A8编码"
// @Param is_warning query int false "库存状态 0 全部  1 是  2 否"
// @Param stock_num query string false "库存数量, 前后逗号分隔"
// @Param goods_num query string false "补货数量, 前后逗号分隔"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} sv.BaseResponse
// @Failure 400 {object} sv.BaseResponse
// @Router /boss/stock-visual/region/export [get]
func RegionWarehouseStockExport(c echo.Context) error {
	res := new(sv.BaseResponse)
	warehouseId := c.QueryParam("warehouse_id")
	skuId := cast.ToInt32(c.QueryParam("sku_id"))
	thirdSkuId := c.QueryParam("third_sku_id")
	isWarning := cast.ToInt32(c.QueryParam("is_warning"))
	stockNum := c.QueryParam("stock_num")
	goodsNum := c.QueryParam("goods_num")

	params, err := stockVisualParamsCheck(warehouseId, thirdSkuId, stockNum, goodsNum, "", skuId, isWarning)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	userJson, _ := json.Marshal(userInfo)
	requestJson, _ := json.Marshal(params)

	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err = client.RPC.CreateTask(context.Background(), &sv.CreateTaskRequest{
		TaskContent:      1,
		OperationFileUrl: string(requestJson),
		RequestHeader:    string(userJson),
		CreateId:         userInfo.UserNo,
	})
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if len(res.Msg) > 0 {
		res.Code = 400
		return c.JSON(400, res)
	}

	res.Msg = "导出中..."
	return c.JSON(200, res)
}

// @Summary 前置仓库存数据导出
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param city query int false "所属城市"
// @Param warehouse_id query string false "仓库ID, 逗号分隔"
// @Param sku_id query int false "商品sku_id"
// @Param third_sku_id query string false "A8编码"
// @Param is_warning query int false "库存状态 0 全部  1 是  2 否"
// @Param stock_num query string false "库存数量, 前后逗号分隔"
// @Param goods_num query string false "补货数量, 前后逗号分隔"
// @Success 200 {object} sv.BaseResponse
// @Failure 400 {object} sv.BaseResponse
// @Router /boss/stock-visual/prepose/export [get]
func PreposeWarehouseStockExport(c echo.Context) error {
	res := new(sv.BaseResponse)
	city := c.QueryParam("city")
	warehouseId := c.QueryParam("warehouse_id")
	skuId := cast.ToInt32(c.QueryParam("sku_id"))
	thirdSkuId := c.QueryParam("third_sku_id")
	isWarning := cast.ToInt32(c.QueryParam("is_warning"))
	stockNum := c.QueryParam("stock_num")
	goodsNum := c.QueryParam("goods_num")

	params, err := stockVisualParamsCheck(warehouseId, thirdSkuId, stockNum, goodsNum, city, skuId, isWarning)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	userJson, _ := json.Marshal(userInfo)
	requestJson, _ := json.Marshal(params)

	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err = client.RPC.CreateTask(context.Background(), &sv.CreateTaskRequest{
		TaskContent:      2,
		OperationFileUrl: string(requestJson),
		RequestHeader:    string(userJson),
		CreateId:         userInfo.UserNo,
	})
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if len(res.Msg) > 0 {
		return c.JSON(400, res)
	}
	res.Msg = "导出中..."
	return c.JSON(200, res)
}

// @Summary 异步任务查询
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param task_content query int false "任务: 1 导出区域仓库存数据，2 导出前置仓库存数据，3 导出效期数据，4 导入效期数据，101处理导出与导入效期记录"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} sv.TaskListResponse
// @Failure 400 {object} sv.TaskListResponse
// @Router /boss/stock-visual/task/list [get]
func GetStockVisualTaskList(c echo.Context) error {
	taskContent := cast.ToInt32(c.QueryParam("task_content"))
	pageIndex := cast.ToInt32(c.QueryParam("page_index"))
	pageSize := cast.ToInt32(c.QueryParam("page_size"))
	//
	res := new(sv.TaskListResponse)
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	userNo := userInfo.UserNo
	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err = client.RPC.GetTaskList(context.Background(), &sv.TaskListRequest{
		TaskContent: taskContent,
		PageIndex:   pageIndex,
		PageSize:    pageSize,
		UserNo:      userNo,
	})

	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if res.Code == 400 {
		return c.JSON(400, res)
	}

	return c.JSON(200, res)
}

// @Summary 仓库列表
// @Tags 库存可视化
// @Param category query string false "仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓, 5-前置虚拟仓)"
// @Param pageindex query int true "分页索引"
// @Param pagesize query int true "分页大小"
// @Param keyword query string false "搜索关键词"
// @Param city query string false "城市"
// @Param prepose_category query int false "是否查询前置仓和前置虚拟仓, 默认 0 否，1 是"
// @Success 200 {object} sv.WarehouseListResponse
// @Failure 400 {object} sv.WarehouseListResponse
// @Router /boss/stock-visual/warehouse/list [get]
func GetStockVisualWarehouseList(c echo.Context) error {
	category := c.FormValue("category")
	pageIndex := cast.ToInt32(c.FormValue("pageindex"))
	pageSize := cast.ToInt32(c.FormValue("pagesize"))
	keyword := c.FormValue("keyword")
	city := c.FormValue("city")
	prepose := cast.ToInt32(c.FormValue("prepose_category"))

	client := sv.GetStockVisualClient(c)
	defer client.Close()
	client.Ctx = AppendToOutgoingContext(context.Background(), c)
	grpcRes, err := client.RPC.GetWarehouseList(client.Ctx, &sv.WarehouseListRequest{
		Category:        category,
		Pageindex:       int32(pageIndex),
		Pagesize:        int32(pageSize),
		KeyWord:         keyword,
		City:            city,
		PreposeCategory: prepose,
	})
	if err != nil {
		grpcRes.Code = 400
		grpcRes.Error = err.Error()
		return c.JSON(400, grpcRes)
	}

	return c.JSON(200, grpcRes)
}

// =================== 权限设置 =============

// @Summary 查询权限设置信息列表
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} sv.AuthInfoListResponse
// @Failure 400 {object} sv.AuthInfoListResponse
// @Router /boss/stock-visual/auth/list [get]
func AuthUserInfoList(c echo.Context) error {
	pageIndex := cast.ToInt32(c.QueryParam("page_index"))
	pageSize := cast.ToInt32(c.QueryParam("page_size"))

	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err := client.RPC.AuthUserInfoList(client.Ctx, &sv.AuthInfoListRequest{
		PageSize:  pageSize,
		PageIndex: pageIndex,
	})
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if res.Code == 400 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 根据id查询权限设置信息详情
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param user_no query string true "用户id"
// @Success 200 {object} sv.AuthInfoDetailResponse
// @Failure 400 {object} sv.AuthInfoDetailResponse
// @Router /boss/stock-visual/auth/detail [get]
func AuthUserInfoDetail(c echo.Context) error {
	userNo := c.QueryParam("user_no")
	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err := client.RPC.GetAuthInfoDetail(client.Ctx, &sv.AuthInfoDetailRequest{
		UserNo: userNo,
	})
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if res.Code == 400 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 新增权限设置
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param AddAuthInfoRequest body sv.AddAuthInfoRequest true " "
// @Success 200 {object} sv.BaseResponse
// @Failure 400 {object} sv.BaseResponse
// @Router /boss/stock-visual/auth/add [post]
func AddAuthUserInfo(c echo.Context) error {
	res := new(sv.BaseResponse)
	params := new(sv.AddAuthInfoRequest)
	if err := c.Bind(params); err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err := client.RPC.AddAuthInfo(client.Ctx, params)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if res.Code == 400 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary 编辑权限设置
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param UpdateAuthInfoRequest body sv.UpdateAuthInfoRequest true " "
// @Success 200 {object} sv.BaseResponse
// @Failure 400 {object} sv.BaseResponse
// @Router /boss/stock-visual/auth/update [post]
func UpdateAuthUserInfo(c echo.Context) error {
	res := new(sv.BaseResponse)
	params := new(sv.UpdateAuthInfoRequest)
	if err := c.Bind(params); err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err := client.RPC.UpdateAuthInfo(client.Ctx, params)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if res.Code == 400 {
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

//==========================================================================================

// EffectiveList
// @Summary 效期列表查询
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param warehouse_id query string false "仓库id多个仓库id通过,逗号拼接"
// @Param sku_id query string  false "商品skuId"
// @Param third_sku_id query string false "第三方货号"
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Param status query string  false "效期状态 1优先出货 2重点关注 3二级预警 4一级预警 5即将过期 6已过期 "
// @Param start_day query int false "效期天数开始"
// @Param end_day query string false "效期天数结束"
// @Success 200 {object}  models.EffectiveManagementResponsePageSize
// @Failure 400 {object}  models.EffectiveManagementResponsePageSize
// @Router /boss/stock-visual/effective/search [GET]
func EffectiveList(c echo.Context) error {

	responses := make([]*sv.EffectiveManagementResponse, 0)
	data := models.EffectiveManagementResponsePageSize{
		responses,
		0,
		"",
	}
	vo := sv.EffectiveManagementResVo{
		WarehouseId: c.QueryParam("warehouse_id"),
		SkuId:       c.QueryParam("sku_id"),
		ThirdSkuId:  c.QueryParam("third_sku_id"),
		Status:      cast.ToInt32(c.QueryParam("status")),
		StartDay:    cast.ToInt32(c.QueryParam("start_day")),
		EndDay:      cast.ToInt32(c.QueryParam("end_day")),
		PageIndex:   cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:    cast.ToInt32(c.QueryParam("pageSize")),
	}
	glog.Info("vo : ", vo)
	if vo.StartDay < 0 || vo.EndDay < 0 {
		data.Msg = "效期天数不能为负数"
		return c.JSON(http.StatusBadRequest, data)
	}
	if vo.StartDay > vo.EndDay {
		data.Msg = "效期天数起始不能大于结束时间"
		return c.JSON(http.StatusBadRequest, data)
	}

	client := sv.GetStockVisualClient(c)
	defer client.Close()

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		data.Msg = err.Error()
		return c.JSON(400, data)
	}

	vo.Operator = userInfo.UserNo
	responseData, err := client.RPC.SearchEffectiveList(client.Ctx, &vo)
	if err != nil {
		data.Msg = "获取效期列表异常"
		glog.Error(data.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, data)
	}

	if len(responseData.Data) > 0 {
		data.Data = responseData.Data
		data.Total = int(responseData.Total)
	}

	return c.JSON(http.StatusOK, data)

}

// @Summary 效期导入
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param url query string true "七牛url"
// @Success 200 {object}  sv.BaseResponse
// @Failure 400 {object}  sv.BaseResponse
// @Router /boss/stock-visual/effective/import [post]
func EffectiveImport(c echo.Context) error {
	res := new(sv.BaseResponse)
	res.Code = 400
	url := c.FormValue("url")
	if len(url) == 0 {
		res.Msg = "请上传文件"
		return c.JSON(400, res)
	}

	// 校验导入文件的格式是否正确
	msg := effectiveImportCheck(url)
	if len(msg) > 0 {
		res.Msg = msg
		return c.JSON(400, res)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	userJson, _ := json.Marshal(userInfo)

	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err = client.RPC.CreateTask(context.Background(), &sv.CreateTaskRequest{
		TaskContent:      4,
		OperationFileUrl: url,
		RequestHeader:    string(userJson),
		CreateId:         userInfo.UserNo,
	})
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	if len(res.Msg) > 0 {
		res.Code = 400
		return c.JSON(400, res)
	}
	return c.JSON(http.StatusOK, res)
}

// 获取渠道商品 通过管家商品查询is_use字段  已经仓库A8货号
// EffectiveList
// @Summary 获取渠道商品
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param sku_id query string  false "商品skuId"
// @Param third_sku_id query string  false "第三方货号"
// @Param type query int false "1:安全规则预警 2:高库存预警 3:最小起订量 4:补货系数"
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Success 200 {object} models.BaseResponseNew
// @Failure 400 {object} models.BaseResponseNew
// @Router /boss/stock-visual/product/search [GET]
func GetChannelProduct(c echo.Context) error {

	response := models.ProductResponse{}
	skuId := c.QueryParam("sku_id")
	thirdSkuId := c.QueryParam("third_sku_id")
	tagType := cast.ToInt32(c.QueryParam("type"))
	warehouseId := cast.ToInt32(c.QueryParam("warehouse_id"))
	pageIndex := cast.ToInt32(c.QueryParam("pageIndex"))
	pageSize := cast.ToInt32(c.QueryParam("pageSize"))
	if tagType == 4 && warehouseId <= 0 {
		response.Msg = "warehouse_id参数必传"
		return c.JSON(http.StatusBadRequest, response)
	}

	glog.Info("skuId: ", skuId, " third_sku_id: ", thirdSkuId)

	vo := sv.GetChannelProductVo{
		SkuId:       skuId,
		ThirdSkuId:  thirdSkuId,
		Type:        tagType,
		PageSize:    pageSize,
		PageIndex:   pageIndex,
		WarehouseId: warehouseId,
	}
	client := sv.GetStockVisualClient(c)
	data, err := client.RPC.SearchChannelProduct(client.Ctx, &vo)
	if err != nil {
		glog.Error("client.RPC.SearchChannelProduct error:", err.Error())
		response.Msg = "获取渠道商品异常"
		return c.JSON(http.StatusBadRequest, response)
	}

	response.Total = int(data.Total)
	response.Data = data.Data

	return c.JSON(http.StatusOK, response)
}

// 保存仓库规则配置
// @Summary 保存仓库规则配置
// @Tags 库存可视化
// @Param WarehouseRuleConfigurationVo body sv.WarehouseRuleConfigurationVo true " "
// @Success 200 {object} sv.WarehouseConfigurationResponse
// @Router /boss/stock-visual/warehouse-rule-configuration/add [post]
func WarehouseRuleConfigurationAdd(c echo.Context) error {
	res := sv.WarehouseConfigurationResponse{}

	vo := sv.WarehouseRuleConfigurationVo{}

	if err := c.Bind(&vo); err != nil {
		res.Msg = "仓库规则配置,参数异常"
		glog.Error("WarehouseRuleConfiguration: ", res.Msg)
		return c.JSON(http.StatusBadRequest, res)
	}
	glog.Info("WarehouseRuleConfigurationVo: ", kit.JsonEncode(vo))
	if vo.WarehouseId <= 0 {
		res.Msg = "请先选择仓库"
		return c.JSON(http.StatusBadRequest, res)
	}
	if vo.Type < 1 || vo.Type > 2 {
		res.Msg = "请选择类型 1:安全规则预警 2:高库存预警"
		return c.JSON(http.StatusBadRequest, res)
	}

	if vo.Type == 1 {
		if vo.SafeDays <= 0 || vo.SafeStock <= 0 {
			res.Msg = "库存||周转天数不能小于1"
			return c.JSON(http.StatusBadRequest, res)
		}
	}

	if vo.Type == 2 {
		if vo.DeadStock <= 0 || vo.DeadStockDays <= 0 {
			res.Msg = "库存||周转天数不能小于1"
			return c.JSON(http.StatusBadRequest, res)
		}
	}

	if vo.Status < 1 || vo.Status > 2 {
		res.Msg = "仓库状态异常启用1禁用2"
		return c.JSON(http.StatusBadRequest, res)
	}

	client := sv.GetStockVisualClient(c)
	defer client.Close()

	// vo.Id > 0 // 修改  =0 新增
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	// 查询是否有仓库的权限
	isAuth := sv.AuthInfoDetailRequest{
		UserNo: userInfo.UserNo,
	}
	detailResponse, err := client.RPC.GetAuthInfoDetail(client.Ctx, &isAuth)
	if err != nil {
		res.Msg = "查询用户仓库权限系统异常"
		glog.Error(res.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, res)
	}
	if detailResponse.Code != 200 {
		res.Msg = "查询用户仓库权限失败"
		glog.Error(res.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, res)
	}

	isTrue := utils.HasPermissionWarehouse(vo.WarehouseId, detailResponse)
	if !isTrue {
		res.Msg = "你没有该仓库的权限，请先设置权限"
		return c.JSON(http.StatusBadRequest, res)
	}

	vo.Operator = userInfo.UserNo

	data, err := client.RPC.WarehouseRuleConfigurationAdd(client.Ctx, &vo)
	glog.Info("data: ", data)
	if err != nil {
		glog.Error("client.RPC.WarehouseRuleConfigurationAdd：Msg", " error: ", err.Error())
		res.Msg = "更新保存仓库信息异常"
		return c.JSON(http.StatusBadRequest, res)
	}
	if data.Code != 200 {
		res.Msg = data.Msg
		return c.JSON(http.StatusBadRequest, res)
	}

	return c.JSON(http.StatusOK, res)

}

// 仓库规则查询
// EffectiveList
// @Summary 仓库规则查询
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param warehouse_id query string  false "仓库id"
// @Param type query int  false "类型(1:安全规则预警 2:高库存预警 3:最小起订量 4补货系数)"
// @Success 200 {object} models.WarehouseConfigurationResponse
// @Failure 400 {object} models.WarehouseConfigurationResponse
// @Router /boss/stock-visual/warehouse-rule-configuration/search [GET]
func WarehouseRuleConfiguration(c echo.Context) error {
	res := models.WarehouseConfigurationResponse{
		Data: &sv.WarehouseRuleConfigurationResponse{},
	}

	warehouseId := cast.ToInt32(c.QueryParam("warehouse_id"))
	warehouseType := cast.ToInt32(c.QueryParam("type"))
	glog.Info("warehouseId: ", warehouseId, " warehouseType", warehouseType)

	if warehouseId <= 0 {
		res.Msg = "请先选择仓库"
		return c.JSON(http.StatusBadRequest, res)
	}

	client := sv.GetStockVisualClient(c)
	defer client.Close()

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Msg = "权限异常"
		glog.Error(res.Msg, err.Error())
		return c.JSON(400, res)
	}

	// 查询是否有仓库的权限
	isAuth := sv.AuthInfoDetailRequest{
		UserNo: userInfo.UserNo,
	}
	detailResponse, err := client.RPC.GetAuthInfoDetail(client.Ctx, &isAuth)
	if err != nil {
		res.Msg = "查询用户仓库权限系统异常"
		glog.Error(res.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, res)
	}
	if detailResponse.Code != 200 {
		res.Msg = "查询用户仓库权限失败"
		glog.Error(res.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, res)
	}

	isTrue := utils.HasPermissionWarehouse(warehouseId, detailResponse)
	if !isTrue {
		res.Msg = "你没有该仓库的权限，请先设置权限"
		return c.JSON(http.StatusBadRequest, res)
	}

	in := sv.GetWarehouseIdVo{
		WarehouseId: warehouseId,
		Type:        warehouseType,
	}
	response, err := client.RPC.GetWarehouseRuleConfiguration(client.Ctx, &in)
	if err != nil {
		glog.Error("client.RPC.GetWarehouseRuleConfiguration", err.Error())
		res.Msg = "查询仓库信息异常"
		return c.JSON(http.StatusBadRequest, res)
	}

	res.Data = response.Data
	return c.JSON(http.StatusOK, res)

}

// 商品规则查询
// @Summary 商品规则查询
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param warehouse_id query string  false "仓库id"
// @Param sku_id query string  false "sku_id"
// @Param type query string  true "类型(1:安全规则预警 2:高库存预警 3:最小起订量 4:补货系数)"
// @Param third_sku_id query string  false "第三方id"
// @Param status query string  false "规则状态1启用 2禁用"
// @Param pageIndex query string  true "分页起始位置"
// @Param pageSize query string  true "分页大小"
// @Success 200 {object} sv.ProductConfigurationResponse
// @Failure 400 {object} sv.ProductConfigurationResponse
// @Router /boss/stock-visual/product-rule-configuration/search [GET]
func ProductRuleConfiguration(c echo.Context) error {
	res := sv.ProductConfigurationResponse{}

	vo := sv.ProductRuleConfigurationVo{
		WarehouseId: cast.ToInt32(c.QueryParam("warehouse_id")),
		SkuId:       c.QueryParam("sku_id"),
		ThirdSkuId:  c.QueryParam("third_sku_id"),
		Status:      cast.ToInt32(c.QueryParam("status")),
		Type:        cast.ToInt32(c.QueryParam("type")),
		PageIndex:   cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:    cast.ToInt32(c.QueryParam("pageSize")),
	}

	glog.Info("ProductRuleConfigurationVo: ", kit.JsonEncode(vo))
	if vo.Type <= 0 || vo.Type > 4 {
		res.Msg = "类型异常(1:安全规则预警 2:高库存预警 3:最小起订量 4:补货系数)"
		return c.JSON(http.StatusBadRequest, res)
	}
	if vo.WarehouseId <= 0 {
		res.Msg = "请选择仓库id"
		return c.JSON(http.StatusBadRequest, res)
	}

	client := sv.GetStockVisualClient(c)
	defer client.Close()

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Msg = "权限异常"
		glog.Error(res.Msg, err.Error())
		return c.JSON(400, res)
	}

	// 查询是否有仓库的权限
	isAuth := sv.AuthInfoDetailRequest{
		UserNo: userInfo.UserNo,
	}
	detailResponse, err := client.RPC.GetAuthInfoDetail(client.Ctx, &isAuth)
	if err != nil {
		res.Msg = "查询用户仓库权限系统异常"
		glog.Error(res.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, res)
	}
	if detailResponse.Code != 200 {
		res.Msg = "查询用户仓库权限失败"
		glog.Error(res.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, res)
	}

	isTrue := utils.HasPermissionWarehouse(vo.WarehouseId, detailResponse)
	if !isTrue {
		res.Msg = "你没有该仓库的权限，请先设置权限"
		return c.JSON(http.StatusBadRequest, res)
	}

	response, err := client.RPC.SearchProductRules(client.Ctx, &vo)
	if err != nil {
		glog.Error("client.RPC.SearchProductRules(client.Ctx, &vo)", err.Error())
		res.Msg = "查询仓库异常"
		return c.JSON(http.StatusBadRequest, res)
	}

	res.Data = response.Data
	res.Total = response.Total
	return c.JSON(http.StatusOK, res)
}

// 增加商品规则设置
// @Summary 增加商品规则设置
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param ProductResVo body sv.ProductResVo true " "
// @Success 200 {object} sv.ProductConfigurationResponse{}
// @Failure 400 {object} sv.ProductConfigurationResponse{}
// @Router /boss/stock-visual/product-rule-configuration/add [post]
func ProductRuleConfigurationAdd(c echo.Context) error {
	res := sv.ProductConfigurationResponse{}

	vo := sv.ProductResVo{}

	if err := c.Bind(&vo); err != nil {
		res.Msg = "商品规则配置,参数异常"
		glog.Error("ProductRuleConfiguration: ", res.Msg)
		return c.JSON(http.StatusBadRequest, res)
	}
	glog.Info("ProductRuleConfigurationVo: ", kit.JsonEncode(vo))

	if vo.WarehouseId <= 0 {
		res.Msg = "请先选择仓库"
		return c.JSON(http.StatusBadRequest, res)
	}
	if vo.Type <= 0 || vo.Type > 4 {
		res.Msg = "类型异常(1:安全规则预警 2:高库存预警 3:最小起订量 4:补货系数)"
		return c.JSON(http.StatusBadRequest, res)
	}

	for _, v := range vo.Data {
		if v.Status < 1 || v.Status > 2 {
			res.Msg = v.ThirdSkuId + ": 仓库状态异常启用1禁用2"
			return c.JSON(http.StatusBadRequest, res)
		}
		if vo.Type == 1 {
			if v.SafeStock <= 0 || v.SafeDays <= 0 {
				res.Msg = v.ThirdSkuId + "库存||周转天数不能小于1"
				return c.JSON(http.StatusBadRequest, res)
			}
		}
		if vo.Type == 2 {
			if v.DeadStockDays <= 0 || v.DeadStock <= 0 {
				res.Msg = v.ThirdSkuId + "库存||周转天数不能小于1"
				return c.JSON(http.StatusBadRequest, res)
			}
		}
		if vo.Type == 3 {
			if v.MinimumOrderQuantity <= 0 {
				res.Msg = v.ThirdSkuId + "最小起订量不能小于1"
				return c.JSON(http.StatusBadRequest, res)
			}
		}
		if vo.Type == 4 {
			if v.ReplenishmentRate < 0 || v.ReplenishmentRate > 2 {
				res.Msg = v.ThirdSkuId + "补货系数只能是0到2之间"
				return c.JSON(http.StatusBadRequest, res)
			}
		}
	}

	responses := make([]*sv.ProductRuleConfigurationResponse, 0)
	configurationResponses := append(responses, vo.Data...)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}

	vo.Operator = userInfo.UserNo
	client := sv.GetStockVisualClient(c)
	defer client.Close()

	// 查询是否有仓库的权限
	isAuth := sv.AuthInfoDetailRequest{
		UserNo: userInfo.UserNo,
	}
	detailResponse, err := client.RPC.GetAuthInfoDetail(client.Ctx, &isAuth)
	if err != nil {
		res.Msg = "查询用户仓库权限系统异常"
		glog.Error(res.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, res)
	}
	if detailResponse.Code != 200 {
		res.Msg = "查询用户仓库权限失败"
		glog.Error(res.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, res)
	}

	isTrue := utils.HasPermissionWarehouse(vo.WarehouseId, detailResponse)
	if !isTrue {
		res.Msg = "你没有该仓库的权限，请先设置权限"
		return c.JSON(http.StatusBadRequest, res)
	}

	//有id的就要判断进行更新，其余的进行新增
	response, err := client.RPC.SaveProductRule(client.Ctx, &vo)
	if err != nil {
		glog.Error("client.RPC.SaveProductRule error: ", err.Error())
		res.Msg = "保存数据异常"
		return c.JSON(http.StatusBadRequest, res)
	}

	res.Total = int32(len(configurationResponses))
	res.Data = response.Data
	return c.JSON(http.StatusOK, res)
}

// 删除商品信息
// @Summary 删除商品信息
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param DeleteProduct body models.DeleteProduct true " "
// @Success 200 {object}  models.BaseResponseNew{}
// @Failure 400 {object}  models.BaseResponseNew{}
// @Router /boss/stock-visual/product-rule-configuration/delete [post]
func ProductRuleConfigurationDelete(c echo.Context) error {

	res := models.BaseResponseNew{}

	vo := sv.DeleteProduct{}
	if err := c.Bind(&vo); err != nil {
		res.Msg = "删除仓库信息,参数异常"
		glog.Error("DeleteWarehouse: ", res.Msg)
		return c.JSON(http.StatusBadRequest, res)
	}

	glog.Info("DeleteWarehouse vo :", vo)
	client := sv.GetStockVisualClient(c)
	defer client.Close()

	_, err := client.RPC.DeleteProductRule(client.Ctx, &vo)
	if err != nil {
		glog.Error("client.RPC.DeleteProductRule", err.Error())
		res.Msg = "删除数据异常"
		return c.JSON(http.StatusBadRequest, res)
	}

	res.Msg = "successful"
	return c.JSON(http.StatusOK, res)

}

// 批量启用停用
// @Summary 批量启用停用
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param DeleteProduct body models.UseOrDisableProduct true " "
// @Success 200 {object}  models.BaseResponseNew{}
// @Failure 400 {object}  models.BaseResponseNew{}
// @Router /boss/stock-visual/product-rule-configuration/update [post]
func BatchUseOrDisable(c echo.Context) error {

	res := models.BaseResponseNew{}
	vo := models.UseOrDisableProduct{}

	if err := c.Bind(&vo); err != nil {
		res.Msg = "批量启用停用,参数异常"
		glog.Error("DeleteWarehouse: ", res.Msg)
		return c.JSON(http.StatusBadRequest, res)
	}

	if len(vo.Ids) <= 0 {
		res.Msg = "请选择需要启用禁用的id"
		return c.JSON(http.StatusBadRequest, res)
	}

	// 查询数据库批量启用禁用
	client := sv.GetStockVisualClient(c)
	defer client.Close()

	productVo := sv.BatchUpdateProductVo{
		Ids:    vo.Ids,
		Status: vo.Status,
	}
	data, err := client.RPC.BatchUpdateProduct(client.Ctx, &productVo)
	if err != nil {
		glog.Error("client.RPC.BatchUpdateProduct(client.Ctx, &productVo)", err.Error())
		res.Msg = "批量更子更新状态异常"
		return c.JSON(http.StatusBadRequest, res)
	}

	res.Data = data

	return c.JSON(http.StatusOK, res)
}

//模板导出下载
// @Summary 模板导出下载
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param type query string  true "类型(1:效期模板 2:安全库存预警规则 3:高库存预警规则 4最小起订量)"
// @Success 200 {object} models.BaseResponseNew
// @Failure 400 {object} models.BaseResponseNew
// @Router /boss/stock-visual/excel/template [GET]
func DownloadTemplateExcel(c echo.Context) error {

	res := models.BaseResponseNew{}

	typeData := cast.ToInt(c.QueryParam("type"))
	glog.Info("typeData: ", typeData)

	if typeData <= 0 || typeData > 5 {
		res.Msg = "数据导出类型参数异常"
		return c.JSON(http.StatusBadRequest, res)
	}
	var fileName string
	header := make([][]string, 0)
	if typeData == 1 { // 导出效期模板
		fileName = "效期模板"
		header = append(header, utils.ExpiryTemplate...)
	} else if typeData == 2 { // 安全库存预警规则
		fileName = "安全库存预警规则"
		header = append(header, utils.SafePrecautionTemplate...)
	} else if typeData == 3 { // 高库存预警规则
		fileName = "高库存预警规则"
		header = append(header, utils.HighStockTemplate...)
	} else if typeData == 4 { // 最小起订量
		fileName = "最小起订量"
		header = append(header, utils.MinimumOrderTemplate...)
	} else if typeData == 5 { // 补货系数
		fileName = "补货系数"
		header = append(header, utils.ReplenishmentRateTemplate...)
	}

	return utils.ExportExcel(c, "", fileName, header)
}

//读取excle数据
// @Summary 读取excle模板数据
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param type query string  true "form-data类型(1:效期模板(不使用) 2:安全库存预警规则 3:高库存预警规则 4最小起订量)"
// @Param warehouse_id query string  true "warehouse_id仓库id"
// @Param file query string  true "form-data文件"
// @Success 200 {object} models.BaseResponseNew
// @Failure 400 {object} models.BaseResponseNew
// @Router /boss/stock-visual/excel/upload [GET]
func UploadTemplateExcel(c echo.Context) error {

	res := models.BaseResponseNew{}

	file, err := c.FormFile("file")
	if err != nil {
		res.Msg = "读取不到文件信息"
		return c.JSON(http.StatusBadRequest, res)
	}

	typeData := cast.ToInt(c.FormValue("type"))
	warehouseId := cast.ToInt(c.FormValue("warehouse_id"))
	if typeData <= 0 || typeData > 5 {
		res.Msg = "数据导出类型参数异常"
		return c.JSON(http.StatusBadRequest, res)
	}
	if warehouseId <= 0 {
		res.Msg = "请先选择仓库id"
		return c.JSON(http.StatusBadRequest, res)
	}

	// 读取数据
	data, err := utils.ReadExcelData(file, "", 1000)
	if err != nil {
		res.Msg = err.Error()
		return c.JSON(http.StatusBadRequest, res)
	}

	resData := make(map[string]interface{}, 0)

	if typeData == 1 { // 读取效期模板数据
		//校验模板是否正确
		isEqual := utils.CheckExcelTemplate(data[0], utils.ExpiryTemplate[0])
		if !isEqual {
			res.Msg = "效期模板不正确"
			return c.JSON(http.StatusBadRequest, res)
		}

		effective, _, total, err := utils.ReadExcelToEffective(data, typeData, warehouseId)
		res.Total = int32(total)
		if err != nil {
			res.Msg = "效期数据装换异常"
			return c.JSON(http.StatusBadRequest, res)
		}
		res.Data = effective

	} else if typeData == 2 { // 读取安全库存预警规则数据

		isEqual := utils.CheckExcelTemplate(data[0], utils.SafePrecautionTemplate[0])
		if !isEqual {
			res.Msg = "安全库存预警模板不正确"
			return c.JSON(http.StatusBadRequest, res)
		}

		effective, errList, total, err := utils.ReadExcelToEffective(data, typeData, warehouseId)
		res.Total = int32(total)
		resData["msg"] = errList
		if err != nil {
			res.Msg = "安全库存预警数据装换异常"
			return c.JSON(http.StatusBadRequest, res)
		}
		res.Data = effective

	} else if typeData == 3 { // 读取高库存预警规则数据
		isEqual := utils.CheckExcelTemplate(data[0], utils.HighStockTemplate[0])
		if !isEqual {
			res.Msg = "高库存预警模板不正确"
			return c.JSON(http.StatusBadRequest, res)
		}

		effective, errList, total, err := utils.ReadExcelToEffective(data, typeData, warehouseId)
		res.Total = int32(total)
		if err != nil {
			res.Msg = "高库存预警数据装换异常"
			return c.JSON(http.StatusBadRequest, res)
		}
		res.Data = effective
		resData["msg"] = errList

	} else if typeData == 4 { // 读取最小起订量数据
		isEqual := utils.CheckExcelTemplate(data[0], utils.MinimumOrderTemplate[0])
		if !isEqual {
			res.Msg = "最小起订量模板不正确"
			return c.JSON(http.StatusBadRequest, res)
		}

		effective, errList, total, err := utils.ReadExcelToEffective(data, typeData, warehouseId)
		res.Total = int32(total)
		if err != nil {
			res.Msg = "最小起订量数据装换异常"
			return c.JSON(http.StatusBadRequest, res)
		}
		res.Data = effective
		resData["msg"] = errList
	} else if typeData == 5 { // 补货系数
		isEqual := utils.CheckExcelTemplate(data[0], utils.ReplenishmentRateTemplate[0])
		if !isEqual {
			res.Msg = "补货系数模板不正确"
			return c.JSON(http.StatusBadRequest, res)
		}

		effective, errList, total, err := utils.ReadExcelToEffective(data, typeData, warehouseId)
		res.Total = int32(total)
		if err != nil {
			res.Msg = "补货系数数据装换异常"
			return c.JSON(http.StatusBadRequest, res)
		}
		res.Data = effective
		resData["msg"] = errList
	}
	resData["data"] = res.Data
	resData["total"] = res.Total
	return c.JSON(http.StatusOK, resData)
}

// EffectiveList
// @Summary 效期列表导出任务
// @Tags 库存可视化
// @Accept plain
// @Produce json
// @Param warehouse_id query string false "仓库id多个仓库id通过,逗号拼接"
// @Param sku_id query string  false "商品skuId"
// @Param third_sku_id query string false "第三方货号"
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Param status query string  false "效期状态 1优先出货 2重点关注 3二级预警 4一级预警 5即将过期 6已过期 "
// @Param start_day query int false "效期天数开始"
// @Param end_day query string false "效期天数结束"
// @Success 200 {object}  models.EffectiveManagementResponsePageSize
// @Failure 400 {object}  models.EffectiveManagementResponsePageSize
// @Router /boss/stock-visual/effective/export [GET]
func EffictiveExportExcle(c echo.Context) error {

	responseNew := models.BaseResponseNew{}
	vo := sv.EffectiveManagementResVo{
		WarehouseId: c.QueryParam("warehouse_id"),
		SkuId:       c.QueryParam("sku_id"),
		ThirdSkuId:  c.QueryParam("third_sku_id"),
		Status:      cast.ToInt32(c.QueryParam("status")),
		StartDay:    cast.ToInt32(c.QueryParam("start_day")),
		EndDay:      cast.ToInt32(c.QueryParam("end_day")),
		PageIndex:   cast.ToInt32(c.QueryParam("pageIndex")),
		PageSize:    cast.ToInt32(c.QueryParam("pageSize")),
	}
	glog.Info("vo : ", kit.JsonEncode(vo))

	if vo.StartDay > vo.EndDay {
		responseNew.Msg = "效期天数起始不能大于结束时间"
		return c.JSON(http.StatusBadRequest, responseNew)
	}

	bytes, _ := json.Marshal(vo)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("效期导出GetPayloadDirectlyToInterface(c)", err)
		responseNew.Msg = ""
		return c.JSON(400, responseNew)
	}
	//
	userJson, _ := json.Marshal(userInfo)

	//创建导出任务
	request := sv.CreateTaskRequest{
		TaskContent:      3,
		CreateId:         userInfo.UserNo,
		OperationFileUrl: string(bytes),
		RequestHeader:    string(userJson),
	}
	client := sv.GetStockVisualClient(c)
	defer client.Close()

	_, err = client.RPC.CreateTask(client.Ctx, &request)
	if err != nil {
		responseNew.Msg = "效期导出任务异常"
		glog.Error(responseNew.Msg, err.Error())
		return c.JSON(200, responseNew)
	}

	return c.JSON(http.StatusOK, responseNew)
}

// @Summary 初始化折扣的全量数据
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param begin_date query int true "时间日期格式列如2012-12-09"
// @Success 200 {object}  models.BaseResponseNew{}
// @Failure 400 {object} models.BaseResponseNew
// @Router /boss/stock-visual/effective/InitDiscountTask [get]
func InitDiscountTask(c echo.Context) error {
	responseNew := models.BaseResponseNew{}
	begin_date := cast.ToString(c.QueryParam("begin_date"))

	_, err := time.ParseInLocation(kit.DATE_LAYOUT, begin_date, time.Local)
	if err != nil {
		responseNew.Msg = "日期格式不正对"
		return c.JSON(400, responseNew)
	}

	go SyncInitDiscountTask(c, begin_date)

	return c.JSON(200, responseNew)
}

func SyncInitDiscountTask(c echo.Context, begin_date string) {
	client := sv.GetStockVisualClient(c)
	defer client.Close()
	glog.Info("---------------")
	response, err := client.RPC.InitDiscountTask(client.Ctx, &sv.DiscountTaskRequest{
		BeginDate: begin_date,
	})
	glog.Info("rpc :返回", kit.JsonEncode(response), " err: ", err)
}

// @Summary 增量处理活动的折扣数据
// @Tags 库存可视化
// @Accept json
// @Produce json
// @Param promotion_id query int true "折扣的id"
// @Param promotionDate query int true "折扣的时间"
// @Success 200 {object} models.BaseResponseNew
// @Failure 400 {object} models.BaseResponseNew
// @Router /boss/stock-visual/effective/IncrementPromotionDiscountTask [get]
func IncrementPromotionDiscountTask(c echo.Context) error {
	responseNew := models.BaseResponseNew{}
	promotionId := cast.ToInt32(c.QueryParam("promotion_id"))
	promotionDate := c.QueryParam("update_date")

	if promotionId <= 0 {
		responseNew.Msg = "请输入折扣的id"
		return c.JSON(200, responseNew)
	}
	client := sv.GetStockVisualClient(c)
	defer client.Close()

	res, err := client.RPC.IncrementPromotionDiscountTask(client.Ctx, &sv.IncrementPromotionDiscountRes{
		PromotionId: promotionId,
		UpdateDate:  promotionDate,
	})
	if err != nil {
		res.Code = 400
		res.Msg = err.Error()
		return c.JSON(400, res)
	}
	return c.JSON(200, res)
}

// @Summary v6.3.3增加按钮权限配置
// @Tags v6.6.7.1 商品库
// @Produce json
// @Param is_permission formData int true "是否开启按钮权限"
// @Param del_pro_permission formData int true "是否有删除权限"
// @Param id query int formData "用户的id"
// @Success 200 {object} models.BaseResponseNew
// @Failure 400 {object} models.BaseResponseNew
// @Router /boss/stock-visual/auth/AddButtonPermissions [post]
func AddButtonPermissions(c echo.Context) error {
	responseNew := models.BaseResponseNew{
		Msg: "successful",
	}
	isPermission := cast.ToInt32(c.FormValue("is_permission"))
	delProPermission := cast.ToInt32(c.FormValue("del_pro_permission"))
	id := cast.ToInt32(c.FormValue("id"))

	if id <= 0 {
		responseNew.Msg = "权限id不能为空"
		return c.JSON(http.StatusBadRequest, responseNew)
	}
	if isPermission > 1 || isPermission < 0 {
		responseNew.Msg = "is_permission参数不合法"
		return c.JSON(http.StatusBadRequest, responseNew)
	}
	glog.Info("AddButtonPermissions: ", isPermission, " id : ", id)
	client := sv.GetStockVisualClient(c)
	defer client.Close()

	vo := sv.AddButtonPermissionsVo{IsPermission: isPermission, Id: id, DelProPermission: delProPermission}
	glog.Info("AddButtonPermissions参数： ", kit.JsonEncode(vo))
	response, err := client.RPC.AddButtonPermissions(client.Ctx, &vo)
	if err != nil {
		responseNew.Msg = "新增按钮权限异常"
		glog.Error(responseNew.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, responseNew)
	}
	if response.Code != 200 {
		responseNew.Msg = "新增按钮权限失败"
		glog.Error(responseNew.Msg, response.Msg)
		return c.JSON(http.StatusBadRequest, responseNew)
	}

	return c.JSON(http.StatusOK, responseNew)
}

// @Summary v6.3.3 查询用户是否有按钮权限
// @Tags 库存可视化 v6.6.7.1
// @Produce json
// @Success 200 {object} models.BaseResponseNew
// @Failure 400 {object} models.BaseResponseNew
// @Router /boss/stock-visual/auth/GetButtonPermissions [get]
func GetButtonPermissions(c echo.Context) error {
	responseNew := models.BaseResponseNew{
		Msg: "successful",
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		responseNew.Msg = err.Error()
		return c.JSON(400, responseNew)
	}
	find_str := userInfo.UserNo

	vo := sv.AuthInfoListRequest{
		PageSize:  math.MaxInt32,
		PageIndex: 1,
		FindStr:   find_str,
	}
	glog.Info("GetButtonPermissions参数： ", kit.JsonEncode(vo))
	client := sv.GetStockVisualClient(c)
	defer client.Close()
	res, err := client.RPC.AuthUserInfoList(client.Ctx, &vo)

	glog.Info("res 返回： ", kit.JsonEncode(res))
	if err != nil {
		responseNew.Msg = err.Error()
		return c.JSON(400, responseNew)
	}
	if res.Code == 400 {
		responseNew.Msg = "查询权限失败"
		return c.JSON(400, responseNew)
	}

	var is_permissiom = false
	responseNew.DelProPermission = false
	for i := range res.Data {
		info := res.Data[i]
		if info.ButtonPermission == 1 {
			is_permissiom = true
		}
		if info.DelProPermission == 1 {
			responseNew.DelProPermission = true
		}
	}

	responseNew.Data = is_permissiom
	return c.JSON(http.StatusOK, responseNew)
}
