package controller

import (
	"_/params"
	"_/proto/cc"
	"_/utils"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"google.golang.org/grpc/status"
	"net/http"
)

// CreateVirtualCardCancel
// @summary 创建虚拟卡注销信息接口
// @Tags 虚拟卡券模块
// @Accept json
// @Produce json
// @Param VipCardVirtualCancelReq body params.VipCardVirtualCancelReq true "参数结构体"
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/vip/virtual_card_cancel/create [POST]
func CreateVirtualCardCancel(c echo.Context) error {
	var (
		param            = new(params.VipCardVirtualCancelReq)
		out              = new(params.BaseResponse)
		userNo, userName = "", ""
	)
	logPrefix := "CreateVirtualCardCancel ======== 接口地址：/boss/vip/virtual_card_cancel/create"
	//获取用户信息
	if claims, err := utils.GetPayloadDirectly(c); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		userNo = utils.InterfaceToString(claims["userno"])
		userName = utils.InterfaceToString(claims["name"])
	}
	fmt.Printf("%s,%s", userNo, userName)
	if err := c.Bind(param); err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if len(param.FileUrl) == 0 {
		out.Message = "附件信息不能为空"
		return c.JSON(http.StatusBadRequest, out)
	}
	rpcReq := cc.CancelVirtualCardReq{
		FileUrl:      param.FileUrl,
		CancelRemark: param.CancelRemark,
		OrgId:        param.OrgId,
		OrgName:      param.OrgName,
		UserNo:       userNo,
		UserName:     userName,
	}
	customerClient := cc.GetCustomerCenterClient()
	defer customerClient.Conn.Close()
	defer customerClient.Cf()

	glog.Info(logPrefix, "数据返回：rpc请求参数：", utils.InterfaceToJSON(rpcReq))
	if _, err := customerClient.VirtualCard.CancelVirtualCard(customerClient.Ctx, &rpcReq); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logPrefix, "rpc错误：", err.Error(), "rpc请求参数：", utils.InterfaceToJSON(rpcReq))
			out.Message = s.Message()
			return c.JSON(http.StatusBadRequest, out)
		}
	}
	return c.JSON(http.StatusOK, out)
}

// VirtualCardCancelList
// @summary 获取虚拟卡券注销结果列表
// @Tags 虚拟卡券模块
// @Accept json
// @Produce json
// @Param page_index query int false "分页-当前页"
// @Param page_size query int false "分页-每页页码"
// @Success 200 {object} params.VipCardVirtualCancelRes
// @Failure 400 {object} params.VipCardVirtualCancelRes
// @Router /boss/vip/virtual_card_cancel/list [GET]
func VirtualCardCancelList(c echo.Context) error {
	var (
		out    = new(params.VipCardVirtualCancelRes)
		userNo = ""
	)
	logPrefix := "VirtualCardCancelList ======== 接口地址：/boss/vip/virtual_card_cancel/list"
	//获取用户信息
	if claims, err := utils.GetPayloadDirectly(c); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		userNo = utils.InterfaceToString(claims["userno"])
	}
	pageIndex := cast.ToInt(c.QueryParam("page_index"))
	if pageIndex == 0 {
		pageIndex = 1
	}
	pageSize := cast.ToInt(c.QueryParam("page_size"))
	if pageSize == 0 {
		pageSize = 10
	}
	rpcReq := cc.VirtualCardCancelListReq{
		PageIndex: int32(pageIndex),
		PageSize:  int32(pageSize),
		UserNo:    userNo,
	}
	customerClient := cc.GetCustomerCenterClient()
	defer customerClient.Conn.Close()
	defer customerClient.Cf()

	glog.Info(logPrefix, "数据返回：rpc请求参数：", utils.InterfaceToJSON(rpcReq))
	if rpcRes, err := customerClient.VirtualCard.VirtualCardCancelList(customerClient.Ctx, &rpcReq); err != nil {
		if s, ok := status.FromError(err); ok {
			glog.Error(logPrefix, "rpc错误：", err.Error(), "rpc请求参数：", utils.InterfaceToJSON(rpcReq))
			out.Message = s.Message()
			return c.JSON(http.StatusBadRequest, out)
		}
	} else {
		glog.Info(logPrefix, "数据返回：rpc请求参数：", utils.InterfaceToJSON(rpcReq), ";返回结果：", utils.InterfaceToJSON(rpcRes))
		if err := utils.MapTo(&rpcRes.List, &out.Data); err != nil {
			glog.Error(logPrefix, "返回结果数据结构转换失败：", err)
			out.Message = err.Error()
			return c.JSON(http.StatusBadRequest, out)
		}
		out.PageCount = rpcRes.PageCount
	}
	return c.JSON(http.StatusOK, out)
}
