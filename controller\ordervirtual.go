package controller

import (
	"_/proto/oc"
	"_/utils"
	"encoding/json"
	"github.com/labstack/echo/v4"
	"github.com/tricobbler/echo-tool/validate"
	"net/http"
	"strings"
)

type Response struct {
	Message string `json:"message"`
}

// @Summary 人工核销检测
// @Tags 客服工具
// @Accept json
// @Produce json
// @Param verify_code query string true "核销码"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Router /boss/ordercenter/order/virtual/manual-written-off-check [get]
func ManualWrittenOffCheck(c echo.Context) error {
	writtenOffCode := c.QueryParam("verify_code")

	if strings.Trim(writtenOffCode, " ") == "" {
		return c.JSON(400, Response{Message: "核销码不能为空"})
	}

	osClient := oc.GetOrderServiceClient()
	rs, err := osClient.RPC.GetVirtualOrderDetail(osClient.Ctx, &oc.GetVirtualOrderDetailRequest{
		WrittenOffCode: writtenOffCode,
	})

	if err != nil {
		return c.JSON(400, Response{Message: err.Error()})
	}

	type Order struct {
		Orderid   string `json:"orderid"`   //订单号
		Chargeoff int32  `json:"chargeoff"` //核销状态 -- 很重要 2-待核销，3-已核销，1-不需要核销，4-已过期
	}
	order := Order{}
	_ = json.Unmarshal([]byte(rs.OldOrderDetail), &order)

	if order.Orderid == "" {
		return c.JSON(400, Response{Message: "核销码不存在"})
	}
	if order.Chargeoff != 0 {
		return c.JSON(400, Response{Message: "核销状态不符，不能核销"})
	}
	if rs.Code == http.StatusOK {
		rs.Message = "未核销"
	}

	return c.JSON(int(rs.Code), Response{Message: rs.Message})
}

// @Summary 人工核销
// @Tags 客服工具
// @Accept json
// @Produce json
// @Param verify_code body string true "核销码"
// @Param financial_code body string true "门店财务编码"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Router /boss/ordercenter/order/virtual/manual-written-off [post]
func ManualWrittenOff(c echo.Context) error {
	type Request struct {
		VerifyCode    string `json:"verify_code" validate:"required" label:"核销码"`
		FinancialCode string `json:"financial_code" validate:"required" label:"财务编码"`
	}

	r := new(Request)

	if err := c.Bind(r); err != nil {
		return c.JSON(400, Response{Message: "请求参数错误"})
	}
	if err := c.Validate(r); err != nil {
		errLists := validate.Translate(err)
		return c.JSON(400, Response{Message: strings.Join(errLists, ", ")})
	}

	osClient := oc.GetOrderServiceClient()
	rs, err := osClient.RPC.WrittenOffByFinancialCode(osClient.Ctx, &oc.WrittenOffByFinancialCodeRequest{
		VerifyCode:    r.VerifyCode,
		FinancialCode: r.FinancialCode,
	})

	if err != nil {
		return c.JSON(400, Response{Message: err.Error()})
	}
	if rs.Code != 200 {
		return c.JSON(400, Response{Message: rs.Message})
	}

	return c.JSON(200, Response{Message: "核销成功"})
}

// @Summary 查询电商虚拟订单基本信息
// @Tags 客服工具
// @Accept json
// @Produce json
// @Param order_sn query string true "订单号"
// @Success 200 {object} oc.QueryMallVirtualOrderExtendInfoResponse
// @Failure 400 {object} oc.QueryMallVirtualOrderExtendInfoResponse
// @Router /boss/ordercenter/order/virtual/queryMallVirtualOrderExtendInfo [get]
func QueryMallVirtualOrderExtendInfo(c echo.Context) error {
	req := &oc.QueryMallVirtualOrderExtendInfoRequest{
		OrderSn: strings.Trim(c.QueryParam("order_sn"), " "),
	}
	if req.OrderSn == "" {
		return c.JSON(400, Response{Message: "订单号不能为空"})
	}
	osClient := oc.GetOrderServiceClient()
	rs, err := osClient.RPC.QueryMallVirtualOrderExtendInfo(osClient.Ctx, req)
	if err != nil {
		return c.JSON(400, oc.QueryMallVirtualOrderExtendInfoResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(int(rs.Code), rs)
}

// @Summary 延长核销码期限
// @Tags 客服工具
// @Accept json
// @Produce json
// @Param order_sn body string true "电商虚拟订单号"
// @Param date body string true "日期 如2022-10-09"
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/ordercenter/order/virtual/extendMallVirtualOrderVerifyCodeExpiryDate [post]
func ExtendMallVirtualOrderVerifyCodeExpiryDate(c echo.Context) error {
	req := &oc.ExtendMallVirtualOrderVerifyCodeExpiryDateRequest{}
	if err := c.Bind(req); err != nil {
		return c.JSON(400, oc.BaseResponse{Code: 400, Message: "参数错误", Error: err.Error()})
	}
	req.OrderSn = strings.Trim(req.OrderSn, " ")
	if req.OrderSn == "" {
		return c.JSON(400, oc.BaseResponse{Message: "订单号不能为空"})
	}
	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		return c.JSON(400, oc.BaseResponse{Message: "获取用户信息错误", Error: err.Error()})
	}
	req.UserName = userInfo.UserName
	req.UserNo = userInfo.UserNo
	osClient := oc.GetOrderServiceClient()
	rs, err := osClient.RPC.ExtendMallVirtualOrderVerifyCodeExpiryDate(osClient.Ctx, req)
	if err != nil {
		return c.JSON(400, oc.BaseResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(int(rs.Code), rs)
}
