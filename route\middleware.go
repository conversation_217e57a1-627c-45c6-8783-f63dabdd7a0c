package route

import (
	"_/controller"
	"_/models"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

var mapResult = make(map[string]string)

// 校验渠道id和来源，并写入context
func SetOrgId() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 首先检查请求头中是否已有 org_id
			if orgId := c.Request().Header.Get("org_id"); orgId != "" {
				return next(c)
			}

			structOuterCode := c.Request().Header.Get("structOuterCode")
			if _, has := mapResult[structOuterCode]; has {
				c.Request().Header.Set("org_id", mapResult[structOuterCode])
			} else {
				db := controller.GetDatacenterDBConn()
				orgInfo := new(models.OrganizationInfo)
				_, err := db.Where("zl_org_id=?", structOuterCode).Get(orgInfo)
				if orgInfo.Id == 0 {
					orgInfo.Id = 1
				}
				if err != nil {
					glog.Error("查询主体ID出错", err.Error())
					//出错了默认给1
					c.Request().Header.Set("org_id", "1")
				}
				mapResult[structOuterCode] = cast.ToString(orgInfo.Id)
				c.Request().Header.Set("org_id", mapResult[structOuterCode])
			}

			return next(c)
		}
	}
}
