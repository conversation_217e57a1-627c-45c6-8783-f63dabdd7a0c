package controller

import (
	"_/dto"
	"_/proto/igc"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// IntegralRecordPageList
// @Summary 获取会员积分明细列表
// @Tags 积分明细
// @Accept json
// @Produce json
// @param page_size body integer true "每页条数，10"
// @param page_index body integer true "页码，默认1"
// @param integral_type body integer true "每页条数"
// @param member_id body string true "每页条数"
// @Success 200 {object} oc.GetIntegralListByMemberIdRes
// @Failure 400 {object} oc.GetIntegralListByMemberIdRes
// @Router /boss/integral-record/page-list [get]
func IntegralRecordPageList(c echo.Context) error {
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}

	client := igc.GetIntegralServiceClient()
	param := &igc.PageIntegralRecordReq{
		PageIndex:    cast.ToInt32(c.QueryParam("page_index")),
		PageSize:     cast.ToInt32(c.QueryParam("page_size")),
		IntegralType: cast.ToInt32(c.QueryParam("integral_type")),
		MemberId:     c.QueryParam("member_id"),
		OrgId:        cast.ToInt32(orgId),
	}
	if out, err := client.PRC.PageIntegralRecord(client.Ctx, param); err != nil {
		return c.JSON(400, &igc.PageIntegralRecordResp{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// IntegralRecordPageList
// @Summary 获取用户剩余积分
// @Tags 剩余积分
// @Accept json
// @Produce json
// @param member_id body string true "每页条数"
// @Success 200 {object} oc.GetIntegralListByMemberIdRes
// @Failure 400 {object} oc.GetIntegralListByMemberIdRes
// @Router /boss/integral-record/integralCurrentGet [get]
func IntegralCurrentGet(c echo.Context) error {
	// 获取主体标识

	out := dto.IntegralCurrent{}
	out.Code = 400
	scrmId := c.QueryParam("scrm_id")
	db := GetDatacenterDBConn()
	_, err := db.SQL("select integral  from  datacenter.member_integral_info where memberid=? and org_id=1", scrmId).Get(&out.Integral)
	if err != nil {
		out.Message = "获取积分信息失败" + err.Error()
	}
	out.Code = 200
	return c.JSON(int(out.Code), out)

}
