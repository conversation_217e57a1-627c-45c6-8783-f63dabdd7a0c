package controller

import (
	"_/proto/dac"
	"_/utils"
	"bytes"
	"io"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// ImportTemplate
// @Summary 导入模板下载
// @Tags 导入
// @Accept json
// @Produce octet-stream
// @Param model query dac.ImportTemplateRequest true " "
// @Success 200 {string} string "文件流"
// @Failure 400 {object} dac.ImportTemplateResponse
// @Router /boss/datacenter/import-template [get]
func ImportTemplate(c echo.Context) error {
	req := &dac.ImportTemplateRequest{
		Type: cast.ToInt32(c.QueryParam("type")),
	}
	client := dac.GetDataCenterClient()
	out, err := client.RPC.ImportTemplate(client.Ctx, req)
	if err == nil && out.Code == 200 {
		c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename=自提点导入模板.xlsx")
		return c.Blob(200, echo.MIMEOctetStream, out.Template)
	} else if err != nil {
		return c.JSON(400, &dac.ImportTemplateResponse{Code: 400, Message: err.Error()})
	}
	return c.JSON(400, &dac.ImportTemplateResponse{Code: 400, Message: out.Message})
}

// Import
// @Summary 导入
// @Tags 导入
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "上传的文件"
// @Param model query dac.ImportRequest true " "
// @Success 200 {object} dac.ImportResponse
// @Failure 400 {object} dac.ImportResponse
// @Router /boss/datacenter/import [post]
func Import(c echo.Context) error {
	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req := &dac.ImportRequest{
		Type:          cast.ToInt32(c.FormValue("type")),
		EffectiveType: cast.ToInt32(c.FormValue("effective_type")),
		EffectiveTime: c.FormValue("effective_time"),
		OrgId:         cast.ToInt32(orgId),
	}
	rsp := &dac.ImportResponse{Code: 400}

	if req.Type > 2 && req.EffectiveType < 1 || (req.EffectiveType == 2 && req.EffectiveTime == "") {
		rsp.Message = "请选择生效时间类型"
		return c.JSON(400, rsp)
	}
	file, _, err := c.Request().FormFile("file")
	if err != nil {
		rsp.Message = "获取文件出错 " + err.Error()
		return c.JSON(400, rsp)
	}
	defer file.Close()

	buf := bytes.NewBuffer(nil)
	if _, err := io.Copy(buf, file); err != nil {
		rsp.Message = "复制文件出错 " + err.Error()
		return c.JSON(400, rsp)
	}
	req.File = buf.Bytes()

	client := dac.GetDataCenterClient()
	// 附加操作人信息
	ctx, _ := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if out, err := client.RPC.Import(ctx, req); err != nil {
		rsp.Message = err.Error()
		return c.JSON(400, rsp)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// ImportHistory
// @Summary 导入历史记录
// @Tags 导入
// @Accept json
// @Produce json
// @Param model query dac.ImportHistoryRequest true " "
// @Success 200 {object} dac.ImportHistoryResponse
// @Failure 400 {object} dac.ImportHistoryResponse
// @Router /boss/datacenter/import-history [get]
func ImportHistory(c echo.Context) error {
	req := &dac.ImportHistoryRequest{
		Type:      cast.ToInt32(c.QueryParam("type")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
	}
	client := dac.GetDataCenterClient()
	// 附加操作人信息
	ctx, _ := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if out, err := client.RPC.ImportHistories(ctx, req); err != nil {
		return c.JSON(400, &dac.ImportHistoryResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
