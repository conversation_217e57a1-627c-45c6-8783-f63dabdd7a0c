package controller

import (
	"_/params"
	"_/proto/ac"
	"_/utils"
	"crypto/tls"
	"net/http"
	"path"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"google.golang.org/grpc/status"

	"github.com/tricobbler/echo-tool/validate"
	kit "github.com/tricobbler/rp-kit"
)

// ---------------------------------新人专享商品管理部分----------------------------
// GetNewBuyProductList
// @Summary 获取新人专享商品列表
// @Tags 新人专享商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Param nid query string false "新人专享活动id 多个活动用逗号隔开"
// @Param productName query string false "商品名称"
// @Param skuId query int false "商品sku id"
// @Param productId query int false "商品的产品id"
// @Param GetNewBuyProductListRequest body params.GetNewBuyProductListRequest true " "
// @Success 200 {object} params.GetNewBuyProductListResponse
// @Failure 400 {object} params.GetNewBuyProductListResponse
// @Router /boss/market/new-buy/product/list [GET]
func GetNewBuyProductList(c echo.Context) error {
	var (
		out    params.GetNewBuyProductListResponse
		err    error
		rpcRes *ac.GetNewBuyProductListResponse
	)
	out.Code = http.StatusBadRequest
	//请求参数
	request := new(params.GetNewBuyProductListRequest)

	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	//rpc请求参数
	rpcRequestParam := new(ac.GetNewBuyProductListRequest)
	//分页与时间区间查询参数转换
	rpcRequestParam.Pagination = &ac.Page{
		PageIndex: request.PageIndex,
		PageSize:  request.PageSize,
	}
	rpcRequestParam.Nid = request.Nid
	rpcRequestParam.ProductName = request.ProductName
	rpcRequestParam.SkuId = request.SkuId
	rpcRequestParam.ProductId = request.ProductId
	rpcRequestParam.Status = request.Status
	rpcRequestParam.ChannelId = ChannelMallId
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("GetNewBuyProductList rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.NB.GetNewBuyProductList(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetNewBuyProductList rpc error:", err)
		out.Message = "获取失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if rpcRes.Code != http.StatusOK {
		glog.Error("GetNewBuyProductList rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Total = rpcRes.Total
	out.Code = http.StatusOK
	out.Message = "获取成功"

	if rpcRes.Total > 0 {
		for _, v := range rpcRes.Data {
			item := new(params.NewBuyProductDataItem)
			err = utils.MapTo(v, item)
			if err != nil {
				glog.Error("GetNewBuyProductList MapTo fail:", err.Error())
				out.Message = err.Error()
				return c.JSON(http.StatusBadRequest, out)
			}
			out.Data = append(out.Data, item)
		}
	}
	return c.JSON(http.StatusOK, out)

}

// GetNewBuyProductDetail
// @Summary 新人专享活动商品详细信息
// @Tags 新人专享商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param id query int true "记录id"
// @Param NIdRequest body params.NIdRequest true " "
// @Success 200 {object} params.GetNewBuyProductDetailResponse
// @Failure 400 {object} params.GetNewBuyProductDetailResponse
// @Router /boss/market/new-buy/product/detail [GET]
func GetNewBuyProductDetail(c echo.Context) error {
	var (
		out    params.GetNewBuyProductDetailResponse //返回参数
		err    error
		rpcRes *ac.GetNewBuyProductDetailResponse //rpc返回结果
	)

	out.Code = http.StatusBadRequest
	request := new(params.NIdRequest)

	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam := new(ac.NewBuyProductDetailRequest)
	rpcRequestParam.Id = request.Id
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("GetNewBuyProductDetail rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.NB.GetNewBuyProductDetail(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetNewBuyProductDetail rpc error:", err)

		out.Message = "更新失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("GetNewBuyProductDetail rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	outData := new(params.NewBuyProductDataItem)
	if err = utils.MapTo(rpcRes.Data, outData); err != nil {
		glog.Error("GetNewBuyProductDetail MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = http.StatusOK
	out.Data = outData
	return c.JSON(http.StatusOK, out)
}

// CreateNewBuyProduct
// @Summary 新人专享活动添加商品
// @Tags 新人专享商品管理
// @Accept json
// @Produce json
// @Param SaveNewBuyProductRequest body params.SaveNewBuyProductRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/new-buy/product [POST]
func CreateNewBuyProduct(c echo.Context) error {
	var (
		out params.BaseResponse
	)

	out.Code = http.StatusBadRequest
	request := new(params.SaveNewBuyProductRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数校验
	pass, validateMsg := checkNewBuyProductSaveData(request, false)
	if pass == false {
		out.Message = validateMsg
		return c.JSON(http.StatusBadRequest, out)
	}
	saveData := new(ac.SaveNewBuyProductData)
	if err := utils.MapTo(request, saveData); err != nil {
		glog.Error("CreateNewBuyProduct MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数转换
	rpcRequestParam := new(ac.CreateNewBuyProductRequest)
	rpcRequestParam.SkuId = request.SkuId
	rpcRequestParam.ProductId = request.ProductId
	rpcRequestParam.SaveData = saveData

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("CreateNewBuyProduct rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err := client.NB.CreateNewBuyProduct(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("CreateNewBuyProduct rpc error:", err)
		out.Message = "创建失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("CreateNewBuyProduct rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	out.Code = http.StatusOK
	out.Message = "创建成功"
	return c.JSON(http.StatusOK, out)
}

// 创建与新增新人专享商品参数检测与数据处理
func checkNewBuyProductSaveData(p *params.SaveNewBuyProductRequest, isUpdate bool) (res bool, msg string) {
	if isUpdate == true {
		if p.Id == 0 {
			msg = "修改时，id为必传"
			return
		}
	}
	if len([]rune(p.ShortTitle)) > 15 || len([]rune(p.LongTitle)) > 50 {
		msg = "商品短标题不能大于15个字符或商品长标题不能大于50个字符"
		return
	}
	return true, ""
}

// UpdateNewBuyProduct
// @Summary 修改新人专享活动商品信息
// @Tags 新人专享商品管理
// @Accept json
// @Produce json
// @Param SaveNewBuyProductRequest body params.SaveNewBuyProductRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/new-buy/product [PUT]
func UpdateNewBuyProduct(c echo.Context) error {
	var (
		out params.BaseResponse
	)

	out.Code = http.StatusBadRequest
	request := new(params.SaveNewBuyProductRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数校验
	pass, validateMsg := checkNewBuyProductSaveData(request, true)
	if pass == false {
		out.Message = validateMsg
		return c.JSON(http.StatusBadRequest, out)
	}

	saveData := new(ac.SaveNewBuyProductData)
	if err := utils.MapTo(request, saveData); err != nil {
		glog.Error("UpdateNewBuyProduct MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	//参数转换
	rpcRequestParam := new(ac.UpdateNewBuyProductRequest)
	rpcRequestParam.Id = request.Id
	rpcRequestParam.SkuId = request.SkuId
	rpcRequestParam.ProductId = request.ProductId
	rpcRequestParam.SaveData = saveData
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("UpdateNewBuyProduct rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err := client.NB.UpdateNewBuyProduct(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("UpdateNewBuyProduct rpc error:", err)
		out.Message = "更新失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("UpdateNewBuyProduct rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	out.Code = http.StatusOK
	out.Message = "修改成功"

	return c.JSON(http.StatusOK, out)
}

// DeleteNewBuyProduct
// @Summary 删除新人专享活动商品信息
// @Tags 新人专享商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param id query int true "记录id"
// @Param NIdRequest body params.NIdRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/new-buy/product [DELETE]
func DeleteNewBuyProduct(c echo.Context) error {
	var (
		out params.BaseResponse
	)

	out.Code = http.StatusBadRequest
	request := new(params.NIdRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam := new(ac.NewBuyProductIdRequest)
	rpcRequestParam.Id = request.Id
	//rpc请求
	client := ac.GetActivityCenterClient()
	glog.Info("DeleteNewBuyProduct rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	if rpcRes, err := client.NB.DeleteNewBuyProduct(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("DeleteNewBuyProduct rpc error:", err, "rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
		out.Message = "删除失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("DeleteNewBuyProduct rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
	}

	out.Code = http.StatusOK
	out.Message = "删除成功"
	return c.JSON(http.StatusOK, out)
}

// GetNewBuyUPetProductSelectList
// @Summary 获取可以参加新人专享活动的阿闻商城的商品
// @Tags 新人专享商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Param nid query int true "新人专享活动id"
// @Param productName query string false "商品名称"
// @Param skuId query int false "商品sku id"
// @Param productId query int false "商品的产品id"
// @Param GetNewBuyUPetProductSelectListRequest body params.GetNewBuyUPetProductSelectListRequest true " "
// @Success 200 {object} params.GetNewBuyUPetProductSelectListResponse
// @Failure 400 {object} params.GetNewBuyUPetProductSelectListResponse
// @Router /boss/market/new-buy/product/select-list [GET]
func GetNewBuyUPetProductSelectList(c echo.Context) error {
	var (
		out    params.GetNewBuyUPetProductSelectListResponse //返回参数
		err    error
		rpcRes *ac.GetNewBuyUPetProductSelectListResponse //RPC请求返回结果
	)

	out.Code = http.StatusBadRequest
	request := new(params.GetNewBuyUPetProductSelectListRequest)
	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam := new(ac.GetNewBuyUPetProductSelectListRequest)

	//分页与时间区间查询参数转换
	rpcRequestParam.Pagination = &ac.Page{
		PageIndex: request.PageIndex,
		PageSize:  request.PageSize,
	}
	rpcRequestParam.Nid = request.Nid
	rpcRequestParam.ProductName = request.ProductName
	rpcRequestParam.SkuId = request.SkuId
	rpcRequestParam.ProductId = request.ProductId
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	glog.Info("GetNewBuyUPetProductSelectList rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.NB.GetNewBuyUPetProductSelectList(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetNewBuyUPetProductSelectList rpc error:", err)
		out.Message = "获取失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if rpcRes.Code != http.StatusOK {
		glog.Error("GetNewBuyUPetProductSelectList rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Total = rpcRes.Total
	out.Code = http.StatusOK
	out.Message = "获取成功"

	if rpcRes.Total > 0 {
		for _, v := range rpcRes.Data {
			item := new(params.NewBuySelectUPetProductData)
			err = utils.MapTo(v, item)
			if err != nil {
				glog.Error("GetNewBuyUPetProductSelectList MapTo fail:", err.Error())
				out.Message = err.Error()
				return c.JSON(http.StatusBadRequest, out)
			}
			out.Data = append(out.Data, item)
		}
	}
	return c.JSON(http.StatusOK, out)
}

// GetNewBuyProductImportList
// @Summary 活动商品导入列表
// @Tags 新人专享商品管理
// @Accept plain
// @Accept json
// @Produce json
// @Param pageIndex query int true "当前多少页 从1开始"
// @Param pageSize query int true "每页多少条数据"
// @Param id query int true "新人专享活动id"
// @Param GetNewBuyProductImportListRequest body params.GetNewBuyProductImportListRequest true " "
// @Success 200 {object} params.GetNewBuyProductImportListResponse
// @Failure 400 {object} params.GetNewBuyProductImportListResponse
// @Router /boss/market/new-buy/product/import-list [GET]
func GetNewBuyProductImportList(c echo.Context) error {
	var (
		out    params.GetNewBuyProductImportListResponse //返回参数
		err    error
		rpcRes *ac.TaskListResponse //RPC请求返回结果
	)

	out.Code = http.StatusBadRequest
	request := new(params.GetNewBuyProductImportListRequest)
	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam := new(ac.TaskListRequest)
	rpcRequestParam.Nid = request.Id

	//分页与时间区间查询参数转换
	rpcRequestParam.Pagination = &ac.Page{
		PageIndex: request.PageIndex,
		PageSize:  request.PageSize,
	}
	rpcRequestParam.Nid = request.Id
	glog.Info("GetNewBuyProductImportList rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.NB.GetTaskList(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetNewBuyProductImportList rpc error:", err)
		out.Message = "获取失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if rpcRes.Code != http.StatusOK {
		glog.Error("GetNewBuyProductImportList rpc fail:", rpcRes.Msg)
		out.Message = rpcRes.Msg
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Total = rpcRes.Total
	out.Code = http.StatusOK
	out.Message = "获取成功"

	if rpcRes.Total > 0 {
		for _, v := range rpcRes.Data {
			item := new(params.NewBuyProductImportData)
			err = utils.MapTo(v, item)
			if err != nil {
				glog.Error("GetNewBuyProductImportList MapTo fail:", err.Error())
				out.Message = err.Error()
				return c.JSON(http.StatusBadRequest, out)
			}
			out.Data = append(out.Data, item)
		}
	}
	return c.JSON(http.StatusOK, out)
}

// ImportNewBuyProduct
// @Summary 活动商品导入
// @Tags 新人专享商品管理
// @Accept json
// @Produce json
// @Param ImportNewBuyProductRequest body params.ImportNewBuyProductRequest true " "
// @Success 200 {object} params.BaseResponse
// @Failure 400 {object} params.BaseResponse
// @Router /boss/market/new-buy/product/import [POST]
func ImportNewBuyProduct(c echo.Context) error {
	var (
		out params.BaseResponse
	)

	out.Code = http.StatusBadRequest
	request := new(params.ImportNewBuyProductRequest)

	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err := c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	// 校验导入文件的格式是否正确
	msg := importCheck(request.Url)
	if len(msg) > 0 {
		out.Message = msg
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数转换
	rpcRequestParam := new(ac.CreateTaskRequest)
	rpcRequestParam.Nid = request.Id
	rpcRequestParam.OperationFileUrl = request.Url
	rpcRequestParam.TaskContent = 1
	rpcRequestParam.RequestHeader = ""
	rpcRequestParam.CreateId = ""
	rpcRequestParam.OrgId = cast.ToInt32(c.Request().Header.Get("org_id"))

	glog.Info("ImportNewBuyProduct rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err := client.NB.CreateTask(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("ImportNewBuyProduct rpc error:", err)
		out.Message = "导入失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		if rpcRes.Code != http.StatusOK {
			glog.Error("ImportNewBuyProduct rpc fail:", rpcRes.Message)
			out.Message = rpcRes.Message
			return c.JSON(http.StatusBadRequest, out)
		}
		out.Code = http.StatusOK
		out.Message = rpcRes.Message
	}
	return c.JSON(http.StatusOK, out)
}

// 导入文件校验
func importCheck(url string) string {
	//判断文件扩展名
	fullFileName := path.Base(url)
	fileExt := path.Ext(fullFileName)
	if fileExt != ".xlsx" {
		return "导入文件格式仅支持.xlsx"
	}
	// 下载excel
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return "下载excel失败"
	}
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	resp, err := client.Do(req)
	if err != nil {
		return "下载excel失败"
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		return "读取excel失败"
	}
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows := f.GetRows(sheetName)

	if len(rows) <= 1 {
		return "请导入数据"
	}

	/*template := utils.CheckExcelTemplate(rows[0], utils.ExpiryTemplate[0])
	glog.Info("temolate：", kit.JsonEncode(rows[0]), template)
	if !template {
		return "导入格式不正确"
	}*/

	if len(rows) > 502 {
		return "单次最多导入500条数据"
	}

	return ""
}

// AddNewPeopleVoucher
// @Summary 添加新人专享券
// @Tags 新人专享
// @Produce json
// @Param NewPeopleVoucherAddRequest body ac.NewPeopleVoucherAddRequest true " "
// @Success 200 {object} ac.NewPeopleVoucherAddResponse
// @Failure 400 {object} ac.NewPeopleVoucherAddResponse
// @Router /boss/market/new_buy/voucher/add [POST]
func AddNewPeopleVoucher(c echo.Context) error {
	in := new(ac.NewPeopleVoucherAddRequest)
	out := new(ac.NewPeopleVoucherAddResponse)

	// A、参数绑定和校验
	if err := c.Bind(in); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	tIds := strings.Split(in.VoucherTIds, ",")
	if len(tIds) > 10 {
		out.Message = "最多填10张商城券id"
		return c.JSON(http.StatusBadRequest, out)
	}

	client := ac.GetActivityCenterClient()
	out, err := client.NB.NewPeopleVoucherAdd(client.Ctx, in)
	if err != nil {
		glog.Error(err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, out)
}

// GetNewPeopleVoucher
// @Summary 新人专享券获取
// @Tags 新人专享
// @Produce json
// @Param BaseRequest body ac.BaseRequest true " "
// @Success 200 {object} ac.NewBuyVoucherListResponse
// @Failure 400 {object} ac.BaseResponse
// @Router /boss/market/new_buy/voucher/get [GET]
func GetNewPeopleVoucherList(c echo.Context) error {
	in := new(ac.BaseRequest)
	out := new(ac.NewBuyVoucherListResponse)

	client := ac.GetActivityCenterClient()
	out, err := client.NB.GetNewPeopleVoucherList(client.Ctx, in)
	if err != nil {
		glog.Error(err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, out)
}

// GetNewBuyInfo
// @Summary 获取新人专享活动详情
// @Tags 新人专享
// @Accept x-www-form-urlencoded
// @Produce json
// @Success 200 {object} ac.NewBuyInfoResponse
// @Failure 400 {object} ac.BaseResponse
// @Router /boss/market/new_buy/info [GET]
/*func GetNewBuyInfo(c echo.Context) error {
	var (
		err         error
		out         ac.BaseResponse
		rpcRequest  *ac.NewBuyInfoRequest
		rpcResponse *ac.NewBuyInfoResponse
	)

	client := ac.GetActivityCenterClient()
	if rpcResponse, err = client.NB.GetNewBuyInfo(client.Ctx, rpcRequest); err != nil {
		glog.Error("GetNewBuyInfo rpc error:", err)
		if status, ok := status.FromError(err); ok {
			out.Code = http.StatusBadRequest
			out.Message = status.Message()
			out.Error = out.Message
		}
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, rpcResponse)

}*/

// GetNewBuyDetail
// @Summary 获取新人专享活动详情
// @Tags 新人专享
// @Produce json
// @Success 200 {object} params.GetNewBuyDetailResponse
// @Failure 400 {object} params.GetNewBuyDetailResponse
// @Router /boss/market/new_buy/detail [GET]
func GetNewBuyDetail(c echo.Context) error {
	var (
		out             params.GetNewBuyDetailResponse //返回参数
		rpcRequestParam ac.NewBuyInfoRequest           //rpc请求详情的参数
		rpcRes          *ac.NewBuyInfoResponse         //rpc获取详情的结果
		err             error
	)
	out.Code = http.StatusBadRequest
	outData := new(params.NewBuyRequest)

	rpcRequestParam.Id = 1

	glog.Info("GetNewBuyDetail rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.NB.GetNewBuyDetail(client.Ctx, &rpcRequestParam); err != nil {
		glog.Error("GetNewBuyDetail rpc error:", err)
		out.Message = "获取失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("GetNewBuyDetail rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = utils.MapTo(rpcRes.Detail, outData); err != nil {
		glog.Error("GetNewBuyDetail MapTo fail:", err.Error())
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = http.StatusOK
	out.Message = "获取成功"
	out.Data = outData
	return c.JSON(http.StatusOK, out)

}

// CreateNewBuy
// @summary 创建新人专享活动
// @tags 新人专享
// @accept json
// @produce json
// @Param NewBuyRequest body params.NewBuyRequest true " "
// @success 200 {object} ac.BaseResponse
// @failure 400 {object} ac.BaseResponse
// @router /boss/market/new_buy [POST]
func CreateNewBuy(c echo.Context) error {
	var (
		out ac.BaseResponse
		err error
	)
	out.Code = http.StatusBadRequest
	params := new(params.NewBuyRequest)
	rpcRequest := new(ac.NewBuyRequest)
	rpcResponse := new(ac.BaseResponse)

	//获取参数
	if err := c.Bind(params); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数验证
	if err := c.Validate(params); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = err.One()
		out.Error = err.All()
		return c.JSON(http.StatusBadRequest, out)
	}
	rpcRequest.Title = params.Title
	rpcRequest.BeginDate = params.BeginDate
	rpcRequest.EndDate = params.EndDate
	rpcRequest.Status = params.Status
	rpcRequest.Cover = params.Cover
	rpcRequest.HeadImg = params.HeadImg
	rpcRequest.IsShippingFree = params.IsShippingFree

	//调用activity-center服务
	logStr := "boss-CreateNewBuy 入参:%#v\n"
	glog.Infof(logStr+" | rpc RequestParam:%#v\n", params, rpcRequest)
	client := ac.GetActivityCenterClient()
	defer client.Conn.Close()
	if rpcResponse, err = client.NB.CreateNewBuy(client.Ctx, rpcRequest); err != nil {
		if status, ok := status.FromError(err); ok {
			glog.Errorf(logStr+" | return：%#v,err:%#v\n", params, rpcResponse, status.Message())
			out.Message = status.Message()
			out.Error = status.Message()
		}
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, rpcResponse)
}

// UpdateNewBuy
// @summary 更新新人专享活动
// @tags 新人专享
// @accept json
// @produce json
// @Param NewBuyRequest body params.NewBuyRequest true " "
// @success 200 {object} ac.UpdateNewBuyResponse
// @failure 400 {object} ac.UpdateNewBuyResponse
// @router /boss/market/new_buy [PUT]
func UpdateNewBuy(c echo.Context) error {
	var (
		out ac.BaseResponse
		err error
	)
	out.Code = http.StatusBadRequest
	params := new(params.NewBuyRequest)
	rpcRequest := new(ac.NewBuyRequest)
	rpcResponse := new(ac.UpdateNewBuyResponse)

	//获取参数
	if err := c.Bind(params); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	//参数验证
	if err := c.Validate(params); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = err.One()
		out.Error = err.All()
		return c.JSON(http.StatusBadRequest, out)
	}
	rpcRequest.Id = params.Id
	rpcRequest.Title = params.Title
	rpcRequest.BeginDate = params.BeginDate
	rpcRequest.EndDate = params.EndDate
	rpcRequest.Status = params.Status
	rpcRequest.Cover = params.Cover
	rpcRequest.HeadImg = params.HeadImg
	rpcRequest.ChannelId = 5
	rpcRequest.IsShippingFree = params.IsShippingFree

	//调用activity-center服务
	logStr := "boss-CreateNewBuy 入参:%#v\n"
	glog.Infof(logStr+" | rpc RequestParam:%#v\n", params, rpcRequest)
	client := ac.GetActivityCenterClient()
	defer client.Conn.Close()
	if rpcResponse, err = client.NB.UpdateNewBuy(client.Ctx, rpcRequest); err != nil {
		if status, ok := status.FromError(err); ok {
			glog.Errorf(logStr+" | return：%#v,err:%#v\n", params, rpcResponse, status.Message())
			out.Message = status.Message()
			out.Error = status.Message()
		}
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, rpcResponse)
}
