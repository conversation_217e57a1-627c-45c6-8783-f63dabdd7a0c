package controller

import (
	"_/proto/mk"
	"net/http"
	"reflect"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"

	"github.com/maybgit/glog"

	"context"
)

// 请求处理
func PromotionReachReduceDeliveryGrpcProcess(c echo.Context, request interface{}, grpcFun ReachReduceGrpcFuncCall) error {
	// 400 错误代码返回
	var badResponse = new(mk.BaseResponse)
	badResponse.Code = mk.Code_parameterError

	//获取请求参数到实体对象
	err := c.Bind(request)
	if err != nil {
		glog.Error(err)
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 获取grpc 链接
	var conn = mk.GetReduceDeliveryServiceClient()
	// 关闭链接
	defer conn.Close()
	if conn == nil {
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = "内部Grpc通讯错误"
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	//调用Grpc方法
	response, err := grpcFun(conn)
	if err != nil {
		glog.Error(err)
		badResponse.Code = mk.Code_grpcConnectionError
		badResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, badResponse)
	}

	// 反射查询Grpc响应
	var responseType = reflect.ValueOf(response)
	if responseType.Kind() == reflect.Ptr {
		responseType = responseType.Elem()
	}
	// 查询code 属性
	codePrototy := responseType.FieldByName("Code")
	if codePrototy.Kind() != reflect.Invalid {
		grpcCode := codePrototy.Int()
		// 解析错误代码
		if grpcCode > 400 {
			badResponse.Code = 400
			if grpcCode == int64(mk.Code_queryDbException) {
				badResponse.Error = "查询数据库异常"
			}
			if grpcCode == int64(mk.Code_saveDbException) {
				badResponse.Error = "保存数据库异常"
			}
			if grpcCode == int64(mk.Code_businessError) {
				// 是否有Message 信息
				var messageFiled = codePrototy.FieldByName("Message")
				if messageFiled.Kind() != reflect.Invalid {
					badResponse.Error = messageFiled.String()
				}
			}
			return c.JSON(http.StatusBadRequest, badResponse)
		}
	}

	return c.JSON(http.StatusOK, response)
}

/////////////////////////////////////////////////////// Query //////////////////////////////////////////////////////////////////////////////////

// @Summary 满减运费--新增
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.ReduceDeliveryAddRequest true "营销活动相关"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/AddReduceDelivery [Post]
func AddReduceDelivery(c echo.Context) error {
	var request = new(mk.ReduceDeliveryAddRequest)
	//调用封装的函数
	return PromotionReachReduceDeliveryGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReduceDelivery.Add(context.Background(), request)
	})
}

// @Summary 满减运费--修改
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.ReduceDeliveryUpdateRequest true "营销活动相关"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/UpdateReduceDelivery [Post]
func UpdateReduceDelivery(c echo.Context) error {
	var request = new(mk.ReduceDeliveryUpdateRequest)
	//调用封装的函数
	return PromotionReachReduceDeliveryGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReduceDelivery.Update(grpc.Ctx, request)

	})
}

// @Summary 满减运费--列表
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.ReduceDeliveryShopRequest true "营销活动相关"
// @Success 200 {object} mk.ReduceDeliveryShopResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/GetReduceDeliveryList [Post]
func GetReduceDeliveryList(c echo.Context) error {
	var request = new(mk.ReduceDeliveryShopRequest)
	// 调用封装的函数
	return PromotionReachReduceDeliveryGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReduceDelivery.QueryPromotionShopByQuery(grpc.Ctx, request)
	})
}

// @Summary 满减运费--详情
// @Tags 营销活动
// @Accept json
// @Produce json
// @param id query int true "满减运费活动ID"
// @Success 200 {object} mk.ReduceDeliveryByIdResponse
// @Success 400 {object} mk.ReduceDeliveryByIdResponse
// @Router /boss/Promotion/GetReduceDeliveryById [Get]
func GetReduceDeliveryById(c echo.Context) error {
	id := c.QueryParam("id")
	var request = &mk.QueryByIdRequest{Id: cast.ToInt32(id)}
	// 调用封装的函数
	return PromotionReachReduceDeliveryGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReduceDelivery.QueryById(grpc.Ctx, request)

	})
}

// @Summary 满减运费--根据店铺关联ID删除活动与店铺的关联
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.DeleteRequest true "营销活动相关"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/DeletePromotionShopById [Post]
func DeletePromotionShopById(c echo.Context) error {
	var request = new(mk.DeleteRequest)
	//调用封装的函数
	return PromotionReachReduceDeliveryGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReduceDelivery.DeletePromotionShopById(grpc.Ctx, request)
	})
}

// @Summary 满减运费--根据查询条件删除活动与店铺的关联
// @Tags 营销活动
// @Accept json
// @Produce json
// @param request body mk.ReduceDeliveryShopRequest true "营销活动相关"
// @Success 200 {object} mk.BaseResponse
// @Success 400 {object} mk.BaseResponse
// @Router /boss/Promotion/DeletePromotionShopByQuery [Post]
func DeletePromotionShopByQuery(c echo.Context) error {
	var request = new(mk.ReduceDeliveryShopRequest)
	//调用封装的函数
	return PromotionReachReduceDeliveryGrpcProcess(c, request, func(grpc *mk.Client) (interface{}, error) {
		return grpc.ReduceDelivery.DeletePromotionShopByQuery(grpc.Ctx, request)
	})
}
