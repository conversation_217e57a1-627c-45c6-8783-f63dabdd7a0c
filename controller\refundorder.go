package controller

import (
	"_/proto/oc"
	"_/utils"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/metadata"

	"github.com/maybgit/glog"

	"github.com/labstack/echo/v4"
)

// @Summary 商家-申请售后单-v6.0 saas-v1.0
// @Tags 售后单
// @Accept json
// @Produce json
// @Param apply body oc.RefundOrderApplyRequest true " "
// @Success 200 {object} oc.RefundOrderApplyResponse
// @Failure 400 {object} oc.RefundOrderApplyResponse
// @Router /boss/refund/apply [post]
func RefundOrderApply(c echo.Context) error {
	var res oc.RefundOrderApplyResponse
	res.Code = 200
	model := new(oc.RefundOrderApplyRequest)
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	refundOrderApplyRequestJson, _ := json.Marshal(model)

	glog.Info("RefundOrderApply:", string(refundOrderApplyRequestJson))
	client := oc.GetOrderServiceClient()

	if model.ChannelId == ChannelMtId || model.ChannelId == ChannelElmId || model.ChannelId == ChannelJddjId || model.ChannelId == 420 {
		out := &oc.BaseResponse{
			Code:    400,
			Message: "失败",
		}
		params := new(oc.OrderApplyPartRefundRequest)
		params.OrderId = model.ExternalOrderId
		params.Reason = model.Reason
		params.RefundType = 1
		params.OperationUser = model.OperationUser
		params.RefundCode = model.RefundCode
		var foodDatas []*oc.ApplyPartRefundList
		if len(model.RefundOrderGoodsData) > 0 {
			for _, item := range model.RefundOrderGoodsData {
				foodData := new(oc.ApplyPartRefundList)
				foodData.Count = float32(item.Quantity)
				foodData.SkuId = item.SkuId
				foodData.AppFoodCode = item.AppFoodCode
				foodData.SubBizOrderId = item.SubBizOrderId
				foodData.PromotionType = item.PromotionType
				foodData.PlatformSkuId = item.PlatformSkuId
				foodDatas = append(foodDatas, foodData)
			}
		}
		params.FoodData = foodDatas

		if res, err := client.AfterSale.OrderApplyPartRefund(client.Ctx, params); err != nil {
			glog.Error("第三方订单部分退款请求出错", err)
			out.Message = res.Message
			out.Error = res.Error
			return c.JSON(200, out)
		} else {

			glog.Info("RefundOrderApply2:", res, kit.JsonEncode(params))
			//为什么直接就返回了呢？ 美团返回
			if model.ChannelId == ChannelMtId || model.ChannelId == ChannelElmId {
				return c.JSON(200, res)
			}
			if res.Code == 400 {
				return c.JSON(int(res.Code), res)
			}
			// RefundOrderSn 为什么= message ???
			model.RefundOrderSn = res.Message
			model.FullRefund = 2
			answerModer := new(oc.RefundOrderAnswerRequest)
			answerModer.ExternalOrderId = model.ExternalOrderId
			//model中的订单号为是主订单号
			code, refundRes, refundErr := refundOrderFlow(c, model, answerModer)
			if refundErr != nil {
				return c.JSON(400, refundErr)
			} else {
				return c.JSON(int(code), refundRes)
			}
		}
	} else {
		refundGoods, err := client.ROC.SplitOrderRefundGoods(context.Background(), model)
		glog.Info("部分退款商品拆分结果", kit.JsonEncode(refundGoods))

		if err != nil {
			glog.Errorf("商家申请售后单错误：%s", err.Error())
			return c.JSON(400, err)
		}
		if refundGoods.Code != 200 {
			res.Code = 400
			return c.JSON(400, res)
		}

		//记录退款失败的订单id
		var fail []string
		for _, order := range refundGoods.OrderGoods {
			orderModel := new(oc.RefundOrderApplyRequest)
			//退款单整体信息
			orderModel = model
			//重置部分参数
			orderModel.OrderId = order.OrderSn
			orderModel.RefundOrderGoodsData = order.RefundOrderGoodsData
			orderModel.ExternalOrderId = order.OrderSn //第三方的订单号为阿闻订单号

			if order.IsAllRefund == 1 {
				orderModel.FullRefund = 1
			} else {
				orderModel.FullRefund = 2
			}

			answerModer := new(oc.RefundOrderAnswerRequest)
			glog.Info("申请售后单入参", kit.JsonEncode(model))
			glog.Info("申请售后单入参1", kit.JsonEncode(orderModel))
			_, refundRes, _ := refundOrderFlow(c, model, answerModer)

			if refundRes.Code != 200 {
				fail = append(fail, fmt.Sprintf("%s->%s", orderModel.OrderId, refundRes.Message))
			}
		}
		if len(fail) > 0 {
			res.Code = 400
			res.Message = "退款失败的订单：" + strings.Join(fail, ",")
		} else {
			res.Code = 200
			res.Message = "退款成功"
		}
		return c.JSON(int(res.Code), res)
	}
}

// 退款流程处理
// step1：申请退款并生成退款数据
// step2：自动应答售后单（商家发起为自动同意）
func refundOrderFlow(c echo.Context, model *oc.RefundOrderApplyRequest, answerModel *oc.RefundOrderAnswerRequest) (int32, *oc.RefundOrderApplyResponse, error) {
	var res oc.RefundOrderApplyResponse
	res.Code = 200

	model.ResType = "等待处理中"
	model.ApplyOpUserType = "2"
	model.OperationType = "商家申请售后单"
	//model.RefundOrderSn = GenerateRefundSn()
	if model.ChannelId != ChannelJddjId && model.ChannelId != 420 {
		answerModel.ExternalOrderId = model.OrderId
	}

	if model.RefundAmount > 0 {
		model.RefundAmount = (model.RefundAmount / 1000) * 10
	}
	// saas-v1.0

	client := oc.GetOrderServiceClient()
	if userInfo, err := utils.GetPayloadDirectlyToInterface(c); err == nil {
		userNo := userInfo.UserNo
		if userNo == "" {
			userNo = userInfo.ScrmId
		}
		client.Ctx = metadata.AppendToOutgoingContext(client.Ctx,
			"user-name", userInfo.UserName,
			"user-no", userNo,
			"ip", c.RealIP(),
		)
	}
	//写入退款数据
	glog.Info("RefundOrderApply申请退款单参数", kit.JsonEncode(model))
	out, err := client.ROC.RefundOrderApply(client.Ctx, model)
	glog.Info("RefundOrderApply申请退款单参数返回结果", kit.JsonEncode(out))
	if err != nil {
		glog.Errorf("商家申请售后单错误：%s", err.Error())
		return 400, out, err
	}

	if out.Code != 200 {
		return 400, out, nil
	}

	glog.Info("RefundOrderApply3:", out)
	answerModel.OrderId = model.OrderId

	answerModel.RefundOrderSn = out.RefundOrderSn
	answerModel.Reason = ""
	answerModel.ResultType = 1
	answerModel.ResultTypeNote = "商家同意退款"
	answerModel.OperationType = "商家同意退款"
	answerModel.OperationUser = model.OperationUser
	glog.Info(model.OrderId, "请求应答售后单入参:", kit.JsonEncode(answerModel))
	answerOut, err1 := client.ROC.RefundOrderAnswer(context.Background(), answerModel)
	if err1 != nil {
		glog.Errorf("商家自动应答售后单错误：%s", err1.Error())
		return 400, out, nil
	}

	glog.Info("RefundOrderApply4:", answerOut)
	return answerOut.Code, out, nil
}

// @Summary 审核售后单
// @Tags 售后单
// @Accept json
// @Produce json
// @Param cancel body oc.RefundOrderAnswerRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/refund/answer [post]
func RefundOrderAnswer(c echo.Context) error {

	var res oc.BaseResponse
	res.Code = 200
	answerModer := new(oc.RefundOrderAnswerRequest)
	if err := c.Bind(answerModer); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := oc.GetOrderServiceClient()

	answerModer.ResultType = 1
	answerModer.ResultTypeNote = "商家同意售后单"
	answerModer.OperationType = "商家同意售后单"
	answerOut, err := client.ROC.RefundOrderAnswer(context.Background(), answerModer)
	if err != nil {
		glog.Errorf("商家自动应答售后单错误：%s", err.Error())
		return c.JSON(400, err)
	}
	return c.JSON(int(answerOut.Code), answerOut)

}

// @Summary 撤销申请售后单
// @Tags 售后单
// @Accept json
// @Produce json
// @Param cancel body oc.RefundOrderCancelRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/refund/cancel [post]
func RefundOrderCancel(c echo.Context) error {
	var res oc.BaseResponse
	res.Code = 200
	model := new(oc.RefundOrderCancelRequest)
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := oc.GetOrderServiceClient()

	model.ResType = "商家取消售后单"
	model.OperationType = "商家取消售后单"
	answerOut, err := client.ROC.RefundOrderCancel(context.Background(), model)
	if err != nil {
		glog.Errorf("商家自动应答售后单错误：%s", err.Error())
		return c.JSON(400, err)
	}
	return c.JSON(int(answerOut.Code), answerOut)

}

// @Summary 发起退款操作
// @Tags 售后单
// @Accept json
// @Produce json
// @Param cancel body oc.RefundOrderCancelRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/refund/pay [post]
func RefundOrderPay(c echo.Context) error {
	var res oc.BaseResponse
	res.Code = 200
	model := new(oc.RefundOrderPayRequest)
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := oc.GetOrderServiceClient()

	model.ResType = "商家退款"
	model.OperationType = "商家退款"
	answerOut, err := client.ROC.RefundOrderPay(context.Background(), model)
	if err != nil {
		glog.Errorf("商家退款错误：%s", err.Error())
		return c.JSON(400, err)
	}
	return c.JSON(int(answerOut.Code), answerOut)
}

// @Summary v6.3.3 查看所有的异常退款的操作记录
// @Tags 退款单操作记录
// @Produce json
// @Param page_index query int true "当前页"
// @Param page_size query string true "分页大小"
// @Success 200 {object} oc.RefundRecordListResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/refund/pay/records [get]
func RefundOrderPayRecords(c echo.Context) error {
	var res oc.BaseResponse
	res.Code = 400
	orgId := cast.ToInt32(c.Request().Header.Get("org_id"))
	size := cast.ToInt32(c.QueryParam("page_size"))
	index := cast.ToInt32(c.QueryParam("page_index"))
	request := oc.ExpressCompanyListRequest{
		PageSize: size,
		Page:     index,
		OrgId:    int64(orgId),
	}

	client := oc.GetOrderServiceClient()
	answerOut, err := client.ROC.RefundRecordList(client.Ctx, &request)

	if err != nil {
		res.Message = "查询RefundRecordList异常 : "
		glog.Error(res.Message, err.Error())

		return c.JSON(400, res)
	}
	if answerOut.Code != 200 {
		res.Message = "查询RefundRecordList返回失败："
		glog.Error(res.Message, kit.JsonEncode(answerOut))
		return c.JSON(400, res)
	}

	return c.JSON(http.StatusOK, answerOut)
}

// @Summary v6.3.3 发起手动全退操作
// @Tags 售后单
// @Accept json
// @Produce json
// @Param vo body oc.RefundOrderPayVo true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/refund/pay/Manual [post]
func RefundOrderManual(c echo.Context) error {
	var res oc.BaseResponse
	res.Code = 400
	model := new(oc.RefundOrderPayVo)
	if err := c.Bind(model); err != nil {
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if len(model.Reason) <= 0 {
		res.Message = "退款原因必填"
		return c.JSON(400, res)
	}
	client := oc.GetOrderServiceClient()

	model.ResType = "商家退款"
	model.OperationType = "商家退款"
	model.IpAddr = c.RealIP()
	//glog.Info("ip : ", c.RealIP(), c.Request().URL, c.Request().RequestURI, "  remoteaddr: ", c.Request().RemoteAddr)
	//ctx := utils.AppendToOutgoingContextLoginUserInfo(client.Ctx, c)
	info, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Message = "获取授权信息失败"
		return c.JSON(400, res)
	}
	UserNo := info.UserNo
	if info.ScrmId != "" {
		UserNo = info.ScrmId
	}
	model.UserNo = UserNo
	model.UserName = info.UserName
	glog.Info("model : ", kit.JsonEncode(model))
	answerOut, err := client.ROC.RepeatRefundOrderPay(client.Ctx, model)
	if err != nil {
		res.Message = "商家退款错误"
		glog.Error(res.Message, err.Error())
		return c.JSON(400, res)
	}
	if answerOut.Code != 200 {
		res.Message = "商家退款失败: " + answerOut.Message
		glog.Error(res.Message)
		return c.JSON(400, res)
	}

	return c.JSON(http.StatusOK, answerOut)
}

// @Summary v6.3.3 已取消的订单手动完成
// @Tags 退款单操作记录
// @Param vo body oc.RefundOrderPayVo true " "
// @Accept json
// @Produce json
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /boss/refund/pay/finish [post]
func RefundOrderFinish(c echo.Context) error {

	var res oc.BaseResponse
	res.Code = 400
	model := new(oc.RefundOrderPayVo)
	if err := c.Bind(model); err != nil {
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	if len(model.Reason) <= 0 {
		res.Message = "完成原因必填"
		return c.JSON(400, res)
	}
	client := oc.GetOrderServiceClient()

	model.OperationType = "商家手动完成订单"
	model.IpAddr = c.RealIP()
	//glog.Info("ip : ", c.RealIP(), c.Request().URL, c.Request().RequestURI, "  remoteaddr: ", c.Request().RemoteAddr)

	//ctx := utils.AppendToOutgoingContextLoginUserInfo(client.Ctx, c)

	info, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		res.Message = "获取授权信息失败"
		glog.Error(res.Message, err.Error())
		return c.JSON(400, res)
	}
	UserNo := info.UserNo
	if info.ScrmId != "" {
		UserNo = info.ScrmId
	}
	model.UserNo = UserNo
	model.UserName = info.UserName

	glog.Info("model : ", kit.JsonEncode(model))
	answerOut, err := client.ROC.RefundOrderFinish(client.Ctx, model)
	if err != nil {
		res.Message = "商家手动完成订单异常"
		glog.Error(res.Message, err.Error())
		return c.JSON(400, res)
	}
	if answerOut.Code != 200 {
		res.Message = "商家手动完成订单失败: " + answerOut.Message
		glog.Error(res.Message, answerOut.Message)
		return c.JSON(400, res)
	}
	return c.JSON(int(answerOut.Code), answerOut)

}

// @Summary v6.3.3 查询退款单列表
// @Tags 退款单操作记录
// @Param order_sn  query string true "父单号"
// @Param refund_state  query int false "退款单状态"
// @Produce json
// @Success 200 {object} oc.RefundOrderSnListResp
// @Failure 400 {object} oc.RefundOrderSnListResp
// @Router /boss/refund/pay/getRefundOrderSn [get]
func RefundOrderSnList(c echo.Context) error {

	order_sn := c.QueryParam("order_sn")
	refund_state := cast.ToInt32(c.QueryParam("refund_state"))

	client := oc.GetOrderServiceClient()

	vo := oc.RefundOrderSnListVo{
		OrderSn:     order_sn,
		RefundState: refund_state,
	}
	out, err := client.ROC.RefundOrderSnList(client.Ctx, &vo)
	if err != nil {
		out.Msg = "查询退款单列表异常"
		glog.Error(out.Msg, err.Error())
		return c.JSON(http.StatusBadRequest, out)
	}
	if out.Code != 200 {
		glog.Error("RefundOrderSnList：", out.Msg)
		return c.JSON(http.StatusBadRequest, out)
	}

	return c.JSON(http.StatusOK, out)
}
