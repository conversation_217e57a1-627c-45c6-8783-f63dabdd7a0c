package controller

import (
	"_/models"
	"_/proto/dac"
	"_/proto/ic"
	"_/proto/oc"
	"_/proto/sv"
	"_/utils"
	"bytes"
	"context"
	"crypto/tls"
	"errors"
	"io/ioutil"
	"strconv"
	"time"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"

	"net/http"
	"strings"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

const (
	OrderAppChannelRedisKey = "order-center:order:app-channel:"
	DiscountSettingKey      = "discount:setting:%d"
)

func GetChannelStoreId(channelId int, financialCode string) (AppPoiCodes string, err error) {
	// 获取美团对应的id
	db := GetDcProductDBConn()
	exist, err := db.Table("datacenter.store_relation").Select("channel_store_id").Where("finance_code=?", financialCode).Where("channel_id=?", channelId).Get(&AppPoiCodes)
	if err != nil {
		err = errors.New("获取渠道门店id失败")
		return

	} else if !exist {
		err = errors.New("未找到美团门店失败")
		return
	}
	return
}

// 促销类型：2限时折扣,5拼团，6周期购，8预售，9新秒杀,99助力
const (
	XianshiType = 2
	GroupType   = 5
	CycleType   = 6
	BookbuyType = 8
	SeckillType = 9
	HelpsType   = 99
)

// 发送websocket消息
func SendWsMessage(message *dac.MessageCreateRequest) error {
	client := GetDataCenterClient()
	defer client.Close()

	if grpcRes, err := client.RPC.MessageCreate(client.Ctx, message); err != nil {
		return err
	} else if grpcRes.Code != 200 {
		return errors.New(grpcRes.Message)
	}

	params := &dac.MessageSendRequest{
		IsUser:   1,
		ObjectId: message.MemberMain,
		Msg:      message.Content,
	}
	if len(message.MemberMain) == 0 {
		params.IsUser = 0
		params.ObjectId = message.ShopId
	}

	if grpcRes, err := client.RPC.MessageSend(client.Ctx, params); err != nil {
		return err
	} else if grpcRes.Code != 200 {
		return errors.New(grpcRes.Message)
	}

	return nil
}

// 通过用户获取渠道门店id
func GetAppPoiCodeByUser(userNo, financialCode string, channelId int32) (appPoiCode []string) {
	var userList, financialCodeList []string
	if userNo != "" {
		userList = []string{userNo}
	}
	if financialCode != "" {
		financialCodeList = []string{financialCode}
	}

	d := GetDataCenterClient()
	defer d.Close()
	if out, err := d.RPC.QueryStoresChannelId(d.Ctx, &dac.StoreRelationUserRequest{
		UserNo:      userList,
		Psize:       2000,
		ChannelId:   channelId,
		FinanceCode: financialCodeList,
	}); err != nil {
		glog.Error(err)
	} else {
		if out.Code != 200 {
			glog.Errorf(out.Message)
		}
		for _, v := range out.Data {
			if len(v.ChannelStoreId) > 0 {
				appPoiCode = append(appPoiCode, v.ChannelStoreId)
			}
		}
	}

	return
}

func GetStockInfoBySkuCode(skuCodeInfo []*ic.SkuCodeInfo) (map[string]int32, error) {
	glog.Info("查询库存的参数信息参数：", skuCodeInfo)

	//clientIc := GetInventoryServiceClient()
	//defer clientIc.Close()
	//
	//if out, err := clientIc.RPC.GetStockInfoBySkuCode(clientIc.Ctx, &ic.GetStockInfoBySkuCodeRequest{
	//	ParamsInfo: skuCodeInfo,
	//}); err != nil {
	//	glog.Error(err)
	//	return map[string]int32{}, err
	//} else if out.Code != 200 {
	//	glog.Error(out.Message)
	//	return map[string]int32{}, errors.New(out.Message)
	//} else {
	//	return out.Result, nil
	//}
	return nil, nil
}

// 获取库存结果
func getSkuStock(stockMap map[string]int32, financeCode, skuId string) string {
	return cast.ToString(stockMap[financeCode+":"+skuId])
}

func createOrderExportTask(userNo, userName, ip, ipLocation, params string, taskContent int, operationFileUrl string, taskName string, orgId int32, financeCode string) error {
	dcClient := dac.GetDataCenterClient()

	if taskContent != 8 {
		//先查询当天是否已经有同类型在进行中的任务
		if res, err := dcClient.RPC.GetOrderExportTaskList(dcClient.Ctx, &dac.GetOrderExportTaskListRequest{
			Createtime:    time.Now().Format(kit.DATE_LAYOUT),
			TaskContent:   strconv.Itoa(taskContent),
			CreateId:      userNo,
			SearchType:    1,
			OrgId:         orgId,
			FinancialCode: financeCode,
		}); err != nil {
			glog.Error(userNo, "调用GetOrderExportTaskList失败，", err)
		} else {
			for _, v := range res.Details {
				if v.TaskStatus == 1 {
					return errors.New("有任务正在进行中，请稍后再试")
				}
			}
		}
	}

	//创建任务
	res, err := dcClient.RPC.CreateOrderExportTask(dcClient.Ctx, &dac.CreateOrderExportTaskListRequest{
		TaskContent:   int32(taskContent),
		TaskStatus:    0, //未开始
		CreateId:      userNo,
		TaskName:      taskName,
		CreateName:    userName,
		Ip:            ip,
		IpLocation:    ipLocation,
		OrgId:         orgId,
		FinancialCode: financeCode,
	})
	if err != nil {
		glog.Error(userNo, "创建任务失败，", err.Error())
		return errors.New("创建任务失败")
	}

	request := models.MqOrderExportTask{
		TaskId:           res.Id,
		UserNo:           userNo,
		TaskContent:      taskContent,
		TaskParams:       params,
		OperationFileUrl: operationFileUrl,
	}
	requestJson := kit.JsonEncode(request)
	var queue string
	//推送到MQ
	//如果类型为8则是店铺配送，
	if taskContent == 8 {
		queue = BatchTaskQueue
	} else {
		queue = OrderExportTaskQueue
	}

	glog.Info("createOrderExportTask:", requestJson)
	if ok := utils.PublishRabbitMQ(queue, requestJson, DatacenterExchange); !ok {
		glog.Error("mq推送失败，", err, "，", requestJson)
		// 失败后更新任务 task_status = 3
		_, err := dcClient.RPC.UpdateOrderExportTask(dcClient.Ctx, &dac.UpdateOrderExportTaskListRequest{
			Id:             int32(res.Id),
			TaskStatus:     3, // 失败
			ResulteFileUrl: "",
		})
		if err != nil {
			glog.Error(userNo, "更新任务失败，", err.Error())
		}
		return errors.New("创建队列任务失败")
	}
	return nil
}

// 获取门店
func getShopids(c echo.Context, channelId int32) []string {
	//渠道ID为0时默认取阿闻渠道
	if channelId == 0 {
		channelId = ChannelAwenId
	}
	if channelId > 100 {
		channelId = channelId / 100
	}

	//根据门店名称，获取财务编码
	var arrFinanceCode []string

	dacClient := dac.GetDataCenterClient()
	dacClient.Ctx = AppendToOutgoingContext(dacClient.Ctx, c)
	//dacClient := GetDataCenterClient(c)
	//defer dacClient.Close()

	var err error
	var out *dac.StoreInfoResponse

	//获取登录用户权限范围内的关联到渠道的门店ChannelId=2为美团
	if out, err = dacClient.RPC.QueryStoreInfoUserAuthority(dacClient.Ctx, &dac.StoreInfoRequest{ChannelId: channelId}); err != nil {
		glog.Error("获取用户有权限的门店id失败，", err.Error())
	} else if out.Code != 200 {
		glog.Error("获取用户有权限的门店id失败，", out.Message)
	} else {
		//glog.Info(fmt.Sprintf("获取用户有权限的门店:%s", out))
		arrFinanceCode = make([]string, len(out.Details))
		for k := range out.Details {
			arrFinanceCode[k] = out.Details[k].FinanceCode
		}
	}

	return arrFinanceCode
}

// 库存可视化列表查询校验
func stockVisualParamsCheck(warehouseId, thirdSkuId, stockNum, goodsNum, city string, skuId, isWarning int32) (*sv.WarehouseLStockistRequest, error) {
	if len(warehouseId) == 0 && len(city) == 0 {
		return nil, errors.New("请使用仓库名称查询")
	}
	var warehouseList []int32
	if len(warehouseId) > 0 {
		warehouseStr := strings.Split(warehouseId, ",")
		for _, v := range warehouseStr {
			if len(strings.TrimSpace(v)) > 0 {
				warehouseList = append(warehouseList, cast.ToInt32(strings.TrimSpace(v)))
			}
		}
	}
	var stockNumList []int32
	if len(stockNum) > 1 {
		stockNumStr := strings.Split(stockNum, ",")
		for _, v := range stockNumStr {
			if len(strings.TrimSpace(v)) > 0 {
				stockNumList = append(stockNumList, cast.ToInt32(strings.TrimSpace(v)))
			}
		}
	}
	var goodsNumList []int32
	if len(goodsNum) > 1 {
		goodsNumStr := strings.Split(goodsNum, ",")
		for _, v := range goodsNumStr {
			if len(strings.TrimSpace(v)) > 0 {
				goodsNumList = append(goodsNumList, cast.ToInt32(strings.TrimSpace(v)))
			}
		}
	}

	params := sv.WarehouseLStockistRequest{
		City:        city,
		WarehouseId: warehouseList,
		SkuId:       skuId,
		ThirdSkuId:  thirdSkuId,
		IsWarning:   isWarning,
		StockNum:    stockNumList,
		GoodsNum:    goodsNumList,
	}
	return &params, nil
}

// 效期导入文件校验
func effectiveImportCheck(url string) string {
	// 下载excel
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return "下载excel失败"
	}
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	resp, err := client.Do(req)
	if err != nil {
		return "下载excel失败"
	}
	defer resp.Body.Close()

	f, err := excelize.OpenReader(resp.Body)
	if err != nil {
		return "下载excel失败"
	}
	// 获取默认工作表名
	sheetIndex := f.GetActiveSheetIndex()
	sheetName := f.GetSheetName(sheetIndex)
	rows := f.GetRows(sheetName)

	if len(rows) <= 1 {
		return "请导入数据"
	}

	template := utils.CheckExcelTemplate(rows[0], utils.ExpiryTemplate[0])
	glog.Info("temolate：", kit.JsonEncode(rows[0]), template)
	if !template {
		return "导入格式不正确"
	}

	if len(rows) > 1002 {
		return "单次最多导入1000条数据"
	}

	return ""
}

// 根据渠道订单号(ordersn) 获取门店的storeMasterId(appChannel)
func GetAppChannelByOrderSn(ordersn string) (int32, error) {
	if len(ordersn) <= 0 {
		return 0, errors.New("ordersn 不合法")
	}
	redisConn := GetRedisConn()
	key := OrderAppChannelRedisKey + ordersn
	appChannel := redisConn.Get(key).Val()
	//如果没有数据 去orderCenter查询db获取
	if len(appChannel) <= 0 {
		clientOrderCenter := oc.GetOrderServiceClient()
		defer clientOrderCenter.Close()
		request := &oc.QueryAppChannelByOrderSnReq{OrderSn: ordersn}
		outQuery, err := clientOrderCenter.RPC.QueryAppChannelByOrderSn(clientOrderCenter.Ctx, request)
		if err != nil {
			glog.Error("QueryAppChannelByOrderSn rpc err", err, "，请求参数：", kit.JsonEncode(request))
			return 0, err
		}
		if outQuery.Code != 200 {
			glog.Error("QueryAppChannelByOrderSn rpc failed", err, "，请求参数：", kit.JsonEncode(request))
			return 0, err
		}

		return outQuery.AppChannel, nil
	}
	return cast.ToInt32(appChannel), nil
}

const storeAppChannelRedisKey = "datacenter:store:app-channel:"

// GetAppChannelByFinanceCode
// 根据门店财务编码financeCode获取门店的appChannel
// 1:首先从缓存中获取
// 2:如果缓存中没有数据则从数据库中查询数据并更新到缓存之后返回
func GetAppChannelByFinanceCode(financeCode string) (int32, error) {
	if len(financeCode) <= 0 {
		glog.Info("GetAppChannelByFinanceCode param error", "，财务编码", financeCode, "，调用函数：", kit.RunFuncName(2))
		return 0, nil
	}
	redisConn := GetRedisConn()
	key := storeAppChannelRedisKey + financeCode
	appChannel := redisConn.Get(key).Val()
	//如果没有数据 去datacenter查询db获取 data-center查询数据之后会更新数据到缓存
	if len(appChannel) <= 0 {
		dacClient := dac.GetDataCenterClient()
		requestParam := &dac.GetAppChannelRequest{FinanceCode: financeCode}
		grpcRes, err := dacClient.RPC.GetAppChannelByFinanceCode(context.Background(), requestParam)
		if err != nil {
			glog.Error("GetAppChannelByFinanceCode rpc err", err, "，财务编码：", financeCode, "，调用函数：", kit.RunFuncName(2))
			return 0, err
		}
		if grpcRes.Code != 200 {
			glog.Info("GetAppChannelByFinanceCode rpc fail", "财务编码", financeCode, "，调用函数：", kit.RunFuncName(2))
			return 0, nil
		}
		return grpcRes.AppChannel, nil
	}
	return cast.ToInt32(appChannel), nil
}

// 获取IP所属省份城市
func GetIpAddress(ip string) string {
	var ipAddress string
	if len(ip) == 0 {
		return ipAddress
	}
	url := "http://whois.pconline.com.cn/ip.jsp?level=3&ip=" + ip
	response := utils.HttpGetUrl(url)
	if len(response) > 0 {
		response = strings.Replace(response, "\n", "", -1)
		response = strings.Replace(response, "\r", "", -1)
		I := bytes.NewReader([]byte(response))
		O := transform.NewReader(I, simplifiedchinese.GBK.NewDecoder())
		d, e := ioutil.ReadAll(O)
		if e != nil {
			return ""
		}
		ipAddress = cast.ToString(d)
	}
	return ipAddress
}

func UpdateOrderData(c echo.Context) error {

	t := cast.ToInt32(c.QueryParam("type"))

	var sn string
	if t == 1 {
		sn = c.QueryParam("order_sn")
	}

	date := c.QueryParam("from_date")

	client := oc.GetOrderServiceClient()

	ret, err := client.RPC.UpdateOrderData(client.Ctx, &oc.UpdateOrderDataRequest{
		Type:    t,
		OrderSn: sn,
		Date:    date,
	})
	if err != nil {
		return c.JSON(400, err.Error())
	}
	return c.JSON(200, ret)
}

// 记录周期购活动编辑操作
func AddActivityOptLog(aid, acType, logType int32, before, after, desc, OpterId, Opter string) error {
	data := models.ActivityOptLog{
		Aid:          aid,
		ActivityType: acType,
		LogType:      logType,
		Before:       before,
		After:        after,
		OpterId:      OpterId,
		Opter:        Opter,
		Reason:       desc,
	}

	if _, err := GetDcActivityDBConn().Insert(&data); err != nil {
		glog.Errorf("AddActivityOptLog 入参：%s,%s,%s,%s,err:%#v\n", before, after, OpterId, Opter, err.Error())
		return err
	}
	return nil
}
