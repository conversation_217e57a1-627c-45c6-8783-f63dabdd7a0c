package utils

import (
	"reflect"
	"testing"

	"github.com/dgrijalva/jwt-go"
)

func TestParseAndGetPayload(t *testing.T) {
	type args struct {
		tokenStr string
	}
	tests := []struct {
		name    string
		args    args
		want    jwt.MapClaims
		wantErr bool
	}{
		{
			name: "",
			args: args{
				tokenStr: "***********************************************************************************************************************************************************************************************************************************************************************************************************************",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseAndGetPayload(tt.args.tokenStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseAndGetPayload() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseAndGetPayload() = %v, want %v", got, tt.want)
			}
		})
	}
}
