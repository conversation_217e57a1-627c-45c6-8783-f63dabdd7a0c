package controller

import (
	"_/dto"
	"_/proto/dac"
	"_/proto/igc"
	"_/utils"
	"bytes"
	"encoding/json"
	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/tricobbler/rp-kit/cast"
	"io"
)

// @Summary 积分商品列表
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param model query igc.GoodsListRequest true " "
// @Success 200 {object} igc.GoodsListResponse
// @Failure 400 {object} igc.GoodsResponse
// @Router /boss/integral-store/goods [GET]
func ISGoods(c echo.Context) error {
	req := &igc.GoodsListRequest{
		Show:      c.QueryParam("show"),
		Commend:   c.QueryParam("commend"),
		Type:      c.<PERSON>m("type"),
		Name:      c.Que<PERSON>("name"),
		PageSize:  cast.ToInt32(c.<PERSON><PERSON>m("page_size")),
		PageIndex: cast.ToInt32(c.Query<PERSON>aram("page_index")),
	}

	client := igc.GetIntegralServiceClient()
	if out, err := client.IG.List(client.Ctx, req); err != nil {
		return c.JSON(400, &igc.GoodsResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分商品详情
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param id query int true "积分礼品Id"
// @Success 200 {object} igc.GoodsDetailResponse
// @Failure 400 {object} igc.GoodsResponse
// @Router /boss/integral-store/goods/detail [GET]
func ISGoodsDetail(c echo.Context) error {
	req := &igc.GoodsIdRequest{
		Id: cast.ToInt32(c.QueryParam("id")),
	}

	client := igc.GetIntegralServiceClient()
	if out, err := client.IG.Detail(client.Ctx, req); err != nil {
		return c.JSON(400, &igc.GoodsResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary micro-charity 积分商品编辑或增加
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param model body igc.GoodsDetail true " "
// @Success 200 {object} igc.GoodsResponse
// @Failure 400 {object} igc.GoodsResponse
// @Router /boss/integral-store/goods [POST]
func ISGoodsStore(c echo.Context) error {
	req := new(igc.GoodsDetail)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &igc.GoodsResponse{Code: 400, Message: err.Error()})
	}

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	req.OrgId = cast.ToInt32(orgId)

	client := igc.GetIntegralServiceClient()
	if out, err := client.IG.Store(client.Ctx, req); err != nil {
		return c.JSON(400, &igc.GoodsResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分商品部分更新（上下架、推荐）
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param id body int true "积分商品id"
// @Param show body int false "上架 0表示下架 1表示上架，不更新不要传"
// @Param commend body int false "推荐 0'不推荐 1表示推荐，不更新不要传"
// @Success 200 {object} igc.GoodsResponse
// @Failure 400 {object} igc.GoodsResponse
// @Router /boss/integral-store/goods [Patch]
func ISGoodsPatch(c echo.Context) error {

	req := new(igc.GoodsPatchRequest)

	var params map[string]interface{}
	if err := json.NewDecoder(c.Request().Body).Decode(&params); err != nil {
		return c.JSON(400, &igc.GoodsResponse{Code: 400, Message: "解析参数错误 " + err.Error()})
	}

	for k, v := range params {
		switch k {
		case "id":
			req.Id = cast.ToInt32(v)
		case "show":
			req.Show = &wrappers.Int32Value{
				Value: cast.ToInt32(v),
			}
		case "commend":
			req.Commend = &wrappers.Int32Value{
				Value: cast.ToInt32(v),
			}
		}
	}

	client := igc.GetIntegralServiceClient()
	if out, err := client.IG.Patch(client.Ctx, req); err != nil {
		return c.JSON(400, &igc.GoodsResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分订单列表
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param model query igc.OrderListRequest true " "
// @Success 200 {object} igc.OrderListResponse
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order/list [GET]
func ISOrders(c echo.Context) error {
	req := &igc.OrderListRequest{
		OrderSn:   c.QueryParam("order_sn"),
		Mobile:    c.QueryParam("mobile"),
		StartDate: c.QueryParam("start_date"),
		EndDate:   c.QueryParam("end_date"),
		State:     c.QueryParam("state"),
		Type:      c.QueryParam("type"),
		From:      c.QueryParam("from"),
		Channel:   c.QueryParam("channel"),
		GoodsName: c.QueryParam("goods_name"),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
	}

	client := igc.GetIntegralServiceClient()
	if out, err := client.IO.List(client.Ctx, req); err != nil {
		return c.JSON(400, &igc.GoodsResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分订单详情
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param model query igc.OrderRequest true " "
// @Success 200 {object} igc.OrderDetailResponse
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order/detail [GET]
func ISOrderDetail(c echo.Context) error {
	req := &igc.OrderRequest{
		OrderSn: c.QueryParam("order_sn"),
	}

	client := igc.GetIntegralServiceClient()
	if out, err := client.IO.Detail(client.Ctx, req); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分订单 快递公司列表
// @Tags 积分商城
// @Accept json
// @Produce json
// @Success 200 {object} igc.ExpressCompaniesResponse
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order-express/company-list [GET]
func ISExpressCompanyList(c echo.Context) error {
	client := igc.GetIntegralServiceClient()
	if out, err := client.IO.ExpressCompanies(client.Ctx, &igc.OrderEmptyRequest{}); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分订单 发货、物流编辑
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param model body igc.ExpressStoreRequest true " "
// @Success 200 {object} igc.OrderResponse
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order/express [POST]
func ISExpressStore(c echo.Context) error {
	req := new(igc.ExpressStoreRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	}

	client := igc.GetIntegralServiceClient()
	if out, err := client.IO.ExpressStore(client.Ctx, req); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分订单 发货单号导入模板（文件）
// @Tags 积分商城
// @Accept json
// @Produce octet-stream
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order-express/import-template [get]
func ISExpressImportTemplate(c echo.Context) error {
	client := igc.GetIntegralServiceClient()
	if out, err := client.IO.ExpressImportTemplate(client.Ctx, &igc.OrderEmptyRequest{}); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	} else {
		if out.Code == 200 {
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename=导入模板.xlsx")
			return c.Blob(200, echo.MIMEOctetStream, out.Template)
		}
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: out.Message})
	}
}

// @Summary 积分订单 发货单号导入
// @Tags 积分商城
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "上传的文件 必须是.xlsx文件"
// @Success 200 {object} igc.OrderResponse
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order-express/import [POST]
func ISExpressImport(c echo.Context) error {
	req := new(igc.ExpressImportRequest)
	file, _, err := c.Request().FormFile("file")
	if err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: "获取文件出错 " + err.Error()})
	}
	defer file.Close()

	buf := bytes.NewBuffer(nil)
	if _, err := io.Copy(buf, file); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: "复制文件出错 " + err.Error()})
	}
	req.File = buf.Bytes()

	client := igc.GetIntegralServiceClient()
	ctx, err := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	}

	if out, err := client.IO.ExpressImport(ctx, req); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分订单 批量发货导入历史
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param model query igc.ExpressImportListRequest true " "
// @Success 200 {object} igc.ExpressImportListResponse
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order-express/import/list [GET]
func ISExpressImportList(c echo.Context) error {
	req := &igc.ExpressImportListRequest{
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
	}

	client := igc.GetIntegralServiceClient()
	ctx, err := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	}

	if out, err := client.IO.ExpressImportList(ctx, req); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分订单导出
// @Tags 积分商城
// @Accept json
// @Produce json
// @Param model query igc.OrderListRequest true " "
// @Success 200 {object} igc.OrderResponse
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order/export [get]
func ISOrderExport(c echo.Context) error {
	req := &igc.OrderListRequest{
		OrderSn:   c.QueryParam("order_sn"),
		Mobile:    c.QueryParam("mobile"),
		StartDate: c.QueryParam("start_date"),
		EndDate:   c.QueryParam("end_date"),
		State:     c.QueryParam("state"),
		Type:      c.QueryParam("type"),
		From:      c.QueryParam("from"),
		Channel:   c.QueryParam("channel"),
		GoodsName: c.QueryParam("goods_name"),
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
	}

	client := igc.GetIntegralServiceClient()
	ctx, err := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	}

	if out, err := client.IO.Export(ctx, req); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 积分订单导出结果
// @Tags 积分商城
// @Param model query igc.OrderExportListRequest true " "
// @Success 200 {object} igc.OrderExportListResponse
// @Failure 400 {object} igc.OrderResponse
// @Router /boss/integral-store/order/export/list [get]
func ISOrderExportList(c echo.Context) error {
	req := &igc.OrderExportListRequest{
		PageSize:  cast.ToInt32(c.QueryParam("page_size")),
		PageIndex: cast.ToInt32(c.QueryParam("page_index")),
	}

	client := igc.GetIntegralServiceClient()
	ctx, err := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	}

	if out, err := client.IO.ExportList(ctx, req); err != nil {
		return c.JSON(400, &igc.OrderResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary micro-charity 获取会员等级列表
// @Tags 积分商城
// @Accept json
// @Produce json
// @Success 200 {object} dto.CommonPageHttpResponse
// @Failure 400 {object} dto.CommonPageHttpResponse
// @Router /boss/integral-store/member/lever [get]
func MemberLever(c echo.Context) error {

	var res = &dto.CommonPageHttpResponse{}

	client := GetDataCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	out, err := client.RPC.MemberLeverList(client.Ctx, &dac.EmptyParam{})
	if err != nil {
		res.Message = "获取会员等级信息失败"
		glog.Error(res.Message, " ", err.Error())
		return c.JSON(400, res)
	}
	if out.Code != 200 {
		res.Message = out.Message
		glog.Error(res.Message)
		return c.JSON(400, res)
	}

	res.Data = out.Data
	return c.JSON(200, res)
}
