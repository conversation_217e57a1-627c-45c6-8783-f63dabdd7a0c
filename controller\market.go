package controller

import (
	mk "_/proto/mk"
	"bytes"
	"strconv"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// @Summary 助力活动列表
// @Tags 运营中心
// @Accept plain
// @Produce json
// @Param model query mk.ActivityIdsRequest true " "
// @Success 200 {object} mk.ActivityIdsResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/market/activity/list [get]
func QueryActivity(c echo.Context) error {
	var model mk.ActivityIdsRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.QueryActivityIds(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除活动
// @Tags 运营中心
// @Accept plain
// @Produce json
// @Param id query string true "活动ID"
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/market/activity/del [get]
func DelActivity(c echo.Context) error {
	var model mk.IdRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.DelActivity(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动状态更新
// @Tags 运营中心
// @Accept plain
// @Produce json
// @Param id query string true "活动ID"
// @Param state query string true "活动状态 1-启用 2-禁用"
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/market/activity/status-refresh [get]
func StatusRefreshActivity(c echo.Context) error {
	var model mk.StatusFreshActivityRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	model.Operator = c.Request().Header.Get("userRealName")
	if out, err := client.Market.StatusFreshActivity(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动新增编辑
// @Tags 运营中心
// @Accept json
// @Produce plain
// @Param product body mk.NewActivityRequest true " "
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/market/activity/new [post]
func NewActivity(c echo.Context) error {
	var model mk.NewActivityRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.OrgId = cast.ToInt32(orgId)

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	model.MaCreater = c.Request().Header.Get("userRealName")
	if out, err := client.Market.NewActivity(client.Ctx, &model); err != nil {
		return c.JSON(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动配置查询
// @Tags 运营中心
// @Accept json
// @Produce plain
// @Param ma_setting_key query string true "配置键值"
// @Success 200 {object} mk.SettingActivityResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/market/activity/setting/query [get]
func QueryActivitySetting(c echo.Context) error {
	var model mk.QuerySettingActivityRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.QuerySettingActivity(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动配置编辑
// @Tags 运营中心
// @Accept json
// @Produce plain
// @Param ma_day_max query string true "配置值"
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/seckill/setting/save [post]
func SaveActivitySetting(c echo.Context) error {
	var model mk.SettingActivityRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.NewSettingActivity(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 优惠券新增编辑
// @Tags 运营中心
// @Accept json
// @Produce plain
// @Param product body mk.CouponActivityNewRequest true " "
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/market/activity/coupon/new [post]
func NewCouponActivity(c echo.Context) error {
	var model mk.CouponActivityNewRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	model.CaCreater = c.Request().Header.Get("userRealName")
	if out, err := client.Market.CouponActivityNew(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 优惠券活动列表
// @Tags 运营中心
// @Accept plain
// @Produce json
// @Param id query string false "活动ID"
// @Param coupon_type query string false "优惠券类型"
// @Param coupon_scene query string false "发券场景"
// @Param businessType query string false "业务类型"
// @Param state query string false "启用状态：1-未开始 2-已开始 3-已结束 4-已终止"
// @Param stime query string false "开始时间"
// @Param etime query string false "结束时间"
// @Param channel query string false "适用渠道"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} mk.CouponListResponse
// @Failure 400 {object} mk.CouponListResponse
// @Router /boss/market/activity/coupon/list [get]
func CouponList(c echo.Context) error {
	var model mk.CouponListRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.CouponListResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.CouponActivityList(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 终止优惠券活动
// @Tags 运营中心
// @Accept plain
// @Produce json
// @Param id query string false "活动ID"
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/market/activity/coupon/end [get]
func EndCouponActivity(c echo.Context) error {
	var model mk.IdRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.EndCouponActivity(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 领取优惠券列表
// @Tags 运营中心
// @Accept plain
// @Produce json
// @Param username query string false "用户名"
// @Param mobile query int false "手机号"
// @Param type query int false "券类型：1-商城券"
// @Param stime query string false "开始时间"
// @Param etime query string false "结束时间"
// @Param couponid query string false "优惠券ID"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} mk.VoucherListResponse
// @Failure 400 {object} mk.VoucherListResponse
// @Router /boss/market/activity/voucher/list [get]
func VoucherList(c echo.Context) error {
	var model mk.VoucherListRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.VoucherListResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.VoucherList(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 领取优惠券列表导出
// @Tags 运营中心
// @Accept plain
// @Produce json
// @Param username query string false "用户名"
// @Param mobile query int false "手机号"
// @Param type query int false "券类型：1-商城券"
// @Param stime query string false "开始时间"
// @Param etime query string false "结束时间"
// @Param couponid query string false "优惠券ID"
// @Param page_index query int true "分页索引"
// @Param page_size query int true "分页大小"
// @Success 200 {object} mk.VoucherListResponse
// @Failure 400 {object} mk.VoucherListResponse
// @Router /boss/market/activity/voucher/export [get]
func VoucherListExport(c echo.Context) error {
	var model mk.VoucherListRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.VoucherListResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.VoucherList(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		if len(out.Details) > 0 {
			f := excelize.NewFile()
			header := []string{"UID", "用户名", "手机号", "领券时间", "券类型", "券ID"}
			for i := 0; i < len(header); i++ {
				f.SetCellValue("Sheet1", cast.ToString(65+i)+"1", header[i])
			}
			for i := 0; i < len(out.Details); i++ {
				v := out.Details[i]
				f.SetCellValue("Sheet1", "A"+strconv.Itoa(i+2), v.VoucherId)         //UID
				f.SetCellValue("Sheet1", "B"+strconv.Itoa(i+2), v.MemberName)        //用户名
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(i+2), v.MemberMobile)      //手机号
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(i+2), v.VoucherActiveDate) //领券时间
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(i+2), v.VoucherFrom)       //券类型
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(i+2), v.VoucherTId)        //券ID
			}
			f.Save()
			var buff bytes.Buffer
			if err = f.Write(&buff); err != nil {
				return c.JSON(200, &mk.VoucherListResponse{Code: 400, Message: "导出文件失败"})
			}
			fileName := "voucher-list -" + time.Now().Format("20060102150405") + ".xlsx"
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
			return c.Stream(200, echo.MIMEOctetStream, bytes.NewReader(buff.Bytes()))
		}
		return c.JSON(int(out.Code), out)

	}
}

// @Summary 增加优惠券领取数量
// @Tags 运营中心
// @Accept plain
// @Produce json
// @Param id query string true "优惠券ID"
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/market/activity/voucher/add [post]
func AddCouponCount(c echo.Context) error {
	var model mk.IdRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.AddCouponCount(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}
}
