package tests

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAllPages(t *testing.T) {
	baseURL := "http://127.0.0.1:11001"

	var tests = []struct {
		method   string
		url      string
		expected int
	}{
		{"POST", "/boss/market/setting/save", 200},
	}

	for _, test := range tests {
		t.Logf("当前请求URL: %v \n", test.url)
		var (
			resp *http.Response
			err  error
		)
		// 2.1 请求以获取响应
		switch {
		case test.method == "POST":
			data := make(map[string][]string)
			data["type"] = []string{"1"}
			resp, err = http.PostForm(baseURL+test.url, data)
		default:
			resp, err = http.Get(baseURL + test.url)
		}
		// 2.2 断言
		assert.NoError(t, err, "请求 "+test.url+"时报错")
		assert.Equal(t, test.expected, resp.StatusCode, test.url+" 应返回状态"+strconv.Itoa(test.expected))
	}
}
