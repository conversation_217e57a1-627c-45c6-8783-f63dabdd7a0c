package controller

import (
	"_/models"
	"_/proto/cc"
	"_/utils"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

func CallPetTipsGrpc(c echo.Context, request interface{}, grpc func(grpc *cc.Client) (response interface{}, err error)) error {
	// 400 错误代码返回
	var badResponse = new(models.BaseResponseV2)
	badResponse.Code = http.StatusBadRequest

	//获取请求参数到实体对象
	if request != nil {
		err := c.Bind(request)
		if err != nil {
			glog.Error(err)
			badResponse.Message = err.Error()
			return c.JSON(http.StatusBadRequest, badResponse)
		}
	}

	// 获取grpc 链接
	var conn = cc.GetCustomerCenterClient()
	// 关闭链接
	defer conn.Close()
	res, err := grpc(conn)
	if err != nil {
		glog.Error(err)
	}
	return c.<PERSON>SO<PERSON>(http.StatusOK, res)

}

// AddPetTips
// @Summary 宠物贴士新增
// @Tags 宠物贴士
// @Accept json
// @Produce json
// @param request body models.PetTipsAddRequest true "查询参数组合"
// @Success 200 {object} models.BaseResponseV2
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/pettips/add [post]
func AddPetTips(c echo.Context) error {
	var request = new(models.PetTipsAddRequest)
	var response = new(models.BaseResponseV2)

	// 调用grpc
	err := CallPetTipsGrpc(c, request, func(grpc *cc.Client) (interface{}, error) {
		// 映射模型
		var grpcRequest = new(cc.PetTipsEditRequest)
		grpcRequest.Title = request.Title
		grpcRequest.Icon = request.Icon
		grpcRequest.Content = request.Content
		grpcRequest.UserNo = utils.GetCurrentUser(c).UserNo
		// 映射标签
		for _, tag := range request.Tags {
			grpcRequest.Tags = append(grpcRequest.Tags, &cc.PetTipsTagDto{Name: tag.TagName, Value: tag.TagValues})
		}
		//保存
		res, err := grpc.PetTips.Add(grpc.Ctx, grpcRequest)

		if err != nil {
			response.Code = 400
			response.Message = err.Error()
		}

		if res != nil {
			//响应
			response.Code = res.Code
			response.Message = res.Message
		}

		return response, err

	})
	return err
}

// UpdatePetTips
// @Summary 宠物贴士修改
// @Tags 宠物贴士
// @Accept json
// @Produce json
// @param request body models.PetTipsUpdateRequest true "查询参数组合"
// @Success 200 {object} models.BaseResponseV2
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/pettips/update [post]
func UpdatePetTips(c echo.Context) error {
	var request = new(models.PetTipsUpdateRequest)
	var response = new(models.BaseResponseV2)

	//调用grpc
	err := CallPetTipsGrpc(c, request, func(grpc *cc.Client) (interface{}, error) {
		// 映射模型
		var grpcRequest = new(cc.PetTipsEditRequest)
		grpcRequest.Id = int32(request.Id)
		grpcRequest.Title = request.Title
		grpcRequest.Icon = request.Icon
		grpcRequest.Content = request.Content
		grpcRequest.UserNo = utils.GetCurrentUser(c).UserNo
		// 映射标签
		for _, tag := range request.Tags {
			grpcRequest.Tags = append(grpcRequest.Tags, &cc.PetTipsTagDto{Name: tag.TagName, Value: tag.TagValues})
		}
		//保存
		res, err := grpc.PetTips.Update(grpc.Ctx, grpcRequest)

		if err != nil {
			response.Code = 400
			response.Message = err.Error()
		}

		if res != nil {
			response.Code = res.Code
			response.Message = res.Message
		}

		return response, err

	})
	return err
}

// DeletePetTips
// @Summary 宠物贴士删除
// @Tags 宠物贴士
// @Accept json
// @Produce json
// @param request body models.PetTipsDeleteRequest true "查询参数组合"
// @Success 200 {object} models.BaseResponseV2
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/pettips/delete [post]
func DeletePetTips(c echo.Context) error {
	var request = new(models.PetTipsDeleteRequest)
	var response = new(models.BaseResponseV2)

	err := CallPetTipsGrpc(c, request, func(grpc *cc.Client) (interface{}, error) {
		//查询参数
		var grpcRequest = new(cc.PetTipsDeleteRequest)
		grpcRequest.Id = int32(request.Id)
		grpcRequest.Ids = request.Ids
		// 删除
		res, err := grpc.PetTips.Delete(grpc.Ctx, grpcRequest)

		if err != nil {
			response.Code = 400
			response.Message = err.Error()
		}

		if res != nil {
			response.Code = res.Code
			response.Message = res.Message
		}

		return response, err

	})
	return err
}

// QueryListPetTips
// @Summary 宠物贴士查询
// @Tags 宠物贴士
// @Accept json
// @Produce json
// @param request query models.PetTipsListQueryRequest true "查询参数组合"
// @Success 200 {object} models.PetTipsListResponse
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/pettips/query [get]
func QueryListPetTips(c echo.Context) error {
	var request = new(models.PetTipsListQueryRequest)
	var response = &models.PetTipsListResponse{}
	// 调用grpc
	err := CallPetTipsGrpc(c, request, func(grpc *cc.Client) (interface{}, error) {
		// grpc 请求
		var grpcRequest = new(cc.PetTipsQueryRequest)
		grpcRequest.SearchKey = request.SearchKey
		grpcRequest.Id = int32(request.Id)
		grpcRequest.PageIndex = request.PageIndex
		grpcRequest.PageSize = request.PageSize

		res, err := grpc.PetTips.Query(grpc.Ctx, grpcRequest)

		if err != nil {
			response.Code = 400
			response.Message = err.Error()
		}

		if res != nil {
			response.Code = res.Code
			response.Message = res.Message

			// 数据响应
			response.Total = int64(res.Total)
			for _, data := range res.Data {
				var dto = new(models.PetTipsDetailListDto)
				dto.Id = int(data.Id)
				dto.Title = data.Title
				dto.CreateAt = data.CreateAt
				response.Data = append(response.Data, dto)
			}
		}

		return response, err
	})
	return err
}

// GetPetTips
// @Summary 查询宠物贴士详情
// @Tags 宠物贴士
// @Accept json
// @Produce json
// @param id query int true "贴士Id"
// @Success 200 {object} models.PetTipsGetResponse
// @Failure 400 {object} models.BaseResponseV2
// @Router /boss/pettips/get [get]
func GetPetTips(c echo.Context) error {
	var request = new(models.IdRequest)
	var response = new(models.PetTipsGetResponse)

	err := CallPetTipsGrpc(c, request, func(grpc *cc.Client) (interface{}, error) {
		// 取参数
		var grpcRequest = new(cc.PetTipsGetRequest)
		grpcRequest.Id = int32(request.Id)
		// 查询
		res, err := grpc.PetTips.Get(grpc.Ctx, grpcRequest)

		response.Code = res.Code
		response.Message = res.Message
		// 再映射数据关联
		if res.Data != nil {
			var data = new(models.PetTipsDetailDto)
			data.Id = int(res.Data.Id)
			data.Icon = res.Data.Icon
			data.Title = res.Data.Title
			data.Reading = int(res.Data.Reading)
			data.Content = res.Data.Content
			data.CreateAt = res.Data.CreateAt
			data.CreateBy = res.Data.CreateBy
			for _, tag := range res.Data.Tags {
				data.Tags = append(data.Tags, &models.PetTipsTagDto{TagName: tag.Name, TagValues: tag.Value})
			}
			response.Data = data
		}

		return response, err
	})
	return err
}
