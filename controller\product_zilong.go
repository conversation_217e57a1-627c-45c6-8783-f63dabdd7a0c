package controller

import (
	"_/proto/pc"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// @Summary 下架子龙无效(已停用或不存在)商品
// @Tags 数据管理工具
// @Accept plain
// @Produce json
// @Param excelUrl query string true "上传后Excel文件链接"
// @Success 200 {object} pc.BindShopWarehouseResponse
// @Failure 400 {object} pc.BindShopWarehouseResponse
// @Router /boss/product/offshelf-zilong-invalid-product [get]
func OffshelfZilongInvalidProduct(c echo.Context) error {
	excelUrl := c.FormValue("excelUrl")

	client := GetDcProductClient(c)
	defer client.Close()
	resp, err := client.RPC.OffshelfZilongInvalidProduct(client.Ctx, &pc.OffshelfZilongInvalidProductRequest{
		ExcelUrl: excelUrl,
	})
	if err != nil {
		glog.Errorf("OffshelfZilongInvalidProduct 下架子龙无效商品异常:%+v", err)
		resp = &pc.OffshelfZilongInvalidProductResponse{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
		}
		return c.JSON(int(resp.Code), resp)
	}
	return c.JSON(int(resp.Code), resp)
}
