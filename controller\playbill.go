package controller

import (
	"_/models"
	"_/proto/mk"
	"encoding/json"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
)

// @Summary 创建海报
// @Tags 海报管理
// @Param Playbill body models.Playbill true " "
// @Success 200 {object} pc.BaseResponse
// @Failure 400 {object} pc.BaseResponse
// @Router /Promotion/playbill/create [post]
func CreatePlayBill(c echo.Context) error {
	out := models.BaseResponse{Code: 400}
	var playbill models.Playbill
	if err := c.Bind(&playbill); err != nil {
		out.Error = err.Error()
		out.Msg = err.Error()
		return c.JSON(400, out)
	}
	//参数判断
	if len(playbill.FinanceCode) == 0 {
		out.Error = "财务编码不能为空"
		out.Msg = out.Error
		return c.JSON(400, out)
	}
	if len(playbill.Playbillname) == 0 {
		out.Error = "海报名称不能为空"
		out.Msg = out.Error
		return c.JSO<PERSON>(400, out)
	}
	if len(playbill.Playbillcontent) == 0 {
		out.Error = "海报内容不能为空"
		out.Msg = out.Error
		return c.JSON(400, out)
	}
	if playbill.Template == 0 {
		out.Error = "海报模板不能为空"
		out.Msg = out.Error
		return c.JSON(400, out)
	}
	if len(playbill.Templateimg) == 0 {
		out.Error = "海报模板图不能为空"
		out.Msg = out.Error
		return c.JSON(400, out)
	}
	financeCode := strings.Split(playbill.FinanceCode, ",")

	var rpc_createPlayBillRequest = &mk.CreatePlayBillRequest{
		FinanceCode:     financeCode,
		Playbillname:    playbill.Playbillname,
		Playbillcontent: playbill.Playbillcontent,
		Template:        playbill.Template,
		Templateimg:     playbill.Templateimg,
		PagePath:        playbill.PagePath,
	}
	client := GetDcMarketClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	if outRes, err := client.RPC.CreatePlayBill(client.Ctx, rpc_createPlayBillRequest); err != nil {
		out.Code = 400
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else if outRes.Code != 200 {
		out.Code = 400
		out.Error = outRes.Error
		out.Msg = outRes.Message
		return c.JSON(400, out)
	} else {
		out.Code = 200
		out.Msg = "创建成功"
		return c.JSON(200, out)
	}
}

// @Summary 海报列表
// @Tags 海报管理
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /Promotion/playbill/alllist [get]
func GetPlayBillList(c echo.Context) error {
	out := &models.BaseResponse{Code: 400}

	var rpc_getPlayBillListRequest = &mk.GetPlayBillListRequest{
		FinanceCode: "",
	}
	client := GetDcMarketClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	if outRes, err := client.RPC.GetPlayBillList(client.Ctx, rpc_getPlayBillListRequest); err != nil {
		out.Code = 400
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else if outRes.Code != 200 {
		out.Code = 400
		out.Error = outRes.Error
		out.Msg = outRes.Message
		return c.JSON(400, out)
	} else {
		out.Code = 200
		out.Msg = "获取成功"
		outDetails, _ := json.Marshal(outRes.Playbills)
		out.Details = string(outDetails)
		return c.JSON(200, out)
	}
}

// @Summary 海报列表
// @Tags 海报管理
// @Param financecode query string true "财务编码"
// @Param playbillname query string true "海报名称"
// @Param stime query string true "海报创建开始时间"
// @Param etime query string true "海报创建结束时间"
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /Promotion/playbill/singlelist [get]
func GetSinglePlayBillList(c echo.Context) error {
	out := models.BaseResponse{Code: 400}

	if len(c.QueryParam("financecode")) == 0 {
		out.Code = 400
		out.Error = "财务编码不能为空"
		out.Msg = out.Error
		return c.JSON(400, out)
	}

	var rpc_getSinglePlayBillListRequest = &mk.GetSinglePlayBillListRequest{
		FinanceCode:  c.QueryParam("financecode"),
		Playbillname: c.QueryParam("playbillname"),
		Stime:        c.QueryParam("stime"),
		Etime:        c.QueryParam("etime"),
	}
	client := GetDcMarketClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	if outRes, err := client.RPC.GetSinglePlayBillList(client.Ctx, rpc_getSinglePlayBillListRequest); err != nil {
		out.Code = 400
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else if outRes.Code != 200 {
		out.Code = 400
		out.Error = outRes.Error
		out.Msg = outRes.Message
		return c.JSON(400, out)
	} else {
		out.Code = 200
		out.Msg = "获取成功"
		outDetails, _ := json.Marshal(outRes.Playbills)
		out.Details = string(outDetails)
		return c.JSON(200, out)
	}
}

// @Summary 删除推广海报
// @Tags 海报管理
// @Accept json
// @Produce json
// @Param model body mk.DeleteSinglePlayBillRequest true " "
// @Success 200 {object} mk.BaseResponse
// @Failure 400 {object} mk.BaseResponse
// @Router /boss/Promotion/playbill/detail-delete [post]
func DeleteSinglePlayBillDetail(c echo.Context) error {
	in := &mk.DeleteSinglePlayBillRequest{}
	out := mk.BaseResponse{Code: 400}
	if err := c.Bind(in); err != nil {
		out.Code = 400
		out.Message = "参数错误"
		out.Error = err.Error()
		return c.JSON(400, out)
	}
	if len(in.Playbillid) == 0 {
		out.Code = 400
		out.Message = "海报id不能为空"
		return c.JSON(400, out)
	}

	client := GetDcMarketClient(c)
	defer client.Close()

	if rsp, err := client.RPC.DeleteSinglePlayBill(client.Ctx, in); err != nil {
		out.Code = 400
		out.Message = "删除失败"
		out.Error = err.Error()
		return c.JSON(400, out)
	} else {
		return c.JSON(int(rsp.Code), rsp)
	}
}

// @Summary 下载海报
// @Tags 海报管理
// @Param playbillid query string true "海报id"
// @Param financecode query string true "财务编码"
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /Promotion/playbill/download [get]
func DownloadPlayBill(c echo.Context) error {
	out := models.BaseResponse{Code: 400}

	if len(c.QueryParam("playbillid")) == 0 {
		out.Code = 400
		out.Error = "海报编号不能为空"
		out.Msg = out.Error
		return c.JSON(400, out)
	}

	if len(c.QueryParam("financecode")) == 0 {
		out.Code = 400
		out.Error = "财务编码不能为空"
		out.Msg = out.Error
		return c.JSON(400, out)
	}

	// 从授权中心获取微信accessToken
	token, err := authCenterClient.GetAccessToken()
	if err != nil {
		out.Code = http.StatusBadRequest
		out.Error = err.Error()
		out.Msg = "获取微信token失败"
		return c.JSON(http.StatusBadRequest, out)
	}

	var rpc_downloadPlayBillRequest = &mk.DownloadPlayBillRequest{
		Playbillid:  c.QueryParam("playbillid"),
		FinanceCode: c.QueryParam("financecode"),
		AccessToken: token,
	}
	client := GetDcMarketClient(c)
	defer client.Conn.Close()
	defer client.Cf()

	if outRes, err := client.RPC.DownloadPlayBill(client.Ctx, rpc_downloadPlayBillRequest); err != nil {
		out.Code = 400
		out.Msg = err.Error()
		return c.JSON(400, out)
	} else if outRes.Code != 200 {
		out.Code = 400
		out.Error = outRes.Error
		out.Msg = outRes.Message
		return c.JSON(400, out)
	} else {
		out.Code = 200
		out.Msg = "获取成功"
		out.Details = outRes.Playbillqrcode
		return c.JSON(200, out)
	}
}
