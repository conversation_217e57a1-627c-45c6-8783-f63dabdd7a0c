package controller

import (
	"_/models"
	"_/proto/base"
	"_/proto/dac"
	"_/utils"
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/ppkg/kit"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
	. "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc/metadata"
)

// @Summary 活动列表（注意是post，避免get参数超出限制）
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model body dac.PickupListReq true " "
// @Success 200 {object} dac.PickupListResponse
// @Failure 400 {object} dac.PickupResponse
// @Router /boss/datacenter/pickup/list [POST]
func PickupList(c echo.Context) error {
	req := new(dac.PickupListReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.List(client.Ctx, req); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 新增活动
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model body dac.PickupStoreReq true " "
// @Success 200 {object} dac.PickupResponse
// @Failure 400 {object} dac.PickupResponse
// @Router /boss/datacenter/pickups [POST]
func PickupStore(c echo.Context) error {
	req := new(dac.PickupStoreReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	}

	client := dac.GetDataCenterClient()
	if user, err := utils.GetPayloadDirectlyToInterface(c); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	} else {
		req.UserNo = user.UserNo
		req.UserName = user.UserName
	}

	if out, err := client.PU.Store(client.Ctx, req); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 删除活动
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model body dac.PickupDeleteReq true " "
// @Success 200 {object} dac.PickupResponse
// @Failure 400 {object} dac.PickupResponse
// @Router /boss/datacenter/pickup/delete [Post]
func PickupDelete(c echo.Context) error {
	req := new(dac.PickupDeleteReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	}

	client := dac.GetDataCenterClient()

	if user, err := utils.GetPayloadDirectlyToInterface(c); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	} else {
		req.UserNo = user.UserNo
		req.UserName = user.UserName
	}

	if out, err := client.PU.Delete(client.Ctx, req); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 自提站点列表
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model query dac.StationsReq false " "
// @Failure 400 {object} dac.StationsResponse
// @Router /boss/datacenter/pickup/station [get]
func PickupStations(c echo.Context) error {
	req := &dac.StationsReq{
		Name:     c.QueryParam("name"),
		Status:   c.QueryParam("status"),
		Page:     cast.ToInt32(c.QueryParam("page")),
		PageSize: cast.ToInt32(c.QueryParam("page_size")),
	}
	client := dac.GetDataCenterClient()
	if out, err := client.PU.Stations(client.Ctx, req); err == nil {
		return c.JSON(200, out)
	} else {
		return c.JSON(400, &dac.StationsResponse{Code: 400, Message: out.Message})
	}
}

// @Summary 添加或保存自提站点
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model body dac.StationStoreReq true " "
// @Failure 400 {object} dac.PickupResponse
// @Router /boss/datacenter/pickup/station [post]
func PickupStationStore(c echo.Context) error {
	req := &dac.StationStoreReq{}

	if err := c.Bind(req); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: err.Error()})
	}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.StationStore(client.Ctx, req); err == nil {
		return c.JSON(200, out)
	} else {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: out.Message})
	}
}

// @Summary 禁用或启用自提站点
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param id body int true "自提点id"
// @Param status body int false "状态 0禁用、1启用，不更新不要传"
// @Failure 400 {object} dac.PickupResponse
// @Router /boss/datacenter/pickup/station [patch]
func PickupStationPatch(c echo.Context) error {
	req := &dac.StationPatchReq{}

	var params map[string]interface{}
	if err := json.NewDecoder(c.Request().Body).Decode(&params); err != nil {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: "解析参数错误 " + err.Error()})
	}

	for k, v := range params {
		switch k {
		case "id":
			req.Id = cast.ToInt32(v)
		case "status":
			req.Status = &wrappers.Int32Value{
				Value: cast.ToInt32(v),
			}
		}
	}
	client := dac.GetDataCenterClient()
	if out, err := client.PU.StationPatch(client.Ctx, req); err == nil {
		return c.JSON(200, out)
	} else {
		return c.JSON(400, &dac.PickupResponse{Code: 400, Message: out.Message})
	}
}

// @Summary 自提站点详情
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model query dac.StationDetailReq true " "
// @Failure 400 {object} dac.StationDetailResponse
// @Router /boss/datacenter/pickup/station/detail [get]
func PickupStationDetail(c echo.Context) error {
	req := &dac.StationDetailReq{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	client := dac.GetDataCenterClient()
	if out, err := client.PU.StationDetail(client.Ctx, req); err == nil {
		return c.JSON(200, out)
	} else {
		return c.JSON(400, &dac.StationDetailResponse{Code: 400, Message: out.Message})
	}
}

// @Summary 自提点导入模板下载
// @Tags 社区团购
// @Accept json
// @Produce octet-stream
// @Param model query dac.StationImportTemplateReq true " "
// @Failure 400 {object} dac.StationImportTemplateResponse
// @Router /boss/datacenter/pickup/station/import/template [get]
func PickupStationTemplate(c echo.Context) error {
	req := &dac.StationImportTemplateReq{}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.StationImportTemplate(client.Ctx, req); err != nil {
		return c.JSON(400, &dac.StationImportTemplateResponse{Code: 400, Message: err.Error()})
	} else {
		if out.Code == 200 {
			c.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename=自提点导入模板.xlsx")
			return c.Blob(200, echo.MIMEOctetStream, out.Template)
		}
		return c.JSON(400, &dac.StationImportTemplateResponse{Code: 400, Message: out.Message})
	}
}

// @Summary 批量导入自提点
// @Tags 社区团购
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "上传的文件"
// @Success 200 {object} dac.StationImportResponse
// @Failure 400 {object} dac.StationImportResponse
// @Router /boss/datacenter/pickup/station/import [post]
func PickupStationImport(c echo.Context) error {
	req := new(dac.StationImportReq)

	file, _, err := c.Request().FormFile("file")
	if err != nil {
		return c.JSON(400, &dac.StationImportResponse{Code: 400, Message: "获取文件出错 " + err.Error()})
	}
	defer file.Close()

	buf := bytes.NewBuffer(nil)
	if _, err := io.Copy(buf, file); err != nil {
		return c.JSON(400, &dac.StationImportResponse{Code: 400, Message: "复制文件出错 " + err.Error()})
	}
	req.File = buf.Bytes()

	client := dac.GetDataCenterClient()
	// 附加操作人信息
	ctx, err := utils.AppendUserToOutgoingContext(client.Ctx, c)
	if out, err := client.PU.StationImport(ctx, req); err != nil {
		return c.JSON(400, &dac.StationImportResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 自提点导入历史
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model query dac.StationImportHistoryListReq true " "
// @Success 200 {object} dac.StationImportHistoryListResponse
// @Failure 400 {object} dac.StationImportHistoryListResponse
// @Router /boss/datacenter/pickup/station/import/list [get]
func PickupStationImportHistory(c echo.Context) error {
	req := &dac.StationImportHistoryListReq{
		Page:     cast.ToInt32(c.QueryParam("page")),
		PageSize: cast.ToInt32(c.QueryParam("page_size")),
	}
	client := dac.GetDataCenterClient()
	if out, err := client.PU.StationImportHistories(client.Ctx, req); err != nil {
		return c.JSON(400, &dac.StationImportHistoryListResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动列表
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model body dac.GroupActivityListReq true " "
// @Success 200 {object} dac.GroupActivityListResponse
// @Failure 400 {object} dac.GroupActivityListResponse
// @Router /boss/datacenter/group-activity/list [post]
func GroupActivityList(c echo.Context) error {
	req := &dac.GroupActivityListReq{}
	res := &dac.GroupActivityListResponse{}

	if err := c.Bind(req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = err.Error()
		return c.JSON(http.StatusBadRequest, res)
	}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.GroupActivityList(client.Ctx, req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = utils.DoError(err.Error())
		return c.JSON(http.StatusBadRequest, res)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 活动详情
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model query dac.GroupActivityQueryReq true " "
// @Success 200 {object} dac.GroupActivityQueryResponse
// @Failure 400 {object} dac.GroupActivityQueryResponse
// @Router /boss/datacenter/group-activity/query [get]
func GroupActivityQuery(c echo.Context) error {
	req := &dac.GroupActivityQueryReq{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	client := dac.GetDataCenterClient()
	if out, err := client.PU.GroupActivityQuery(client.Ctx, req); err != nil {
		return c.JSON(http.StatusBadRequest, &dac.GroupActivityQueryResponse{Code: 400, Message: utils.DoError(err.Error())})
	} else {
		return c.JSON(http.StatusOK, out)
	}
}

// @Summary 活动新增编辑
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model body dac.GroupActivityEditReq true " "
// @Success 200 {object} base.BaseResponse
// @Failure 400 {object} base.BaseResponse
// @Router /boss/datacenter/group-activity/edit [post]
func GroupActivityEdit(c echo.Context) error {
	req := &dac.GroupActivityEditReq{}
	res := &base.BaseResponse{}

	if err := c.Bind(req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = err.Error()
		return c.JSON(http.StatusBadRequest, res)
	}

	req.CreatedBy = c.Request().Header.Get("userNo")
	client := dac.GetDataCenterClient()
	if _, err := client.PU.GroupActivityEdit(client.Ctx, req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = utils.DoError(err.Error())
		return c.JSON(http.StatusBadRequest, res)
	} else {
		res.Code = http.StatusOK
		return c.JSON(http.StatusOK, res)
	}
}

// @Summary 活动状态更新
// @Tags 社区团购
// @Accept json
// @Produce json
// @Param model body dac.GroupActivityStatusReq true " "
// @Success 200 {object} base.BaseResponse
// @Failure 400 {object} base.BaseResponse
// @Router /boss/datacenter/group-activity/status [post]
func GroupActivityStatus(c echo.Context) error {
	req := &dac.GroupActivityStatusReq{}
	res := &base.BaseResponse{}

	if err := c.Bind(req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = err.Error()
		return c.JSON(http.StatusBadRequest, res)
	}

	req.CreatedBy = c.Request().Header.Get("userNo")
	client := dac.GetDataCenterClient()
	if _, err := client.PU.GroupActivityStatus(client.Ctx, req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = utils.DoError(err.Error())
		return c.JSON(http.StatusBadRequest, res)
	} else {
		res.Code = http.StatusOK
		return c.JSON(http.StatusOK, res)
	}
}

// @Summary 佣金提现申请列表
// @Tags 佣金提现
// @Accept json
// @Produce json
// @Param model body dac.CommissionCashoutListReq true " "
// @Success 200 {object} dac.CommissionCashoutListResponse
// @Failure 400 {object} dac.CommissionCashoutListResponse
// @Router /boss/datacenter/commission-cashout/list [post]
func CommissionCashoutList(c echo.Context) error {
	req := &dac.CommissionCashoutListReq{}
	res := &dac.CommissionCashoutListResponse{}

	if err := c.Bind(req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = err.Error()
		return c.JSON(http.StatusBadRequest, res)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		res.Code = http.StatusBadRequest
		res.Message = err.Error()
		return c.JSON(http.StatusBadRequest, res)
	}

	if userInfo.FinancialCode != "" {
		res.Code = http.StatusBadRequest
		res.Message = "没有权限查看"
		return c.JSON(http.StatusBadRequest, res)
	}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.CommissionCashoutList(client.Ctx, req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = utils.DoError(err.Error())
		return c.JSON(http.StatusBadRequest, res)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 佣金提现申请列表详情
// @Tags 佣金提现
// @Accept json
// @Produce json
// @Param model body dac.CommissionCashoutQueryReq true " "
// @Success 200 {object} dac.CommissionCashoutQueryResponse
// @Failure 400 {object} dac.CommissionCashoutQueryResponse
// @Router /boss/datacenter/commission-cashout/query [get]
func CommissionCashoutQuery(c echo.Context) error {
	req := &dac.CommissionCashoutQueryReq{
		Id: cast.ToInt32(c.QueryParam("id")),
	}
	res := &dac.CommissionCashoutQueryResponse{}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.CommissionCashoutQuery(client.Ctx, req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = utils.DoError(err.Error())
		return c.JSON(http.StatusBadRequest, res)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 佣金提现申请列表审核
// @Tags 佣金提现
// @Accept json
// @Produce json
// @Param model body dac.CommissionCashoutAuditReq true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/datacenter/commission-cashout/audit [post]
func CommissionCashoutAudit(c echo.Context) error {
	req := &dac.CommissionCashoutAuditReq{}
	res := &base.BaseResponse{}

	if err := c.Bind(req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = err.Error()
		return c.JSON(http.StatusBadRequest, res)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}

	client := dac.GetDataCenterClient()
	client.Ctx = metadata.AppendToOutgoingContext(client.Ctx,
		"user-no", userInfo.UserNo, "user-name", userInfo.UserName,
	)
	if _, err := client.PU.CommissionCashoutAudit(client.Ctx, req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = utils.DoError(err.Error())
		return c.JSON(http.StatusBadRequest, res)
	} else {
		res.Code = http.StatusOK
		return c.JSON(http.StatusOK, res)
	}
}

// @Summary 佣金提现导入已打款记录
// @Tags 佣金提现
// @Accept plain
// @Produce json
// @Param params body dac.CommissionCashoutImportReq true " "
// @Success 200 {object} dac.BaseResponse
// @Failure 400 {object} dac.BaseResponse
// @Router /boss/datacenter/commission-cashout/import [post]
func CommissionCashoutImport(c echo.Context) error {
	baseResponse := dac.BaseResponse{
		Code: http.StatusBadRequest,
	}

	// 序列化参数
	params := new(dac.CommissionCashoutImportReq)
	if err := c.Bind(params); err != nil {
		baseResponse.Message = err.Error()
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error("auth token解析失败，", err)
		return r.NewHTTPError(400, err.Error())
	}

	client := dac.GetDataCenterClient()
	client.Ctx = metadata.AppendToOutgoingContext(client.Ctx,
		"user-no", userInfo.UserNo, "user-name", userInfo.UserName,
	)
	_, err = client.PU.CommissionCashoutImport(client.Ctx, params)
	if err != nil {
		baseResponse.Message = utils.DoError(err.Error())
		return c.JSON(http.StatusBadRequest, baseResponse)
	}

	baseResponse.Code = http.StatusOK
	return c.JSON(http.StatusOK, baseResponse)
}

// @Summary 佣金提现导入已打款记录历史列表
// @Tags 佣金提现
// @Accept json
// @Produce json
// @Param model body dac.CommissionCashoutImportListReq true " "
// @Success 200 {object} dac.CommissionCashoutImportListResponse
// @Failure 400 {object} dac.CommissionCashoutImportListResponse
// @Router /boss/datacenter/commission-cashout/importlist [post]
func CommissionCashoutImportList(c echo.Context) error {
	req := &dac.CommissionCashoutImportListReq{}
	res := &dac.CommissionCashoutImportListResponse{}

	if err := c.Bind(req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = err.Error()
		return c.JSON(http.StatusBadRequest, res)
	}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.CommissionCashoutImportList(client.Ctx, req); err != nil {
		res.Code = http.StatusBadRequest
		res.Message = utils.DoError(err.Error())
		return c.JSON(http.StatusBadRequest, res)
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 提现申请导出
// @Tags 佣金提现
// @Accept json
// @Produce json
// @Param model body dac.CommissionCashoutListReq true " "
// @Success 200 {object} base.BaseResponse
// @Failure 400 {object} base.BaseResponse
// @Router /boss/datacenter/commission-cashout/export [post]
func CashOutExport(c echo.Context) error {
	model := new(dac.CommissionCashoutListReq)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	Ip := strings.Split(c.Request().RemoteAddr, ":")[0]
	IpLocation := GetIpAddress(Ip)

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	taskContent := 13
	// 宠物saas-v1.0
	UserNo := userInfo.UserNo
	if UserNo == "" {
		UserNo = userInfo.ScrmId
	}

	//创建任务
	dcClient := dac.GetDataCenterClient()
	res, err := dcClient.RPC.CreateOrderExportTask(dcClient.Ctx, &dac.CreateOrderExportTaskListRequest{
		TaskContent: int32(taskContent),
		TaskStatus:  0, //未开始
		CreateId:    UserNo,
		TaskName:    "",
		CreateName:  userInfo.UserName,
		Ip:          Ip,
		IpLocation:  IpLocation,
	})
	if err != nil {
		glog.Error(UserNo, "创建任务失败，", err.Error())
		return c.JSON(200, base.BaseResponse{
			Code:    400,
			Message: "创建任务失败",
		})
	}

	request := models.MqOrderExportTask{
		TaskId:           res.Id,
		UserNo:           UserNo,
		TaskContent:      taskContent,
		TaskParams:       JsonEncode(model),
		OperationFileUrl: "",
	}
	requestJson := kit.JsonEncode(request)
	var queue = "dc-sz-data-center-batch-task-commission"

	glog.Info("cashOutExportTask:", requestJson)
	if ok := utils.PublishRabbitMQ(queue, requestJson, DatacenterExchange); !ok {
		glog.Error("mq推送失败，", err, "，", requestJson)
		// 失败后更新任务 task_status = 3
		_, err := dcClient.RPC.UpdateOrderExportTask(dcClient.Ctx, &dac.UpdateOrderExportTaskListRequest{
			Id:             int32(res.Id),
			TaskStatus:     3, // 失败
			ResulteFileUrl: "",
		})
		if err != nil {
			glog.Error(userInfo.UserNo, "更新任务失败，", err.Error())
		}
		return c.JSON(200, base.BaseResponse{
			Code:    400,
			Message: "创建任务失败",
		})
	}

	return c.JSON(200, base.BaseResponse{
		Code:    200,
		Message: "任务创建成功",
	})
}
