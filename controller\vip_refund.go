package controller

import (
	"_/proto/oc"
	"_/utils"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"net/http"
)

// VipRefundRefuse
//@summary VIP卡退款审批
// @Tags VIP 3.0
// @Accept json
// @Produce json
// @Param oc.RefundExamineReq body oc.RefundExamineReq true "参数结构体"
// @Success 200 {object} oc.CardBaseResponse
// @Failure 400 {object} oc.CardBaseResponse
// @Router /boss/vip/refund/examine [POST]
func VipRefundExamine(c echo.Context) error {
	var (
		out = new(oc.CardBaseResponse)
		req = new(oc.RefundExamineReq)
	)
	out.Code = 400
	if err := c.Bind(req); err != nil {
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	//获取用户信息
	if claims, err := utils.GetPayloadDirectly(c); err != nil {
		glog.Error(err)
		out.Message = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	} else {
		req.UserNo = utils.InterfaceToString(claims["userno"])
		req.UserName = utils.InterfaceToString(claims["name"])
	}
	//调用订单中心审批接口
	client := oc.GetOrderServiceClient()
	if out, err := client.Card.RefundExamine(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}

	out.Code = 200
	return c.JSON(http.StatusOK, out)
}
