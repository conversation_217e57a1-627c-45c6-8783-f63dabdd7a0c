package controller

import (
	"testing"

	"github.com/labstack/echo/v4"
)

func TestExportGoodsImage(t *testing.T) {
	type args struct {
		c echo.Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ExportGoodsImage(tt.args.c); (err != nil) != tt.wantErr {
				t.Errorf("ExportGoodsImage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
