package controller

import (
	"_/proto/oc"
	"_/utils"
	"context"
	"encoding/json"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
)

// @Summary 查询异常订单列表
// @Tags 异常配送单
// @Accept plain
// @Produce json
// @Param PageSize query int true "分页大小"
// @Param Page query int true "当前页"
// @Param OrderId query string false "订单编号"
// @Param Remarks query string false "备注"
// @Param mt_order_sn query string false "美团单号"
// @Success 200 {object} oc.OrderExceptionListResponse
// @Failure 400 {object} oc.OrderExceptionListResponse
// @Router /boss/ordercenter/OrderExceptionList [GET]
func OrderExceptionList(c echo.Context) error {
	model := new(oc.OrderExceptionListRequest)
	var res oc.OrderExceptionListResponse
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		return c.JSON(200, res)
	}

	userInfo, err := utils.GetPayloadDirectlyToInterface(c)
	if err != nil {
		glog.Error(err)
		res.Message = err.Error()
		return c.JSON(400, res)
	}
	var arrFinanceCode []string

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	// 宠物saas 走这里
	if orgId == "6" {
		// validateAndGetShopIds 验证店铺权限并返回店铺ID列表
		arrFinanceCode, err = validateAndGetShopIds(userInfo, model.FinancialCode, orgId, "查询异常订单列表")
		if err != nil {
			return err
		}
	} else {
		if userInfo.FinancialCode == "" {
			arrFinanceCode = getShopids(c, model.ChannelId)
			if len(arrFinanceCode) <= 0 {
				return c.JSON(200, oc.AwenOrderListResponse{Code: 400, Message: "获取不到用户有权限的门店", Error: "获取不到用户有权限的门店"})
			}
		} else {
			arrFinanceCode = append(arrFinanceCode, userInfo.FinancialCode)
		}
	}

	model.Shopids = arrFinanceCode

	client := oc.GetOrderServiceClient()
	grpcRes, err := client.OES.OrderExceptionList(context.Background(), model)
	if err != nil || grpcRes.Code != 200 {
		return c.JSON(200, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 取消配送
// @Tags 异常配送单
// @Accept json
// @Produce json
// @Param model body oc.ExceptionOrderStatusRequest true " "
// @Success 200 {object} oc.ExceptionOrderStatusResponse
// @Failure 400 {object} oc.ExceptionOrderStatusResponse
// @Router /boss/ordercenter/OrderCancel [POST]
func OrderCancel(c echo.Context) error {

	glog.Info("取消配送")
	model := new(oc.ExceptionOrderStatusRequest)
	var res oc.ExceptionOrderStatusResponse
	if err := c.Bind(model); err != nil {
		res.Code = 400
		res.Error = err.Error()
		glog.Error("取消配送" + err.Error())
		return c.JSON(400, res)
	}

	// storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	// if err != nil {
	// 	glog.Error("OrderCancel,", "GetAppChannelByOrderSn", model.OrderId, err)
	// 	return c.JSON(http.StatusBadRequest, res)
	// }
	// model.StoreMasterId = storeMasterId

	client := oc.GetOrderServiceClient()
	grpcRes, err := client.OES.OrderCancel(context.Background(), model)
	if err != nil || grpcRes.Code != 200 {
		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("取消配送:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 再次发起配送
// @Tags 异常配送单
// @Accept json
// @Produce json
// @Param model body oc.ExceptionOrderStatusRequest true " "
// @Success 200 {object} oc.ExceptionOrderStatusResponse
// @Failure 400 {object} oc.ExceptionOrderStatusResponse
// @Router /boss/ordercenter/DistributionAgain [POST]
func DistributionAgain(c echo.Context) error {
	glog.Info("再次发起配送")
	model := new(oc.ExceptionOrderStatusRequest)
	var res oc.ExceptionOrderStatusResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	// storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	// if err != nil {
	// 	glog.Error("DistributionAgain,", "GetAppChannelByOrderSn", model.OrderId, err)
	// 	return c.JSON(http.StatusBadRequest, res)
	// }
	// model.StoreMasterId = storeMasterId

	//if model.DeliveryType != 0 {
	//	res.Code = 400
	//	res.Error = "参数错误"
	//	return c.JSON(400, res)
	//}
	client := oc.GetOrderServiceClient()
	grpcRes, err := client.OES.DistributionAgain(context.Background(), model)
	if err != nil || grpcRes.Code != 200 {
		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("再次发起配送:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 发起自配
// @Tags 异常配送单
// @Accept json
// @Produce json
// @Param model body oc.OrderExceptionRequest true " "
// @Success 200 {object} oc.OrderExceptionResponse
// @Failure 400 {object} oc.OrderExceptionResponse
// @Router /boss/ordercenter/OrderOwnDeliver [POST]
func OrderOwnDeliver(c echo.Context) error {
	glog.Info("发起自配")
	model := new(oc.OrderExceptionRequest)
	var res oc.OrderExceptionResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}
	storeMasterId, err := GetAppChannelByOrderSn(model.OrderId)
	if err != nil {
		glog.Info("OrderOwnDeliver,", "GetAppChannelByOrderSn", model.OrderId, err)
	}
	model.StoreMasterId = storeMasterId
	if model.CourierPhone == "" || len(model.CourierPhone) != 11 {
		res.Code = 400
		res.Message = "配送员手机号不正确，请填写11手机号码"
		return c.JSON(400, res)
	}

	client := oc.GetOrderServiceClient()
	grpcRes, err := client.OES.OrderOwnDeliver(context.Background(), model)
	if err != nil || grpcRes.Code != 200 {
		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("发起自配:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}

// @Summary 已送达
// @Tags 异常配送单
// @Accept json
// @Produce json
// @Param model body oc.ExceptionOrderStatusRequest true " "
// @Success 200 {object} oc.ExceptionOrderStatusResponse
// @Failure 400 {object} oc.ExceptionOrderStatusResponse
// @Router /boss/ordercenter/GoodArrive [POST]
func GoodArrive(c echo.Context) error {
	glog.Info("已送达")
	model := new(oc.ExceptionOrderStatusRequest)
	var res oc.ExceptionOrderStatusResponse
	if err := c.Bind(model); err != nil {

		res.Code = 400
		res.Error = err.Error()
		return c.JSON(400, res)
	}

	client := oc.GetOrderServiceClient()
	grpcRes, err := client.OES.GoodArrive(context.Background(), model)
	if err != nil || grpcRes.Code != 200 {
		signByte, _ := json.Marshal(grpcRes)
		signStr := string(signByte)
		glog.Info("已送达:" + signStr)
		return c.JSON(400, grpcRes)
	}
	return c.JSON(200, grpcRes)
}
