package controller

import (
	mk "_/proto/mk"
	"github.com/labstack/echo/v4"
)

// QuerySuccour returns json
func QuerySuccour(c echo.Context) error {
	var model mk.UserListRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourUser(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

//edit   user
// QueryEditUser returns json
func QueryEditUser(c echo.Context) error {
	var model mk.UserRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourEditUser(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QueryQrcode returns json
func QueryQrcode(c echo.Context) error {
	var model mk.QrcodeRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourQrcode(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QueryQrcodeAdd returns json
func QueryQrcodeAdd(c echo.Context) error {
	var model mk.QrcodeAddRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourQrcodeAdd(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QueryQrcodeSet returns json
func QueryQrcodeSet(c echo.Context) error {
	var model mk.QrcodeSetRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourQrcodeSet(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QuerySalvation returns json
func QuerySalvation(c echo.Context) error {
	var model mk.SalvationRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourSalvation(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QuerySalvationAdd returns json
func QuerySalvationAdd(c echo.Context) error {
	var model mk.SalvationAddRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourSalvationAdd(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QuerySalvationSet returns json
func QuerySalvationSet(c echo.Context) error {
	var model mk.SalvationSetRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourSalvationSet(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QuerySalvationReceiving returns json
func QuerySalvationReceiving(c echo.Context) error {
	var model mk.SalvationReceivingRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourSalvationReceiving(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QuerySalvationReceivingAdd returns json
func QuerySalvationReceivingAdd(c echo.Context) error {
	var model mk.SalvationReceivingAddRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourSalvationReceivingAdd(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QueryDonateSet returns json
func QueryDonateSet(c echo.Context) error {
	var model mk.DonateSetRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourDonateSet(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QueryQrcodeLimit returns json
func QueryQrcodeLimit(c echo.Context) error {
	var model mk.QrcodeLimitRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourQrcodeLimit(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}

// QueryQrcodeLimitEdit returns json
func QueryQrcodeLimitEdit(c echo.Context) error {
	var model mk.QrcodeLimitEditRequest
	if err := c.Bind(&model); err != nil {
		return c.JSON(400, &mk.BaseResponse{Code: 400, Message: err.Error()})
	}

	client := mk.GetMarketCenterClient()
	defer client.Conn.Close()
	defer client.Cf()

	if out, err := client.Market.SuccourQrcodeLimitEdit(client.Ctx, &model); err != nil {
		return c.JSON(400, err)
	} else {
		return c.JSON(int(out.Code), out)
	}

}
