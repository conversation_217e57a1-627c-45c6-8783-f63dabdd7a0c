name: 'Tests: `nvm install-latest-npm`'

on: [pull_request, push]

permissions:
  contents: read

jobs:
  matrix:
    runs-on: ubuntu-latest
    outputs:
      latest: ${{ steps.set-matrix.outputs.requireds }}
    steps:
      - name: <PERSON><PERSON> Runner
        uses: step-security/harden-runner@v2
        with:
          allowed-endpoints:
            iojs.org:443
            nodejs.org:443
            raw.githubusercontent.com:443
      - uses: ljharb/actions/node/matrix@main
        id: set-matrix
        with:
          versionsAsRoot: true
          type: majors
          preset: '>=1'

  nodes:
    needs: [matrix]
    permissions:
      contents: read
    name: 'nvm install-latest-npm'
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        node-version: ${{ fromJson(needs.matrix.outputs.latest) }}
        include:
          - node-version: "21"
          - node-version: "20.5"
          - node-version: "20.4"
          - node-version: "14.17"
          - node-version: "14.16"
          - node-version: "9.2"
          - node-version: "9.1"
          - node-version: "9.0"
          - node-version: "6.1"
          - node-version: "5.9"
          - node-version: "4.6"
          - node-version: "4.5"
          - node-version: "4.4"
          - node-version: "0.12"
          - node-version: "0.10"

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@v2
        with:
          allowed-endpoints:
            github.com:443
            raw.githubusercontent.com:443
            iojs.org:443
            nodejs.org:443
            registry.npmjs.org:443
      - uses: actions/checkout@v4
      - uses: ljharb/actions/node/install@main
        name: 'install node'
        with:
          node-version: ${{ matrix.node-version }}
          skip-ls-check: true
          skip-install: true
          skip-latest-npm: true
      - run: npm --version
      - run: '. ./nvm.sh ; nvm install-latest-npm'
        name: 'nvm install-latest-npm'
      - run: npm --version

  node:
    permissions:
      contents: none
    name: 'nvm install-latest-npm'
    needs: [nodes]
    runs-on: ubuntu-latest
    steps:
      - run: true
